/**
 * 内容管理页面样式
 * 专门为内容管理页面设计的样式
 */

/* 内容统计卡片 */
.content-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.content-stat-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-5);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.content-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.content-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.content-stat-card.stat-published::before {
  background: linear-gradient(90deg, var(--success-color), var(--success-hover));
}

.content-stat-card.stat-draft::before {
  background: linear-gradient(90deg, var(--warning-color), var(--warning-hover));
}

.content-stat-card.stat-archived::before {
  background: linear-gradient(90deg, var(--info-color), var(--info-hover));
}

.content-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

.content-stat-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
  line-height: 1;
}

.content-stat-label {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* 内容表格样式 */
.content-table {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
}

.content-table-header {
  padding: var(--spacing-5) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-table-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.content-table-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

/* 内容状态样式 */
.content-status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.content-status.status-published {
  background-color: var(--success-light);
  color: var(--success-color);
}

.content-status.status-draft {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.content-status.status-archived {
  background-color: var(--info-light);
  color: var(--info-color);
}

.content-status.status-disabled {
  background-color: var(--gray-200);
  color: var(--gray-600);
}

/* 内容编辑器 */
.content-editor {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

.content-editor-header {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-editor-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.content-editor-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.content-editor-body {
  padding: var(--spacing-6);
}

.content-editor-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.content-editor-row {
  display: flex;
  gap: var(--spacing-4);
}

.content-editor-col {
  flex: 1;
}

.content-editor-col-auto {
  flex: 0 0 auto;
}

/* 富文本编辑器 */
.content-rich-editor {
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.content-rich-editor-toolbar {
  padding: var(--spacing-2);
  background: var(--gray-100);
  border-bottom: var(--border-width) solid var(--border-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  flex-wrap: wrap;
}

.content-rich-editor-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background: transparent;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
}

.content-rich-editor-btn:hover {
  background-color: var(--gray-200);
  color: var(--gray-800);
}

.content-rich-editor-btn.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.content-rich-editor-divider {
  width: 1px;
  height: 24px;
  background-color: var(--border-color);
  margin: 0 var(--spacing-2);
}

.content-rich-editor-textarea {
  width: 100%;
  min-height: 300px;
  padding: var(--spacing-4);
  border: none;
  outline: none;
  resize: vertical;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
}

/* 内容预览 */
.content-preview {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

.content-preview-header {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.content-preview-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.content-preview-body {
  padding: var(--spacing-6);
  max-height: 400px;
  overflow-y: auto;
}

.content-preview-content {
  line-height: var(--line-height-relaxed);
  color: var(--gray-700);
}

.content-preview-content h1,
.content-preview-content h2,
.content-preview-content h3,
.content-preview-content h4,
.content-preview-content h5,
.content-preview-content h6 {
  color: var(--gray-800);
  margin-top: var(--spacing-4);
  margin-bottom: var(--spacing-2);
}

.content-preview-content p {
  margin-bottom: var(--spacing-3);
}

.content-preview-content ul,
.content-preview-content ol {
  margin-bottom: var(--spacing-3);
  padding-left: var(--spacing-6);
}

.content-preview-content blockquote {
  margin: var(--spacing-4) 0;
  padding: var(--spacing-3) var(--spacing-4);
  border-left: 4px solid var(--primary-color);
  background-color: var(--gray-100);
  border-radius: var(--border-radius);
}

.content-preview-content code {
  background-color: var(--gray-100);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  font-family: var(--font-family-monospace);
  font-size: var(--font-size-sm);
}

.content-preview-content pre {
  background-color: var(--gray-100);
  padding: var(--spacing-4);
  border-radius: var(--border-radius);
  overflow-x: auto;
  margin: var(--spacing-4) 0;
}

/* 内容筛选器 */
.content-filters {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-sm);
  border: var(--border-width) solid var(--border-color);
  margin-bottom: var(--spacing-6);
}

.content-filters-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.content-filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  min-width: 150px;
}

.content-filter-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
}

.content-filter-input {
  padding: var(--spacing-2) var(--spacing-3);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-fast);
}

.content-filter-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 批量操作栏 */
.content-bulk-actions {
  display: none;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-6);
  background: var(--primary-light);
  border-bottom: var(--border-width) solid var(--primary-color);
}

.content-bulk-actions.show {
  display: flex;
}

.content-bulk-info {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.content-bulk-buttons {
  display: flex;
  gap: var(--spacing-2);
}

/* 内容操作按钮 */
.content-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.content-action-btn {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius);
  border: var(--border-width) solid transparent;
  background: transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
}

.content-action-btn:hover {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

.content-action-btn.btn-view:hover {
  background-color: var(--info-light);
  color: var(--info-color);
}

.content-action-btn.btn-edit:hover {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.content-action-btn.btn-delete:hover {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-stats {
    grid-template-columns: 1fr;
  }
  
  .content-table-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }
  
  .content-table-actions {
    justify-content: center;
  }
  
  .content-filters-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .content-filter-group {
    min-width: auto;
  }
  
  .content-editor-row {
    flex-direction: column;
  }
  
  .content-bulk-actions {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .content-rich-editor-toolbar {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .content-actions {
    flex-direction: column;
    gap: var(--spacing-1);
  }
  
  .content-action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .content-editor-body {
    padding: var(--spacing-4);
  }
  
  .content-preview-body {
    padding: var(--spacing-4);
  }
  
  .content-rich-editor-textarea {
    min-height: 200px;
  }
}
