/**
 * CSS变量定义
 * 统一管理颜色、字体、间距等设计系统
 */

:root {
  /* 主色调 - 基于参考图片的蓝色系 */
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-light: #e6f7ff;
  --primary-dark: #096dd9;

  /* 辅助色调 */
  --secondary-color: #6c757d;
  --secondary-hover: #545b62;
  --secondary-light: #f8f9fa;

  /* 状态颜色 - 基于参考图片的配色 */
  --success-color: #52c41a;
  --success-hover: #73d13d;
  --success-light: #f6ffed;

  --warning-color: #faad14;
  --warning-hover: #ffc53d;
  --warning-light: #fffbe6;

  --danger-color: #ff4d4f;
  --danger-hover: #ff7875;
  --danger-light: #fff2f0;

  --error-color: #ff4d4f;  /* 别名，与danger-color相同 */

  --info-color: #1890ff;
  --info-hover: #40a9ff;
  --info-light: #e6f7ff;

  /* 紫色系 - 用于分类等特殊统计 */
  --purple-color: #722ed1;
  --purple-hover: #9254de;
  --purple-light: #f9f0ff;

  /* 中性色 */
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --black: #000000;

  /* 字体 */
  --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 间距 */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */

  /* 边框 */
  --border-width: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-color: var(--gray-300);
  --border-radius: 0.375rem;   /* 6px */
  --border-radius-sm: 0.25rem; /* 4px */
  --border-radius-lg: 0.5rem;  /* 8px */
  --border-radius-xl: 0.75rem; /* 12px */
  --border-radius-full: 9999px;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* 布局 */
  --container-max-width: 1200px;
  --sidebar-width: 240px;
  --header-height: 64px;

  /* 侧边栏 - 基于参考图片的深蓝色设计 */
  --sidebar-bg: #001529;
  --sidebar-text: #ffffff;
  --sidebar-text-secondary: rgba(255, 255, 255, 0.65);
  --sidebar-active: var(--primary-color);
  --sidebar-hover: rgba(255, 255, 255, 0.1);

  /* 内容区域 - 基于参考图片的浅色背景 */
  --content-bg: #f0f2f5;
  --card-bg: #ffffff;
  --text-primary: #262626;
  --text-secondary: #8c8c8c;

  /* 响应式断点 */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
}
