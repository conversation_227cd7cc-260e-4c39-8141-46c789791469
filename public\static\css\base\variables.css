/**
 * CSS变量定义
 * 统一管理颜色、字体、间距等设计系统
 */

:root {
  /* 主色调 */
  --primary-color: #007bff;
  --primary-hover: #0056b3;
  --primary-light: #e3f2fd;
  --primary-dark: #004085;

  /* 辅助色调 */
  --secondary-color: #6c757d;
  --secondary-hover: #545b62;
  --secondary-light: #f8f9fa;

  /* 状态颜色 */
  --success-color: #28a745;
  --success-hover: #1e7e34;
  --success-light: #d4edda;
  
  --warning-color: #ffc107;
  --warning-hover: #e0a800;
  --warning-light: #fff3cd;
  
  --danger-color: #dc3545;
  --danger-hover: #c82333;
  --danger-light: #f8d7da;

  --error-color: #dc3545;  /* 别名，与danger-color相同 */
  
  --info-color: #17a2b8;
  --info-hover: #138496;
  --info-light: #d1ecf1;

  /* 中性色 */
  --white: #ffffff;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
  --black: #000000;

  /* 字体 */
  --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 间距 */
  --spacing-0: 0;
  --spacing-1: 0.25rem;   /* 4px */
  --spacing-2: 0.5rem;    /* 8px */
  --spacing-3: 0.75rem;   /* 12px */
  --spacing-4: 1rem;      /* 16px */
  --spacing-5: 1.25rem;   /* 20px */
  --spacing-6: 1.5rem;    /* 24px */
  --spacing-8: 2rem;      /* 32px */
  --spacing-10: 2.5rem;   /* 40px */
  --spacing-12: 3rem;     /* 48px */
  --spacing-16: 4rem;     /* 64px */
  --spacing-20: 5rem;     /* 80px */

  /* 边框 */
  --border-width: 1px;
  --border-width-2: 2px;
  --border-width-4: 4px;
  --border-color: var(--gray-300);
  --border-radius: 0.375rem;   /* 6px */
  --border-radius-sm: 0.25rem; /* 4px */
  --border-radius-lg: 0.5rem;  /* 8px */
  --border-radius-xl: 0.75rem; /* 12px */
  --border-radius-full: 9999px;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;

  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;

  /* 布局 */
  --container-max-width: 1200px;
  --sidebar-width: 240px;
  --header-height: 64px;

  /* 侧边栏 */
  --sidebar-bg: #1f2937;
  --sidebar-text: #d1d5db;
  --sidebar-active: var(--primary-color);

  /* 内容区域 */
  --content-bg: #f8fafc;
  --card-bg: #ffffff;
  --text-primary: #111827;
  --text-secondary: #6b7280;

  /* 响应式断点 */
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
}
