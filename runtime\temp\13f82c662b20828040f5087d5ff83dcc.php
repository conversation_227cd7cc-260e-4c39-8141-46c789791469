<?php /*a:2:{s:45:"F:\linshi\thphp\kmxt\view\category\index.html";i:1754117141;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1754115353;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类管理 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- 主样式文件 -->
    <link rel="stylesheet" href="/static/css/main.css">

    <!-- 自定义样式 -->
    <style>
        /* 基础模板中只保留必要的CSS变量，其他变量由外部CSS文件提供 */

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            overflow-x: hidden; /* 防止水平滚动条闪烁 */
        }
        
        /* 优化动画性能 */
        .sidebar,
        .main-content {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        /* 防止初始化时的闪烁 */
        .sidebar-loading .sidebar,
        .sidebar-loading .main-content {
            transition: none !important;
        }

        /* 防止导航激活状态闪烁 */
        .nav-loading .nav-link {
            transition: none !important;
        }














        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: var(--card-bg);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
            border-bottom: 1px solid var(--border-color);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: var(--text-secondary);
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: var(--content-bg);
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: var(--text-primary);
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: var(--content-bg);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: var(--content-bg);
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-right: 1rem;
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: var(--text-secondary);
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
<!-- 使用统一的CSS架构，移除内联样式 -->
<link rel="stylesheet" href="/static/css/main.css">
<link rel="stylesheet" href="/static/css/pages/categories.css">

</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">分类管理</h1>
        <p class="text-muted mb-0">管理和配置分类层级结构</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline btn-sm" onclick="expandAll()">
            <i class="fas fa-expand-arrows-alt"></i>
            全部展开
        </button>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="collapseAll()">
            <i class="fas fa-compress-arrows-alt"></i>
            全部收起
        </button>
        <button class="modern-btn modern-btn-success btn-sm" onclick="importCategories()">
            <i class="fas fa-file-import"></i>
            导入分类
        </button>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="exportCategories()">
            <i class="fas fa-file-export"></i>
            导出分类
        </button>
        <button class="modern-btn modern-btn-primary" onclick="showAddModal()">
            <i class="fas fa-plus"></i>
            新建分类
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="fas fa-layer-group"></i>
        </div>
        <div class="modern-stats-value">
            <?php 
                echo \app\model\Category::where('parent_id', 0)->where('status', 1)->count();
             ?>
        </div>
        <div class="modern-stats-label">顶级分类</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="fas fa-sitemap"></i>
        </div>
        <div class="modern-stats-value">
            <?php 
                echo \app\model\Category::where('status', 1)->count();
             ?>
        </div>
        <div class="modern-stats-label">分类总数</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
            <i class="fas fa-tags"></i>
        </div>
        <div class="modern-stats-value">
            <?php 
                echo \app\model\Card::count();
             ?>
        </div>
        <div class="modern-stats-label">卡密总数</div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="fas fa-layer-group"></i>
        </div>
        <div class="modern-stats-value">3</div>
        <div class="modern-stats-label">最大层级</div>
    </div>
</div>

<!-- 分类列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <div class="d-flex justify-content-between align-items-center w-100">
            <div>
                <h5 class="modern-card-title">
                    <i class="fas fa-sitemap me-2"></i>
                    分类层级结构
                </h5>
                <small class="text-muted">点击箭头或分类名展开/收起子分类，悬停显示操作按钮</small>
            </div>
            <button class="modern-btn modern-btn-outline btn-sm" onclick="showHierarchyHelp()">
                <i class="fas fa-question-circle"></i>
                层级说明
            </button>
        </div>
    </div>
    <div class="modern-card-body p-0">

        <?php if(empty($categoryTree) || (($categoryTree instanceof \think\Collection || $categoryTree instanceof \think\Paginator ) && $categoryTree->isEmpty())): ?>
        <div class="tree-view">
            <div class="tree-empty">
                <i class="fas fa-folder-open"></i>
                <h4>暂无分类数据</h4>
                <p>开始创建您的第一个分类吧</p>
                <button class="modern-btn modern-btn-primary" onclick="showAddModal()">
                    <i class="fas fa-plus"></i>
                    添加分类
                </button>
            </div>
        </div>
        <?php else: ?>
        <div class="tree-view">
            <ul class="tree-list">
                <?php if(is_array($categoryTree) || $categoryTree instanceof \think\Collection || $categoryTree instanceof \think\Paginator): $i = 0; $__LIST__ = $categoryTree;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                <!-- 一级分类 -->
                <li class="tree-node level-1" data-id="<?php echo htmlentities((string) $category['id']); ?>" data-level="1">
                    <div class="tree-node-content">
                        <!-- 展开/收起按钮 -->
                        <button class="tree-expand-btn <?php if(empty($category['children'])): ?>no-children<?php endif; ?>">
                            <?php if(!empty($category['children'])): ?>
                            <i class="fas fa-chevron-right"></i>
                            <?php endif; ?>
                        </button>

                        <!-- 分类图标 -->
                        <div class="tree-node-icon">
                            <i class="fas fa-folder"></i>
                        </div>

                        <!-- 分类信息 -->
                        <div class="tree-node-text">
                            <div class="tree-node-title"><?php echo htmlentities((string) $category['name']); ?></div>
                            <?php if($category['description']): ?>
                            <div class="tree-node-subtitle"><?php echo htmlentities((string) $category['description']); ?></div>
                            <?php endif; ?>
                        </div>

                        <!-- 状态和操作 -->
                        <div class="tree-node-status">
                            <span class="tree-status-badge status-<?php echo $category['status']==1 ? 'active'  :  'inactive'; ?>">
                                <i class="fas fa-<?php echo $category['status']==1 ? 'check-circle'  :  'times-circle'; ?>"></i>
                                <?php echo $category['status']==1 ? '启用'  :  '禁用'; ?>
                            </span>

                            <div class="tree-node-actions">
                                <button class="tree-action-btn btn-add" onclick="event.stopPropagation(); addSubCategory(<?php echo htmlentities((string) $category['id']); ?>)" title="添加子分类">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="tree-action-btn btn-edit" onclick="event.stopPropagation(); showEditModal(<?php echo htmlentities((string) $category['id']); ?>)" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="tree-action-btn btn-delete" onclick="event.stopPropagation(); deleteCategory(<?php echo htmlentities((string) $category['id']); ?>)" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 子分类容器 -->
                    <?php if(!empty($category['children'])): ?>
                    <div class="tree-children" id="children-<?php echo htmlentities((string) $category['id']); ?>" style="display: none;">
                        <ul class="tree-list">

                            <!-- 二级分类 -->
                            <?php if(is_array($category['children']) || $category['children'] instanceof \think\Collection || $category['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $category['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$child): $mod = ($i % 2 );++$i;?>
                            <li class="tree-node level-2" data-id="<?php echo htmlentities((string) $child['id']); ?>" data-level="2">
                                <div class="tree-node-content">
                                    <!-- 展开/收起按钮 -->
                                    <button class="tree-expand-btn <?php if(empty($child['children'])): ?>no-children<?php endif; ?>">
                                        <?php if(!empty($child['children'])): ?>
                                        <i class="fas fa-chevron-right"></i>
                                        <?php endif; ?>
                                    </button>

                                    <!-- 分类图标 -->
                                    <div class="tree-node-icon">
                                        <i class="fas fa-folder"></i>
                                    </div>

                                    <!-- 分类信息 -->
                                    <div class="tree-node-text">
                                        <div class="tree-node-title"><?php echo htmlentities((string) $child['name']); ?></div>
                                        <?php if($child['description']): ?>
                                        <div class="tree-node-subtitle"><?php echo htmlentities((string) $child['description']); ?></div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- 状态和操作 -->
                                    <div class="tree-node-status">
                                        <span class="tree-status-badge status-<?php echo $child['status']==1 ? 'active'  :  'inactive'; ?>">
                                            <i class="fas fa-<?php echo $child['status']==1 ? 'check-circle'  :  'times-circle'; ?>"></i>
                                            <?php echo $child['status']==1 ? '启用'  :  '禁用'; ?>
                                        </span>

                                        <div class="tree-node-actions">
                                            <button class="tree-action-btn btn-add" onclick="event.stopPropagation(); addSubCategory(<?php echo htmlentities((string) $child['id']); ?>)" title="添加子分类">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button class="tree-action-btn btn-edit" onclick="event.stopPropagation(); showEditModal(<?php echo htmlentities((string) $child['id']); ?>)" title="编辑">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="tree-action-btn btn-delete" onclick="event.stopPropagation(); deleteCategory(<?php echo htmlentities((string) $child['id']); ?>)" title="删除">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 三级分类容器 -->
                                <?php if(!empty($child['children'])): ?>
                                <div class="tree-children" id="children-<?php echo htmlentities((string) $child['id']); ?>" style="display: none;">
                                    <ul class="tree-list">
                                        <!-- 三级分类 -->
                                        <?php if(is_array($child['children']) || $child['children'] instanceof \think\Collection || $child['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $child['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$grandchild): $mod = ($i % 2 );++$i;?>
                                        <li class="tree-node level-3" data-id="<?php echo htmlentities((string) $grandchild['id']); ?>" data-level="3">
                                            <div class="tree-node-content">
                                                <!-- 展开/收起按钮（三级分类没有子级） -->
                                                <button class="tree-expand-btn no-children"></button>

                                                <!-- 分类图标 -->
                                                <div class="tree-node-icon">
                                                    <i class="fas fa-file"></i>
                                                </div>

                                                <!-- 分类信息 -->
                                                <div class="tree-node-text">
                                                    <div class="tree-node-title"><?php echo htmlentities((string) $grandchild['name']); ?></div>
                                                    <?php if($grandchild['description']): ?>
                                                    <div class="tree-node-subtitle"><?php echo htmlentities((string) $grandchild['description']); ?></div>
                                                    <?php endif; ?>
                                                </div>

                                                <!-- 状态和操作 -->
                                                <div class="tree-node-status">
                                                    <span class="tree-status-badge status-<?php echo $grandchild['status']==1 ? 'active'  :  'inactive'; ?>">
                                                        <i class="fas fa-<?php echo $grandchild['status']==1 ? 'check-circle'  :  'times-circle'; ?>"></i>
                                                        <?php echo $grandchild['status']==1 ? '启用'  :  '禁用'; ?>
                                                    </span>

                                                    <div class="tree-node-actions">
                                                        <button class="tree-action-btn btn-edit" onclick="event.stopPropagation(); showEditModal(<?php echo htmlentities((string) $grandchild['id']); ?>)" title="编辑">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="tree-action-btn btn-delete" onclick="event.stopPropagation(); deleteCategory(<?php echo htmlentities((string) $grandchild['id']); ?>)" title="删除">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </li>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </ul>
                                </div>
                                <?php endif; ?>
                            </li>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </ul>
                    </div>
                    <?php endif; ?>
                </li>
                <?php endforeach; endif; else: echo "" ;endif; ?>
            </ul>
        </div>
        <?php endif; ?>
</div>

        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.isAnimating = false;
                this.resizeTimeout = null;

                this.init();
            }

            init() {
                // 绑定事件
                this.sidebarToggle.addEventListener('click', (e) => this.handleToggle(e));
                document.addEventListener('click', (e) => this.handleOutsideClick(e));
                window.addEventListener('resize', () => this.handleResize());

                // 初始化状态
                this.updateLayout();
            }

            handleToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.isAnimating) return;

                this.isAnimating = true;

                if (window.innerWidth <= 768) {
                    this.toggleMobile();
                } else {
                    this.toggleDesktop();
                }

                // 动画完成后重置标志
                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }

            toggleMobile() {
                this.sidebar.classList.toggle('show');
            }

            toggleDesktop() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    this.sidebar.classList.add('collapsed');
                    this.mainContent.classList.add('expanded');
                }
            }

            handleOutsideClick(e) {
                if (window.innerWidth <= 768) {
                    if (!this.sidebar.contains(e.target) && !this.sidebarToggle.contains(e.target)) {
                        this.sidebar.classList.remove('show');
                    }
                }
            }

            handleResize() {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateLayout();
                }, 150);
            }

            updateLayout() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    // 移动端：移除桌面端的类
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    // 桌面端：移除移动端的类
                    this.sidebar.classList.remove('show');
                }
            }
        }

        // 初始化侧边栏管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载类防止初始化闪烁
            document.body.classList.add('sidebar-loading');

            // 初始化管理器
            new SidebarManager();

            // 移除加载类，启用过渡效果
            setTimeout(() => {
                document.body.classList.remove('sidebar-loading');
            }, 100);
        });

        // 导航激活状态管理器
        class NavigationManager {
            constructor() {
                this.currentPath = window.location.pathname;
                this.navLinks = document.querySelectorAll('.nav-link');
                this.init();
            }

            init() {
                // 添加导航加载类，暂时禁用过渡效果
                document.body.classList.add('nav-loading');

                // 立即设置激活状态，避免闪烁
                this.setActiveState();

                // 移除加载类，启用过渡效果
                setTimeout(() => {
                    document.body.classList.remove('nav-loading');
                }, 50);

                // 监听页面变化（如果使用了PJAX或类似技术）
                window.addEventListener('popstate', () => {
                    this.currentPath = window.location.pathname;
                    this.setActiveState();
                });
            }

            setActiveState() {
                // 使用requestAnimationFrame确保在下一帧执行，避免闪烁
                requestAnimationFrame(() => {
                    this.navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        const isActive = this.isLinkActive(href);

                        // 只在状态真正改变时才操作DOM
                        if (isActive && !link.classList.contains('active')) {
                            link.classList.add('active');
                        } else if (!isActive && link.classList.contains('active')) {
                            link.classList.remove('active');
                        }
                    });
                });
            }

            isLinkActive(href) {
                if (!href) return false;

                // 精确匹配路径
                if (this.currentPath === href) {
                    return true;
                }

                // 处理子路径匹配
                if (href !== '/' && this.currentPath.startsWith(href + '/')) {
                    return true;
                }

                // 特殊处理：根路径只在完全匹配时激活
                if (href === '/' && this.currentPath === '/') {
                    return true;
                }

                return false;
            }
        }

        // 初始化导航管理器
        document.addEventListener('DOMContentLoaded', function() {
            new NavigationManager();
        });
    </script>
    
    
<!-- 引入JavaScript模块 -->
<script src="/static/js/common.js"></script>
<script src="/static/js/api.js"></script>
<script src="/static/js/components/modal.js"></script>
<script src="/static/js/components/toast.js"></script>
<script src="/static/js/components/form.js"></script>
<script src="/static/js/utils/validator.js"></script>
<script src="/static/js/modules/category/tree.js"></script>
<script src="/static/js/modules/category/operations.js"></script>
<script src="/static/js/modules/category/index.js"></script>
<script>
// 页面特定的初始化代码可以在这里添加
console.log('分类管理页面加载完成');
</script>

<!-- 层级说明模态框 -->
<div id="hierarchyModal" class="modal" style="display: none;" onclick="if(event.target === this) closeHierarchyModal()">
    <div class="modal-content" style="max-width: 800px;">
        <div class="modal-header">
            <h2><i class="fas fa-sitemap"></i> 分类层级结构说明</h2>
            <span class="close" onclick="closeHierarchyModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="help-section">
                <h3><i class="fas fa-layer-group"></i> 分类层级说明</h3>
                <div class="hierarchy-levels">
                    <div class="level-item">
                        <span class="level-badge level-1">一级</span>
                        <div class="level-content">
                            <span class="level-desc">主分类 - 最多支持3级分类结构</span>
                            <span class="level-example">如：会员卡密、软件授权、数字内容</span>
                        </div>
                    </div>
                    <div class="level-item">
                        <span class="level-badge level-2">二级</span>
                        <div class="level-content">
                            <span class="level-desc">子分类 - 归属于一级分类</span>
                            <span class="level-example">如：基础会员、高级会员、开发工具</span>
                        </div>
                    </div>
                    <div class="level-item">
                        <span class="level-badge level-3">三级</span>
                        <div class="level-content">
                            <span class="level-desc">子子分类 - 归属于二级分类</span>
                            <span class="level-example">如：月度会员、年度会员、专业版</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="help-section">
                <h3><i class="fas fa-sort-numeric-down"></i> 排序功能说明</h3>
                <div class="sort-info">
                    <div class="info-grid">
                        <div class="info-item">
                            <i class="fas fa-arrow-up text-primary"></i>
                            <div>
                                <strong>排序规则</strong>
                                <p>数字越小，排序越靠前</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-layer-group text-success"></i>
                            <div>
                                <strong>同级排序</strong>
                                <p>同级分类之间可以调整排序</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-save text-warning"></i>
                            <div>
                                <strong>自动保存</strong>
                                <p>修改后自动保存并刷新显示</p>
                            </div>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-hashtag text-info"></i>
                            <div>
                                <strong>数值范围</strong>
                                <p>排序值范围：1-999</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeHierarchyModal()">
                <i class="fas fa-times"></i> 关闭
            </button>
        </div>
    </div>
</div>

<!-- 添加/编辑分类模态框 -->
<div id="categoryModal" class="modal" style="display: none;" onclick="if(event.target === this) closeCategoryModal()">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h2 id="categoryModalTitle"><i class="fas fa-plus"></i> 添加分类</h2>
            <span class="close" onclick="closeCategoryModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="categoryModalForm">
                <input type="hidden" id="modal_category_id" name="id">

                <div class="form-group">
                    <label for="modal_parent_id">父分类</label>
                    <input type="hidden" id="modal_parent_id" name="parent_id" value="0">
                    <div class="category-tree-selector" id="categoryTreeSelector">
                        <div class="tree-selector-header" onclick="toggleTreeSelector()">
                            <span class="selected-text">顶级分类</span>
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <div class="tree-selector-dropdown" id="treeSelectorDropdown" style="display: none;">
                            <div class="tree-item" data-id="0" onclick="selectCategory(0, '顶级分类', 0)">
                                <i class="fas fa-home text-primary"></i>
                                <span>顶级分类</span>
                            </div>
                        </div>
                    </div>
                    <small class="form-text">选择父分类，不选择则为顶级分类</small>
                </div>

                <div class="form-group">
                    <label for="modal_name">分类名称 <span style="color: #ff4d4f;">*</span></label>
                    <input type="text" id="modal_name" name="name" class="form-control" required maxlength="100">
                    <small class="form-text">分类名称，最多100个字符</small>
                </div>

                <div class="form-group">
                    <label for="modal_description">分类描述</label>
                    <textarea id="modal_description" name="description" class="form-control" rows="3" maxlength="500"></textarea>
                    <small class="form-text">分类的详细描述，最多500个字符</small>
                </div>

                <div class="form-group">
                    <label for="modal_sort_order">排序权重</label>
                    <input type="number" id="modal_sort_order" name="sort_order" class="form-control" value="0" min="0" max="9999">
                    <small class="form-text">数字越小排序越靠前，默认为0</small>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeCategoryModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" class="btn-primary" onclick="submitCategoryForm()">
                <span class="btn-text">
                    <i class="fas fa-save"></i> 保存
                </span>
                <span class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 保存中...
                </span>
            </button>
        </div>
    </div>
</div>


</body>
</html>
