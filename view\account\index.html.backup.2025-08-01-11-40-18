{extend name="layout/base" /}

{block name="title"}账户设置 - 卡密兑换管理系统{/block}

{block name="content"}
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        margin-bottom: 24px;
    }

    .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0;
    }

    /* 设置容器 */
    .settings-container {
        display: grid;
        grid-template-columns: 280px 1fr;
        gap: 24px;
        max-width: 1200px;
    }

    /* 设置侧边栏 */
    .settings-sidebar {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        height: fit-content;
    }

    .sidebar-nav {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .nav-item {
        margin-bottom: 8px;
    }

    .nav-link {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        color: var(--text-secondary);
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.2s ease;
        font-size: 14px;
        font-weight: 500;
    }

    .nav-link:hover {
        background-color: #f8f9fa;
        color: var(--text-primary);
        text-decoration: none;
    }

    .nav-link.active {
        background-color: var(--primary-color);
        color: white;
    }

    .nav-link i {
        width: 16px;
        text-align: center;
    }

    /* 主内容区 */
    .settings-main {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }

    .main-header {
        padding: 24px;
        border-bottom: 1px solid #f0f0f0;
        background: #f8f9fa;
    }

    .main-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
    }

    .main-body {
        padding: 24px;
    }

    /* 设置卡片 */
    .setting-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 24px;
        border: 1px solid #f0f0f0;
    }

    .setting-card h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 8px 0;
    }

    .setting-card p {
        color: var(--text-secondary);
        font-size: 14px;
        margin: 0 0 16px 0;
    }

    /* 表单样式 */
    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-control:disabled {
        background-color: #f5f5f5;
        color: #999;
        cursor: not-allowed;
    }

    .form-text {
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
    }

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: none;
    }

    .btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        color: white;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }

    .btn-danger {
        background: var(--danger-color);
        color: white;
    }

    .btn-danger:hover {
        background: #d32f2f;
        color: white;
        box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
    }

    .btn-outline {
        background: white;
        color: #6c757d;
        border: 1px solid #dee2e6;
    }

    .btn-outline:hover {
        background: #f8f9fa;
        color: #495057;
        border-color: #adb5bd;
    }

    /* 安全信息 */
    .security-info {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .info-item {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
    }

    .info-label {
        font-size: 12px;
        color: var(--text-secondary);
        margin-bottom: 4px;
        text-transform: uppercase;
        font-weight: 600;
    }

    .info-value {
        font-size: 14px;
        color: var(--text-primary);
        font-weight: 500;
    }

    /* 危险区域 */
    .danger-zone {
        margin-top: 32px;
        padding-top: 24px;
        border-top: 2px solid #ff4d4f;
    }

    .danger-zone h4 {
        color: var(--danger-color);
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .danger-zone p {
        color: var(--text-secondary);
        font-size: 14px;
        margin-bottom: 16px;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .settings-container {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .form-row {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .security-info {
            grid-template-columns: 1fr;
        }
    }
</style>

<!-- 页面头部 -->
<div class="page-header">
    <h1 class="page-title">账户设置</h1>
    <p class="page-subtitle">管理您的账户信息和安全设置</p>
</div>

<div class="settings-container">
    <!-- 设置侧边栏 -->
    <div class="settings-sidebar">
        <ul class="sidebar-nav">
            <li class="nav-item">
                <a href="#basic" class="nav-link active" onclick="showSection('basic', this)">
                    <i class="fas fa-user"></i>
                    基本信息
                </a>
            </li>
            <li class="nav-item">
                <a href="#security" class="nav-link" onclick="showSection('security', this)">
                    <i class="fas fa-shield-alt"></i>
                    安全设置
                </a>
            </li>
            <li class="nav-item">
                <a href="#sessions" class="nav-link" onclick="showSection('sessions', this)">
                    <i class="fas fa-desktop"></i>
                    会话管理
                </a>
            </li>
        </ul>
    </div>
    
    <!-- 主内容区 -->
    <div class="settings-main">
        <!-- 基本信息 -->
        <div id="basic-section" class="setting-section">
            <div class="main-header">
                <h3>基本信息</h3>
            </div>
            
            <div class="main-body">
                <div class="setting-card">
                    <h4>账户信息</h4>
                    <p>更新您的基本账户信息</p>
                    
                    <form id="basicForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">用户名</label>
                                <input type="text" name="username" class="form-control" value="{$admin.username}" required>
                                <div class="form-text">用户名用于登录系统</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">姓名</label>
                                <input type="text" name="name" class="form-control" value="{$admin.name}" required>
                                <div class="form-text">您的真实姓名或显示名称</div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" name="email" class="form-control" value="{$admin.email}">
                            <div class="form-text">用于接收系统通知和找回密码</div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 安全设置 -->
        <div id="security-section" class="setting-section" style="display: none;">
            <div class="main-header">
                <h3>安全设置</h3>
            </div>
            
            <div class="main-body">
                <!-- 安全信息 -->
                <div class="security-info">
                    <div class="info-item">
                        <div class="info-label">账户状态</div>
                        <div class="info-value">{$securitySettings.account_status}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">创建时间</div>
                        <div class="info-value">{$securitySettings.account_created|date='Y-m-d H:i'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最后登录</div>
                        <div class="info-value">{$securitySettings.last_login_time|date='Y-m-d H:i'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">登录IP</div>
                        <div class="info-value">{$securitySettings.last_login_ip}</div>
                    </div>
                </div>
                
                <div class="setting-card">
                    <h4>修改密码</h4>
                    <p>定期更改密码以保护您的账户安全</p>
                    
                    <form id="passwordForm">
                        <div class="form-group">
                            <label class="form-label">当前密码</label>
                            <input type="password" name="current_password" class="form-control" required>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">新密码</label>
                                <input type="password" name="new_password" class="form-control" minlength="6" required>
                                <div class="form-text">密码长度至少6位</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label">确认新密码</label>
                                <input type="password" name="confirm_password" class="form-control" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i>
                                修改密码
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- 会话管理 -->
        <div id="sessions-section" class="setting-section" style="display: none;">
            <div class="main-header">
                <h3>会话管理</h3>
            </div>
            
            <div class="main-body">
                <div class="setting-card">
                    <h4>活动会话</h4>
                    <p>管理您的登录会话，确保账户安全</p>
                    
                    <div class="security-info">
                        <div class="info-item">
                            <div class="info-label">当前会话</div>
                            <div class="info-value">
                                <i class="fas fa-circle text-success"></i>
                                活跃中
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">会话开始</div>
                            <div class="info-value">{$securitySettings.last_login_time|date='Y-m-d H:i'}</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">IP地址</div>
                            <div class="info-value">{$securitySettings.last_login_ip}</div>
                        </div>
                    </div>
                </div>
                
                <!-- 危险区域 -->
                <div class="danger-zone">
                    <h4>危险操作</h4>
                    <p>以下操作将影响您的账户安全，请谨慎操作</p>
                    
                    <div class="setting-card">
                        <h4>清除所有会话</h4>
                        <p>这将强制退出所有设备上的登录状态，您需要重新登录</p>
                        
                        <button type="button" class="btn btn-danger" onclick="clearAllSessions()">
                            <i class="fas fa-sign-out-alt"></i>
                            清除所有会话
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 切换设置区域
    function showSection(sectionName, element) {
        // 隐藏所有区域
        document.querySelectorAll('.setting-section').forEach(section => {
            section.style.display = 'none';
        });
        
        // 显示选中的区域
        document.getElementById(sectionName + '-section').style.display = 'block';
        
        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        element.classList.add('active');
    }

    // 基本信息表单提交
    document.getElementById('basicForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        fetch('/account/updateBasic', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('更新失败，请稍后重试', 'error');
        });
    });

    // 密码修改表单提交
    document.getElementById('passwordForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        // 验证密码确认
        if (data.new_password !== data.confirm_password) {
            showToast('两次输入的密码不一致', 'error');
            return;
        }
        
        fetch('/account/changePassword', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                document.getElementById('passwordForm').reset();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('密码修改失败，请稍后重试', 'error');
        });
    });

    // 清除所有会话
    function clearAllSessions() {
        if (!confirm('确定要清除所有会话吗？这将强制退出所有设备上的登录状态，您需要重新登录。')) {
            return;
        }
        
        fetch('/account/clearSessions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '/login';
                }, 2000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('操作失败，请稍后重试', 'error');
        });
    }

    // Toast 通知
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }
</script>

<!-- Toast 动画样式 -->
<style>
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .text-success {
        color: #52c41a !important;
    }
</style>

{/block}
