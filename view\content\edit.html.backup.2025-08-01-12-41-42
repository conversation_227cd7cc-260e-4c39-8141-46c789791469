{extend name="layout/base" /}

{block name="title"}编辑内容{/block}

{block name="content"}
<style>
    :root {
        --primary-color: #1890ff;
        --success-color: #52c41a;
        --warning-color: #fa8c16;
        --danger-color: #ff4d4f;
        --text-primary: #262626;
        --text-secondary: #8c8c8c;
        --border-color: #d9d9d9;
        --bg-light: #fafafa;
    }

    /* 页面头部 */
    .page-header {
        background: white;
        padding: 24px 32px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        margin-bottom: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-title {
        font-size: 24px;
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }

    .back-btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        color: #495057;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s ease;
    }

    .back-btn:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        color: #212529;
    }

    /* 编辑表单 */
    .edit-form {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        overflow: hidden;
    }

    .form-header {
        padding: 20px 24px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .form-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
    }

    .form-body {
        padding: 24px;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        margin-bottom: 24px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 14px;
    }

    .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        box-sizing: border-box;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    .form-control[readonly] {
        background: #f5f5f5;
        color: #666;
    }

    textarea.form-control {
        resize: vertical;
        min-height: 200px;
        font-family: 'Courier New', monospace;
        line-height: 1.5;
    }

    /* 卡密信息卡片 */
    .card-info {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 24px;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 500;
        color: var(--text-secondary);
        font-size: 13px;
    }

    .info-value {
        font-weight: 600;
        color: var(--text-primary);
        font-size: 14px;
    }

    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
    }

    .status-unused {
        background: #e6f7ff;
        color: #1890ff;
    }

    .status-used {
        background: #f6ffed;
        color: #52c41a;
    }

    .status-disabled {
        background: #fff2e8;
        color: #fa8c16;
    }

    /* 按钮 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: 1px solid transparent;
        text-align: center;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .btn-primary:hover {
        background: #0056b3;
        border-color: #0056b3;
        color: white;
        box-shadow: 0 3px 10px rgba(24, 144, 255, 0.25);
    }

    .btn-outline {
        background: #f8f9fa;
        border-color: #dee2e6;
        color: #495057;
    }

    .btn-outline:hover {
        border-color: #6c757d;
        color: #495057;
        background: #e9ecef;
    }

    .form-actions {
        padding: 20px 24px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    /* 字符计数 */
    .char-count {
        text-align: right;
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 4px;
    }

    .char-count.warning {
        color: var(--warning-color);
    }

    .char-count.danger {
        color: var(--danger-color);
    }

    /* 响应式 */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
        }
        
        .form-row {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .form-actions {
            flex-direction: column;
        }
    }
</style>

<div class="page-header">
    <h1 class="page-title">编辑内容</h1>
    <a href="/content" class="back-btn">
        <i class="fas fa-arrow-left"></i>
        返回列表
    </a>
</div>

<div class="edit-form">
    <div class="form-header">
        <h3>卡密内容编辑</h3>
    </div>
    
    <div class="form-body">
        <!-- 卡密信息 -->
        <div class="card-info">
            <div class="info-item">
                <span class="info-label">卡密编号</span>
                <span class="info-value">
                    <code style="background: #f5f5f5; padding: 2px 6px; border-radius: 3px; font-size: 12px;">
                        {$card.card_code}
                    </code>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">分类</span>
                <span class="info-value">{$card.category_name|default='未分类'}</span>
            </div>
            <div class="info-item">
                <span class="info-label">状态</span>
                <span class="info-value">
                    {switch name="card.status"}
                        {case value="0"}
                        <span class="status-badge status-unused">未使用</span>
                        {/case}
                        {case value="1"}
                        <span class="status-badge status-used">已使用</span>
                        {/case}
                        {case value="2"}
                        <span class="status-badge status-disabled">已禁用</span>
                        {/case}
                    {/switch}
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">创建时间</span>
                <span class="info-value">{$card.created_at}</span>
            </div>
            <div class="info-item">
                <span class="info-label">更新时间</span>
                <span class="info-value">{$card.updated_at}</span>
            </div>
        </div>

        <!-- 编辑表单 -->
        <form id="editForm">
            <input type="hidden" name="id" value="{$card.id}">

            <div class="form-row">
                <div class="form-group">
                    <label for="title">内容标题</label>
                    <input type="text" id="title" name="title" class="form-control"
                           placeholder="请输入内容标题（可选）"
                           maxlength="255"
                           value="{$card.title}">
                    <small class="form-text text-muted">标题将在内容列表中显示，如果不填写将自动从内容中提取</small>
                </div>
                <div class="form-group">
                    <label for="expire_at">过期时间（可选）</label>
                    <input type="datetime-local" id="expire_at" name="expire_at" class="form-control"
                           value="{$card.expire_at|date='Y-m-d\TH:i'}">
                </div>
            </div>

            <div class="form-group">
                <label for="content">内容 <span style="color: #ff4d4f;">*</span></label>
                <textarea id="content" name="content" class="form-control"
                          placeholder="请输入卡密的兑换内容..."
                          maxlength="5000"
                          oninput="updateCharCount()"
                          required>{$card.content}</textarea>
                <div class="char-count" id="charCount">0 / 5000</div>
            </div>
        </form>
    </div>
    
    <div class="form-actions">
        <a href="/content" class="btn btn-outline">
            <i class="fas fa-times"></i>
            取消
        </a>
        <button type="button" class="btn btn-primary" onclick="saveContent()">
            <i class="fas fa-save"></i>
            保存内容
        </button>
    </div>
</div>

<script>
    // 更新字符计数
    function updateCharCount() {
        const textarea = document.getElementById('content');
        const charCount = document.getElementById('charCount');
        const currentLength = textarea.value.length;
        const maxLength = 5000;

        charCount.textContent = `${currentLength} / ${maxLength}`;

        // 根据字符数量改变颜色
        if (currentLength > maxLength * 0.9) {
            charCount.className = 'char-count danger';
        } else if (currentLength > maxLength * 0.8) {
            charCount.className = 'char-count warning';
        } else {
            charCount.className = 'char-count';
        }
    }

    // 保存内容
    function saveContent() {
        const form = document.getElementById('editForm');
        const formData = new FormData(form);

        // 验证内容
        const content = formData.get('content').trim();
        if (!content) {
            showToast('请输入内容', 'warning');
            return;
        }

        if (content.length > 5000) {
            showToast('内容长度不能超过5000个字符', 'warning');
            return;
        }

        const loadingToast = showToast('正在保存...', 'info');

        fetch('/content/edit', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideToast(loadingToast);

            if (data.code === 200) {
                showToast(data.message, 'success');
                setTimeout(() => {
                    window.location.href = '/content';
                }, 1000);
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            hideToast(loadingToast);
            showToast('保存失败，请稍后重试', 'error');
            console.error('Error:', error);
        });
    }

    // Toast 通知功能
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-${getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // 添加样式
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${getToastColor(type)};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);

        return toast;
    }

    function hideToast(toast) {
        if (toast && toast.parentNode) {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    function getToastIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function getToastColor(type) {
        const colors = {
            success: '#52c41a',
            error: '#ff4d4f',
            warning: '#fa8c16',
            info: '#1890ff'
        };
        return colors[type] || '#1890ff';
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        updateCharCount();

        // 监听键盘快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+S 保存
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                saveContent();
            }
            // Esc 返回
            if (e.key === 'Escape') {
                window.location.href = '/content';
            }
        });
    });
</script>

<!-- 动画样式 -->
<style>
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
</style>

{/block}
