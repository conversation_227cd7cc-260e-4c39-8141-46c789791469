/**
 * 表单组件
 * 提供表单的通用操作功能
 */

const Form = {
    /**
     * 序列化表单数据
     * @param {HTMLFormElement|string} form 表单元素或选择器
     * @returns {object}
     */
    serialize(form) {
        const formElement = typeof form === 'string' ? document.querySelector(form) : form;
        if (!formElement) return {};
        
        const formData = new FormData(formElement);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            // 处理多选框
            if (data[key]) {
                if (Array.isArray(data[key])) {
                    data[key].push(value);
                } else {
                    data[key] = [data[key], value];
                }
            } else {
                data[key] = value;
            }
        }
        
        return data;
    },
    
    /**
     * 填充表单数据
     * @param {HTMLFormElement|string} form 表单元素或选择器
     * @param {object} data 数据对象
     */
    fill(form, data) {
        const formElement = typeof form === 'string' ? document.querySelector(form) : form;
        if (!formElement || !data) return;
        
        Object.keys(data).forEach(key => {
            const element = formElement.querySelector(`[name="${key}"]`);
            if (!element) return;
            
            const value = data[key];
            
            switch (element.type) {
                case 'checkbox':
                    element.checked = Boolean(value);
                    break;
                case 'radio':
                    if (element.value === String(value)) {
                        element.checked = true;
                    }
                    break;
                case 'select-one':
                case 'select-multiple':
                    element.value = value;
                    break;
                default:
                    element.value = value || '';
            }
        });
    },
    
    /**
     * 重置表单
     * @param {HTMLFormElement|string} form 表单元素或选择器
     */
    reset(form) {
        const formElement = typeof form === 'string' ? document.querySelector(form) : form;
        if (formElement) {
            formElement.reset();
        }
    },
    
    /**
     * 验证表单
     * @param {HTMLFormElement|string} form 表单元素或选择器
     * @param {object} rules 验证规则
     * @returns {object} 验证结果
     */
    validate(form, rules = {}) {
        const formElement = typeof form === 'string' ? document.querySelector(form) : form;
        if (!formElement) return { valid: false, errors: {} };
        
        const data = this.serialize(formElement);
        const errors = {};
        
        Object.keys(rules).forEach(field => {
            const rule = rules[field];
            const value = data[field];
            
            // 必填验证
            if (rule.required && (!value || value.trim() === '')) {
                errors[field] = rule.message || `${field}不能为空`;
                return;
            }
            
            // 如果值为空且不是必填，跳过其他验证
            if (!value || value.trim() === '') return;
            
            // 最小长度验证
            if (rule.minLength && value.length < rule.minLength) {
                errors[field] = rule.message || `${field}长度不能少于${rule.minLength}个字符`;
                return;
            }
            
            // 最大长度验证
            if (rule.maxLength && value.length > rule.maxLength) {
                errors[field] = rule.message || `${field}长度不能超过${rule.maxLength}个字符`;
                return;
            }
            
            // 正则验证
            if (rule.pattern && !rule.pattern.test(value)) {
                errors[field] = rule.message || `${field}格式不正确`;
                return;
            }
            
            // 自定义验证函数
            if (rule.validator && typeof rule.validator === 'function') {
                const result = rule.validator(value, data);
                if (result !== true) {
                    errors[field] = result || `${field}验证失败`;
                    return;
                }
            }
        });
        
        return {
            valid: Object.keys(errors).length === 0,
            errors: errors,
            data: data
        };
    },
    
    /**
     * 显示验证错误
     * @param {object} errors 错误对象
     * @param {string} containerSelector 错误容器选择器
     */
    showErrors(errors, containerSelector = '.form-errors') {
        const container = document.querySelector(containerSelector);
        if (!container) return;
        
        container.innerHTML = '';
        
        if (Object.keys(errors).length === 0) {
            container.style.display = 'none';
            return;
        }
        
        const errorList = document.createElement('ul');
        errorList.className = 'error-list';
        
        Object.values(errors).forEach(error => {
            const li = document.createElement('li');
            li.textContent = error;
            errorList.appendChild(li);
        });
        
        container.appendChild(errorList);
        container.style.display = 'block';
    },
    
    /**
     * 清除验证错误
     * @param {string} containerSelector 错误容器选择器
     */
    clearErrors(containerSelector = '.form-errors') {
        const container = document.querySelector(containerSelector);
        if (container) {
            container.innerHTML = '';
            container.style.display = 'none';
        }
    }
};

// 导出到全局
window.Form = Form;
