/**
 * 主样式文件
 * 引入所有CSS模块，建立完整的样式系统
 */

/* 基础样式 */
@import url('./base/variables.css');
@import url('./base/reset.css');
@import url('./base/typography.css');

/* 组件样式 */
@import url('./components/buttons.css');
@import url('./components/forms.css');
@import url('./components/modals.css');
@import url('./components/tables.css');
@import url('./components/cards.css');
@import url('./components/badges.css');
@import url('./components/tree-selector.css');
@import url('./components/tree-view.css');
@import url('./components/navigation.css');
@import url('./components/pagination.css');

/* 布局样式 */
@import url('./layouts/header.css');
@import url('./layouts/sidebar.css');
@import url('./layouts/main.css');

/* 工具类 */
@import url('./utilities/spacing.css');
@import url('./utilities/colors.css');
@import url('./utilities/display.css');

/* 页面特定样式 */
@import url('./pages/dashboard.css');
@import url('./pages/cards.css');
@import url('./pages/categories.css');
@import url('./pages/content.css');
@import url('./pages/settings.css');

/**
 * 全局基础样式
 */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
  margin: 0;
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
  background-color: var(--gray-100);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* 图片样式 */
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

/* 表格样式 */
table {
  border-collapse: collapse;
  width: 100%;
}

/* 代码样式 */
code,
kbd,
pre,
samp {
  font-family: var(--font-family-monospace);
  font-size: 1em;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: var(--font-size-sm);
}

code {
  font-size: var(--font-size-sm);
  color: var(--danger-color);
  word-wrap: break-word;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-200);
  border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-color);
  color: var(--white);
}

::-moz-selection {
  background-color: var(--primary-color);
  color: var(--white);
}

/* 焦点样式 */
:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* 无障碍辅助 */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* 响应式辅助类 */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

@media (min-width: 576px) {
  .d-sm-none { display: none !important; }
  .d-sm-inline { display: inline !important; }
  .d-sm-inline-block { display: inline-block !important; }
  .d-sm-block { display: block !important; }
  .d-sm-flex { display: flex !important; }
  .d-sm-inline-flex { display: inline-flex !important; }
}

@media (min-width: 768px) {
  .d-md-none { display: none !important; }
  .d-md-inline { display: inline !important; }
  .d-md-inline-block { display: inline-block !important; }
  .d-md-block { display: block !important; }
  .d-md-flex { display: flex !important; }
  .d-md-inline-flex { display: inline-flex !important; }
}

@media (min-width: 992px) {
  .d-lg-none { display: none !important; }
  .d-lg-inline { display: inline !important; }
  .d-lg-inline-block { display: inline-block !important; }
  .d-lg-block { display: block !important; }
  .d-lg-flex { display: flex !important; }
  .d-lg-inline-flex { display: inline-flex !important; }
}

@media (min-width: 1200px) {
  .d-xl-none { display: none !important; }
  .d-xl-inline { display: inline !important; }
  .d-xl-inline-block { display: inline-block !important; }
  .d-xl-block { display: block !important; }
  .d-xl-flex { display: flex !important; }
  .d-xl-inline-flex { display: inline-flex !important; }
}

/* 文本对齐 */
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

/* 文本颜色 */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-muted { color: var(--gray-500) !important; }

/* 背景颜色 */
.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--gray-100) !important; }
.bg-dark { background-color: var(--gray-800) !important; }
.bg-white { background-color: var(--white) !important; }

/* 边框 */
.border { border: var(--border-width) solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: var(--border-width) solid var(--border-color) !important; }
.border-right { border-right: var(--border-width) solid var(--border-color) !important; }
.border-bottom { border-bottom: var(--border-width) solid var(--border-color) !important; }
.border-left { border-left: var(--border-width) solid var(--border-color) !important; }

/* 圆角 */
.rounded { border-radius: var(--border-radius) !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-xl { border-radius: var(--border-radius-xl) !important; }
.rounded-full { border-radius: var(--border-radius-full) !important; }
.rounded-0 { border-radius: 0 !important; }

/* 阴影 */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }
.shadow-none { box-shadow: none !important; }
