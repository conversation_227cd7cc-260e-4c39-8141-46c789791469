/**
 * 树形选择器组件样式
 * 用于分类选择、层级数据选择等场景
 */

/* 分类树选择器样式 */
.category-tree-selector {
  position: relative;
}

.tree-selector-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tree-selector-input:hover {
  border-color: var(--primary-color);
}

.tree-selector-input.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.tree-selector-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10001;
  max-height: 300px;
  overflow-y: auto;
}

.tree-item {
  position: relative;
}

.tree-item-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.tree-item-content:hover {
  background-color: #f8f9fa;
}

.tree-item.selected .tree-item-content {
  background-color: var(--primary-color);
  color: white;
}

.expand-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  cursor: pointer;
  transition: transform 0.2s ease;
  color: #6c757d;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.expand-icon-placeholder {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.tree-item-icon {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

.tree-item-text {
  flex: 1;
}

.tree-item-level-1 { padding-left: 20px; }
.tree-item-level-2 { padding-left: 40px; }
.tree-item-level-3 { padding-left: 60px; }
.tree-item-level-4 { padding-left: 80px; }

.selected-text {
  flex: 1;
  color: var(--text-primary);
}

.dropdown-arrow {
  transition: transform 0.2s ease;
  color: var(--gray-500);
}

.tree-selector-input.active .dropdown-arrow {
  transform: rotate(180deg);
}

/* 树形选择器头部样式 */
.tree-selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tree-selector-header:hover {
  border-color: var(--primary-color);
}

.tree-selector-header.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 筛选器树形选择器样式 */
.filter-tree-selector {
  position: relative;
}

.filter-tree-selector-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 38px;
}

.filter-tree-selector-input:hover {
  border-color: var(--primary-color);
}

.filter-tree-selector-input.active {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.filter-tree-selector-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1049;
  max-height: 300px;
  overflow-y: auto;
}

.filter-tree-item {
  position: relative;
}

.filter-tree-item-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.filter-tree-item-content:hover {
  background-color: #f8f9fa;
}

.filter-tree-item.selected .filter-tree-item-content {
  background-color: var(--primary-color);
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tree-selector-dropdown,
  .filter-tree-selector-dropdown {
    max-height: 200px;
  }
  
  .tree-item-content,
  .filter-tree-item-content {
    padding: 10px 12px;
  }
  
  .tree-item-level-1 { padding-left: 15px; }
  .tree-item-level-2 { padding-left: 30px; }
  .tree-item-level-3 { padding-left: 45px; }
  .tree-item-level-4 { padding-left: 60px; }
}

/* 加载状态 */
.tree-selector-loading {
  padding: 20px;
  text-align: center;
  color: var(--gray-500);
}

.tree-selector-loading i {
  margin-right: 8px;
}

/* 空状态 */
.tree-selector-empty {
  padding: 20px;
  text-align: center;
  color: var(--gray-500);
}

.tree-selector-empty i {
  font-size: 2rem;
  margin-bottom: 8px;
  opacity: 0.5;
}
