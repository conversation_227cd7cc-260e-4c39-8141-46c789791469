<?php

namespace app\controller;

use app\BaseController;
use app\model\Category;
use think\facade\Db;

/**
 * 用户前端控制器
 */
class Index extends BaseController
{
    /**
     * 用户前端首页
     */
    public function index()
    {
        // 获取网站设置
        $settings = $this->getSettings();

        // 检查网站状态
        if ($settings['site_status'] != '1') {
            return view('index/maintenance', [
                'settings' => $settings
            ]);
        }

        return view('index/exchange', [
            'settings' => $settings
        ]);
    }

    /**
     * 卡密兑换页面
     */
    public function exchange()
    {
        $settings = $this->getSettings();

        // 检查网站状态
        if ($settings['site_status'] != '1') {
            return view('index/maintenance', [
                'settings' => $settings
            ]);
        }

        if ($this->request->isPost()) {
            return $this->doExchange();
        }

        return view('index/exchange', [
            'settings' => $settings
        ]);
    }

    /**
     * 执行卡密兑换
     */
    private function doExchange()
    {
        $cardCode = $this->request->post('card_code', '');

        if (empty($cardCode)) {
            return json(['code' => 400, 'message' => '请输入卡密']);
        }

        try {
            // 查找卡密
            $card = Db::name('cards')->where('card_code', $cardCode)->find();

            if (!$card) {
                return json(['code' => 404, 'message' => '卡密不存在或已失效']);
            }

            // 检查卡密状态
            if ($card['status'] == 1) {
                return json(['code' => 400, 'message' => '该卡密已被使用']);
            }

            if ($card['status'] == 2) {
                return json(['code' => 400, 'message' => '该卡密已被禁用']);
            }

            // 检查过期时间
            if ($card['expire_at'] && strtotime($card['expire_at']) < time()) {
                return json(['code' => 400, 'message' => '该卡密已过期']);
            }

            // 更新卡密状态
            Db::name('cards')->where('id', $card['id'])->update([
                'status' => 1,
                'used_at' => date('Y-m-d H:i:s'),
                'user_ip' => $this->request->ip(),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 记录兑换日志
            try {
                Db::name('exchange_logs')->insert([
                    'card_id' => $card['id'],
                    'card_code' => $card['card_code'],
                    'user_ip' => $this->request->ip(),
                    'user_agent' => $this->request->header('user-agent'),
                    'exchange_time' => date('Y-m-d H:i:s'),
                    'created_at' => date('Y-m-d H:i:s')
                ]);
            } catch (\Exception $e) {
                // 忽略日志记录错误
            }

            return json([
                'code' => 200,
                'message' => '兑换成功',
                'data' => [
                    'content' => $card['content'],
                    'exchange_time' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '兑换失败，请稍后重试']);
        }
    }



    /**
     * 获取网站设置
     */
    private function getSettings()
    {
        $defaults = [
            'site_name' => '卡密兑换管理系统',
            'site_description' => '专业的卡密兑换管理平台',
            'site_keywords' => '卡密,兑换,管理系统',
            'site_status' => '1',
            'maintenance_message' => '系统维护中，请稍后访问',
            'theme_color' => '#1890ff',
            'logo_url' => '',
            'favicon_url' => ''
        ];

        $settings = [];
        foreach ($defaults as $key => $default) {
            try {
                $setting = Db::name('settings')->where('key', $key)->find();
                $settings[$key] = $setting ? $setting['value'] : $default;
            } catch (\Exception $e) {
                $settings[$key] = $default;
            }
        }

        return $settings;
    }



    /**
     * 管理后台入口
     */
    public function admin()
    {
        // 检查登录状态，如果未登录则跳转到登录页
        if (!\app\controller\Admin::checkLogin()) {
            return redirect('/login');
        }

        return redirect('/dashboard');
    }

    public function hello($name = 'ThinkPHP8')
    {
        return 'hello,' . $name;
    }
}
