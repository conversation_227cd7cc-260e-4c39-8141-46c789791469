<?php

namespace app\controller;

use app\BaseController;
use think\facade\Db;
use think\facade\Session;

/**
 * 账户设置控制器
 */
class Account extends BaseController
{
    /**
     * 账户设置页面
     */
    public function index()
    {
        $adminId = Session::get('admin_id');
        
        if (!$adminId) {
            return redirect('/login');
        }
        
        // 获取管理员信息
        $admin = Db::name('admins')->where('id', $adminId)->find();
        
        if (!$admin) {
            return redirect('/login');
        }
        
        // 获取安全设置
        $securitySettings = $this->getSecuritySettings($adminId);
        
        return view('account/index', [
            'admin' => $admin,
            'securitySettings' => $securitySettings
        ]);
    }
    
    /**
     * 更新账户基本信息
     */
    public function updateBasic()
    {
        $adminId = Session::get('admin_id');
        
        if (!$adminId) {
            return json(['code' => 401, 'message' => '请先登录']);
        }
        
        $data = $this->request->post();
        
        try {
            // 验证数据
            $validate = $this->validate($data, [
                'username' => 'require|alphaNum|length:3,20',
                'name' => 'require|max:50',
                'email' => 'email|max:100'
            ]);
            
            if ($validate !== true) {
                return json(['code' => 400, 'message' => $validate]);
            }
            
            // 检查用户名是否已被其他用户使用
            $existingAdmin = Db::name('admins')
                ->where('username', $data['username'])
                ->where('id', '<>', $adminId)
                ->find();
                
            if ($existingAdmin) {
                return json(['code' => 400, 'message' => '该用户名已被其他用户使用']);
            }
            
            // 检查邮箱是否已被其他用户使用
            if (!empty($data['email'])) {
                $existingEmail = Db::name('admins')
                    ->where('email', $data['email'])
                    ->where('id', '<>', $adminId)
                    ->find();
                    
                if ($existingEmail) {
                    return json(['code' => 400, 'message' => '该邮箱已被其他用户使用']);
                }
            }
            
            // 更新数据
            $updateData = [
                'username' => $data['username'],
                'name' => $data['name'],
                'email' => $data['email'] ?? '',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $result = Db::name('admins')->where('id', $adminId)->update($updateData);
            
            if ($result !== false) {
                // 更新会话信息
                Session::set('admin_username', $data['username']);
                Session::set('admin_name', $data['name']);
                
                return json(['code' => 200, 'message' => '账户信息更新成功']);
            } else {
                return json(['code' => 500, 'message' => '更新失败']);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 修改密码
     */
    public function changePassword()
    {
        $adminId = Session::get('admin_id');
        
        if (!$adminId) {
            return json(['code' => 401, 'message' => '请先登录']);
        }
        
        $data = $this->request->post();
        
        try {
            // 验证数据
            $validate = $this->validate($data, [
                'current_password' => 'require',
                'new_password' => 'require|min:6|max:20',
                'confirm_password' => 'require|confirm:new_password'
            ]);
            
            if ($validate !== true) {
                return json(['code' => 400, 'message' => $validate]);
            }
            
            // 获取当前管理员信息
            $admin = Db::name('admins')->where('id', $adminId)->find();
            
            if (!$admin) {
                return json(['code' => 404, 'message' => '用户不存在']);
            }
            
            // 验证当前密码
            if (!password_verify($data['current_password'], $admin['password'])) {
                return json(['code' => 400, 'message' => '当前密码错误']);
            }
            
            // 更新密码
            $newPasswordHash = password_hash($data['new_password'], PASSWORD_DEFAULT);
            
            $result = Db::name('admins')->where('id', $adminId)->update([
                'password' => $newPasswordHash,
                'updated_at' => date('Y-m-d H:i:s')
            ]);
            
            if ($result !== false) {
                return json(['code' => 200, 'message' => '密码修改成功']);
            } else {
                return json(['code' => 500, 'message' => '密码修改失败']);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '密码修改失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 清除所有会话（强制重新登录）
     */
    public function clearSessions()
    {
        $adminId = Session::get('admin_id');
        
        if (!$adminId) {
            return json(['code' => 401, 'message' => '请先登录']);
        }
        
        try {
            // 清除当前会话
            Session::clear();
            
            return json(['code' => 200, 'message' => '所有会话已清除，请重新登录']);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '操作失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取安全设置信息
     */
    private function getSecuritySettings($adminId)
    {
        try {
            $admin = Db::name('admins')->where('id', $adminId)->find();
            
            return [
                'password_updated_at' => $admin['updated_at'] ?? '未知',
                'last_login_time' => $admin['last_login_time'] ?? '从未登录',
                'last_login_ip' => $admin['last_login_ip'] ?? '未知',
                'account_created' => $admin['created_at'] ?? '未知',
                'account_status' => $admin['status'] == 1 ? '正常' : '禁用'
            ];
            
        } catch (\Exception $e) {
            return [
                'password_updated_at' => '获取失败',
                'last_login_time' => '获取失败',
                'last_login_ip' => '获取失败',
                'account_created' => '获取失败',
                'account_status' => '获取失败'
            ];
        }
    }
}
