/**
 * 字体排版样式
 * 统一的字体和排版系统
 */

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: var(--spacing-4);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--gray-800);
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-base);
}

/* 段落样式 */
p {
  margin-top: 0;
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
}

/* 列表样式 */
ul, ol {
  margin-top: 0;
  margin-bottom: var(--spacing-4);
  padding-left: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-1);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
}

/* 引用样式 */
blockquote {
  margin: 0 0 var(--spacing-4);
  padding: var(--spacing-4) var(--spacing-6);
  border-left: 4px solid var(--primary-color);
  background-color: var(--gray-100);
  border-radius: var(--border-radius);
  font-style: italic;
  color: var(--gray-600);
}

/* 代码样式 */
code {
  font-family: var(--font-family-monospace);
  font-size: var(--font-size-sm);
  color: var(--danger-color);
  background-color: var(--gray-100);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
  word-wrap: break-word;
}

pre {
  font-family: var(--font-family-monospace);
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  background-color: var(--gray-100);
  padding: var(--spacing-4);
  border-radius: var(--border-radius);
  overflow-x: auto;
  margin-bottom: var(--spacing-4);
  border: var(--border-width) solid var(--border-color);
}

pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

/* 强调样式 */
strong, b {
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
}

em, i {
  font-style: italic;
}

/* 小字体 */
small {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

/* 标记样式 */
mark {
  background-color: var(--warning-light);
  color: var(--gray-800);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-sm);
}

/* 删除线 */
del {
  text-decoration: line-through;
  color: var(--gray-500);
}

/* 下划线 */
ins {
  text-decoration: underline;
  color: var(--primary-color);
}

/* 上标和下标 */
sup, sub {
  font-size: var(--font-size-xs);
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

/* 分隔线 */
hr {
  margin: var(--spacing-8) 0;
  border: 0;
  border-top: var(--border-width) solid var(--border-color);
}

/* 字体大小工具类 */
.text-xs { font-size: var(--font-size-xs) !important; }
.text-sm { font-size: var(--font-size-sm) !important; }
.text-base { font-size: var(--font-size-base) !important; }
.text-lg { font-size: var(--font-size-lg) !important; }
.text-xl { font-size: var(--font-size-xl) !important; }
.text-2xl { font-size: var(--font-size-2xl) !important; }
.text-3xl { font-size: var(--font-size-3xl) !important; }
.text-4xl { font-size: var(--font-size-4xl) !important; }

/* 字体粗细工具类 */
.font-light { font-weight: var(--font-weight-light) !important; }
.font-normal { font-weight: var(--font-weight-normal) !important; }
.font-medium { font-weight: var(--font-weight-medium) !important; }
.font-semibold { font-weight: var(--font-weight-semibold) !important; }
.font-bold { font-weight: var(--font-weight-bold) !important; }

/* 行高工具类 */
.leading-tight { line-height: var(--line-height-tight) !important; }
.leading-normal { line-height: var(--line-height-normal) !important; }
.leading-relaxed { line-height: var(--line-height-relaxed) !important; }

/* 字体样式工具类 */
.italic { font-style: italic !important; }
.not-italic { font-style: normal !important; }

/* 文本装饰工具类 */
.underline { text-decoration: underline !important; }
.line-through { text-decoration: line-through !important; }
.no-underline { text-decoration: none !important; }

/* 文本转换工具类 */
.uppercase { text-transform: uppercase !important; }
.lowercase { text-transform: lowercase !important; }
.capitalize { text-transform: capitalize !important; }
.normal-case { text-transform: none !important; }

/* 文本溢出工具类 */
.truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

.text-ellipsis {
  text-overflow: ellipsis !important;
}

.text-clip {
  text-overflow: clip !important;
}

/* 空白处理工具类 */
.whitespace-normal { white-space: normal !important; }
.whitespace-nowrap { white-space: nowrap !important; }
.whitespace-pre { white-space: pre !important; }
.whitespace-pre-line { white-space: pre-line !important; }
.whitespace-pre-wrap { white-space: pre-wrap !important; }

/* 单词换行工具类 */
.break-normal {
  overflow-wrap: normal !important;
  word-break: normal !important;
}

.break-words {
  overflow-wrap: break-word !important;
}

.break-all {
  word-break: break-all !important;
}
