<?php

namespace app\model;

use think\Model;

/**
 * 卡密分类模型
 */
class Category extends Model
{
    // 设置表名
    protected $table = 'km_categories';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'parent_id'   => 'int',
        'name'        => 'string',
        'description' => 'string',
        'status'      => 'int',
        'sort_order'  => 'int',
        'level'       => 'int',
        'path'        => 'string',
        'created_at'  => 'datetime',
        'updated_at'  => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    
    /**
     * 状态获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用',
        ];
        return $status[$data['status']] ?? '未知';
    }
    
    /**
     * 关联卡密
     */
    public function cards()
    {
        return $this->hasMany(Card::class, 'category_id', 'id');
    }
    
    /**
     * 获取启用的分类
     */
    public static function getEnabledCategories()
    {
        return self::where('status', self::STATUS_ENABLED)
                   ->order('sort_order', 'asc')
                   ->order('id', 'asc')
                   ->select();
    }

    /**
     * 获取分类树形结构
     */
    public static function getCategoryTree($parentId = 0)
    {
        $categories = self::where('parent_id', $parentId)
            ->where('status', self::STATUS_ENABLED)
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();

        $tree = [];
        foreach ($categories as $category) {
            $item = $category->toArray();
            $item['children'] = self::getCategoryTree($category->id);
            $tree[] = $item;
        }

        return $tree;
    }

    /**
     * 获取所有分类的扁平列表（带层级标识）
     */
    public static function getFlatCategories($parentId = 0, $level = 1, $prefix = '')
    {
        $categories = self::where('parent_id', $parentId)
            ->where('status', self::STATUS_ENABLED)  // 只获取启用的分类
            ->order('sort_order', 'asc')
            ->order('id', 'asc')
            ->select();

        $result = [];
        foreach ($categories as $category) {
            $item = $category->toArray();
            $item['display_name'] = $prefix . $category->name;
            $item['level'] = $level;
            $result[] = $item;

            // 递归获取子分类
            $children = self::getFlatCategories($category->id, $level + 1, $prefix . '├─ ');
            $result = array_merge($result, $children);
        }

        return $result;
    }

    /**
     * 获取分类路径
     */
    public function getFullPath()
    {
        if ($this->parent_id == 0) {
            return $this->name;
        }

        $parent = self::find($this->parent_id);
        if ($parent) {
            return $parent->getFullPath() . ' > ' . $this->name;
        }

        return $this->name;
    }

    /**
     * 更新分类路径
     */
    public function updatePath()
    {
        if ($this->parent_id == 0) {
            $this->path = (string)$this->id;
            $this->level = 1;
        } else {
            $parent = self::find($this->parent_id);
            if ($parent) {
                $this->path = $parent->path . ',' . $this->id;
                $this->level = $parent->level + 1;
            }
        }
        $this->save();

        // 更新所有子分类的路径
        $this->updateChildrenPath();
    }

    /**
     * 更新子分类路径
     */
    private function updateChildrenPath()
    {
        $children = self::where('parent_id', $this->id)->select();
        foreach ($children as $child) {
            $child->updatePath();
        }
    }

    /**
     * 检查是否可以删除（没有子分类和卡密）
     */
    public function canDelete()
    {
        // 检查是否有子分类
        $childCount = self::where('parent_id', $this->id)->count();
        if ($childCount > 0) {
            return false;
        }

        // 检查是否有关联的卡密
        $cardCount = \app\model\Card::where('category_id', $this->id)->count();
        if ($cardCount > 0) {
            return false;
        }

        return true;
    }

    /**
     * 获取所有子分类ID（包括子子分类）
     */
    public function getAllChildrenIds()
    {
        $ids = [];
        $children = self::where('parent_id', $this->id)->select();

        foreach ($children as $child) {
            $ids[] = $child->id;
            $ids = array_merge($ids, $child->getAllChildrenIds());
        }

        return $ids;
    }
    
    /**
     * 获取分类统计信息
     */
    public static function getCategoryStats()
    {
        return self::alias('c')
                   ->leftJoin('km_cards card', 'c.id = card.category_id')
                   ->field([
                       'c.id',
                       'c.name',
                       'COUNT(card.id) as total_cards',
                       'SUM(CASE WHEN card.status = 0 THEN 1 ELSE 0 END) as unused_cards',
                       'SUM(CASE WHEN card.status = 1 THEN 1 ELSE 0 END) as used_cards',
                       'SUM(CASE WHEN card.status = 2 THEN 1 ELSE 0 END) as disabled_cards'
                   ])
                   ->where('c.status', self::STATUS_ENABLED)
                   ->group('c.id')
                   ->order('c.sort_order', 'asc')
                   ->select();
    }
}
