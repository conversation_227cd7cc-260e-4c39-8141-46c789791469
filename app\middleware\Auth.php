<?php

namespace app\middleware;

use think\facade\Session;

/**
 * 管理员权限验证中间件
 */
class Auth
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     */
    public function handle($request, \Closure $next)
    {
        // 检查是否已登录
        if (!Session::has('admin_id')) {
            // 如果是AJAX请求，返回JSON
            if ($request->isAjax()) {
                return json(['code' => 401, 'message' => '请先登录', 'redirect' => '/login']);
            }
            
            // 普通请求重定向到登录页
            return redirect('/login');
        }
        
        // 验证会话有效性
        $adminId = Session::get('admin_id');
        if (!$adminId) {
            Session::clear();
            
            if ($request->isAjax()) {
                return json(['code' => 401, 'message' => '登录已过期，请重新登录', 'redirect' => '/login']);
            }
            
            return redirect('/login');
        }
        
        return $next($request);
    }
}
