/**
 * 卡密管理主控制器
 * 统一管理卡密相关的所有功能
 */

const CardManager = {
    /**
     * 初始化卡密管理功能
     */
    init() {
        console.log('初始化卡密管理功能...');
        
        // 初始化各个子模块
        this.initSubModules();
        
        // 绑定页面事件
        this.bindEvents();
        
        // 初始化表格功能
        this.initTable();
        
        // 初始化筛选功能
        this.initFilters();
        
        console.log('卡密管理功能初始化完成');
    },
    
    /**
     * 初始化子模块
     */
    initSubModules() {
        // 初始化详情功能
        if (typeof CardDetail !== 'undefined') {
            CardDetail.init();
        }
        
        // 初始化操作功能
        if (typeof CardOperations !== 'undefined') {
            CardOperations.init();
        }
        
        // 初始化生成功能
        if (typeof CardGenerate !== 'undefined') {
            CardGenerate.init();
        }
    },
    
    /**
     * 绑定页面事件
     */
    bindEvents() {
        // 绑定搜索表单
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }
        
        // 绑定搜索按钮
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.handleSearch());
        }
        
        // 绑定重置按钮
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.handleReset());
        }
        
        // 绑定筛选变化事件
        const filterSelects = document.querySelectorAll('.filter-select');
        filterSelects.forEach(select => {
            select.addEventListener('change', () => this.handleFilter());
        });
        
        // 绑定每页显示数量变化
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            pageSizeSelect.addEventListener('change', (e) => {
                Table.changePageSize(e.target.value);
            });
        }
    },
    
    /**
     * 初始化表格功能
     */
    initTable() {
        // 设置当前页面大小选中状态
        const pageSizeSelect = document.getElementById('pageSizeSelect');
        if (pageSizeSelect) {
            const currentPageSize = Pagination.getPageSize();
            pageSizeSelect.value = currentPageSize;
        }
        
        // 初始化批量操作按钮状态
        Table.updateBatchButtons();
    },
    
    /**
     * 初始化筛选功能
     */
    initFilters() {
        // 从URL参数中恢复筛选状态
        const url = new URL(window.location);
        const filters = {
            category_id: url.searchParams.get('category_id') || '',
            status: url.searchParams.get('status') || '',
            date_start: url.searchParams.get('date_start') || '',
            date_end: url.searchParams.get('date_end') || '',
            keyword: url.searchParams.get('keyword') || ''
        };
        
        // 设置筛选控件的值
        Object.keys(filters).forEach(key => {
            const element = document.getElementById(key);
            if (element && filters[key]) {
                element.value = filters[key];
            }
        });
    },
    
    /**
     * 处理搜索
     */
    handleSearch() {
        const keyword = document.getElementById('keyword');
        if (keyword) {
            Table.search(keyword.value.trim());
        }
    },
    
    /**
     * 处理重置
     */
    handleReset() {
        // 重置表单
        const searchForm = document.getElementById('searchForm');
        if (searchForm) {
            searchForm.reset();
        }
        
        // 重置筛选
        Table.resetFilter();
    },
    
    /**
     * 处理筛选
     */
    handleFilter() {
        const filters = {
            category_id: this.getFilterValue('category_id'),
            status: this.getFilterValue('status'),
            date_start: this.getFilterValue('date_start'),
            date_end: this.getFilterValue('date_end')
        };
        
        Table.filter(filters);
    },
    
    /**
     * 获取筛选值
     * @param {string} id 元素ID
     * @returns {string}
     */
    getFilterValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : '';
    },
    
    /**
     * 刷新页面数据
     */
    refresh() {
        Utils.reload();
    },
    
    /**
     * 获取选中的卡密数量
     * @returns {number}
     */
    getSelectedCount() {
        return Table.getSelectedIds().length;
    },
    
    /**
     * 显示统计信息
     */
    showStats() {
        const selectedCount = this.getSelectedCount();
        const totalCount = document.querySelectorAll('.item-checkbox').length;
        
        console.log(`已选中 ${selectedCount} / ${totalCount} 个卡密`);
    }
};

// 全局函数，供HTML调用
window.changePageSize = function(limit) {
    Table.changePageSize(limit);
};

window.updateStatus = function(id, status) {
    CardOperations.toggleStatus(id, status);
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保所有依赖都已加载
    if (typeof Utils !== 'undefined' && 
        typeof API !== 'undefined' && 
        typeof Table !== 'undefined') {
        CardManager.init();
    } else {
        console.error('卡密管理功能初始化失败：缺少依赖模块');
    }
});

// 导出模块
window.CardManager = CardManager;
