/**
 * 分类管理页面样式
 * 专门为分类管理页面设计的样式
 */

/* 分类树容器 */
.category-tree {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

.category-tree-header {
  padding: var(--spacing-5) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.category-tree-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.category-tree-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.category-tree-body {
  padding: var(--spacing-4);
  max-height: 600px;
  overflow-y: auto;
}

/* 分类树节点 */
.category-tree-node {
  margin-bottom: var(--spacing-2);
}

.category-tree-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-3);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  cursor: pointer;
  position: relative;
}

.category-tree-item:hover {
  background-color: var(--gray-100);
}

.category-tree-item.active {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.category-tree-item.selected {
  background-color: var(--primary-color);
  color: var(--white);
}

/* 树节点缩进 */
.category-tree-item.level-1 { padding-left: var(--spacing-3); }
.category-tree-item.level-2 { padding-left: var(--spacing-8); }
.category-tree-item.level-3 { padding-left: var(--spacing-12); }
.category-tree-item.level-4 { padding-left: var(--spacing-16); }

/* 展开/折叠图标 */
.category-tree-toggle {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-2);
  border-radius: var(--border-radius-sm);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.category-tree-toggle:hover {
  background-color: var(--gray-200);
}

.category-tree-toggle.expanded {
  transform: rotate(90deg);
}

.category-tree-toggle-icon {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

/* 分类图标 */
.category-tree-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.category-tree-icon.level-1 {
  background-color: var(--primary-color);
  color: var(--white);
}

.category-tree-icon.level-2 {
  background-color: var(--success-color);
  color: var(--white);
}

.category-tree-icon.level-3 {
  background-color: var(--warning-color);
  color: var(--white);
}

.category-tree-icon.level-4 {
  background-color: var(--info-color);
  color: var(--white);
}

/* 分类名称 */
.category-tree-name {
  flex: 1;
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-right: var(--spacing-3);
}

.category-tree-item.selected .category-tree-name {
  color: var(--white);
}

/* 分类统计 */
.category-tree-count {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  background-color: var(--gray-200);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--border-radius-full);
  min-width: 20px;
  text-align: center;
  margin-right: var(--spacing-2);
}

.category-tree-item.selected .category-tree-count {
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--white);
}

/* 分类操作按钮 */
.category-tree-actions-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.category-tree-item:hover .category-tree-actions-btn {
  opacity: 1;
}

.category-tree-action {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-sm);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-500);
}

.category-tree-action:hover {
  background-color: var(--gray-200);
  color: var(--gray-700);
}

.category-tree-action.action-edit:hover {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.category-tree-action.action-delete:hover {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.category-tree-action.action-add:hover {
  background-color: var(--success-light);
  color: var(--success-color);
}

/* 子分类容器 */
.category-tree-children {
  margin-left: var(--spacing-4);
  border-left: 2px solid var(--gray-200);
  padding-left: var(--spacing-2);
  margin-top: var(--spacing-2);
  display: none;
}

.category-tree-node.expanded > .category-tree-children {
  display: block;
}

/* 分类表单 */
.category-form {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
}

.category-form-section {
  margin-bottom: var(--spacing-6);
}

.category-form-section:last-child {
  margin-bottom: 0;
}

.category-form-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: var(--border-width) solid var(--border-color);
}

.category-form-row {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.category-form-col {
  flex: 1;
}

.category-form-col-auto {
  flex: 0 0 auto;
}

/* 父分类选择器 */
.category-parent-selector {
  position: relative;
}

.category-parent-input {
  cursor: pointer;
  background-color: var(--white);
}

.category-parent-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: var(--z-dropdown);
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  max-height: 300px;
  overflow-y: auto;
  display: none;
  margin-top: var(--spacing-1);
}

.category-parent-dropdown.show {
  display: block;
}

.category-parent-option {
  padding: var(--spacing-2) var(--spacing-3);
  cursor: pointer;
  transition: background-color var(--transition-fast);
  border-bottom: var(--border-width) solid var(--gray-200);
}

.category-parent-option:last-child {
  border-bottom: none;
}

.category-parent-option:hover {
  background-color: var(--gray-100);
}

.category-parent-option.selected {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

/* 分类图标选择器 */
.category-icon-selector {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--gray-50);
}

.category-icon-option {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
}

.category-icon-option:hover {
  background-color: var(--gray-100);
  transform: scale(1.1);
}

.category-icon-option.selected {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

/* 分类统计卡片 */
.category-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.category-stat-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  text-align: center;
  transition: all var(--transition-normal);
}

.category-stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.category-stat-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--spacing-3);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--white);
}

.category-stat-value {
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-1);
}

.category-stat-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

/* 空状态 */
.category-empty {
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
  color: var(--gray-500);
}

.category-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.category-empty-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
}

.category-empty-description {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-tree-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }
  
  .category-tree-actions {
    justify-content: center;
  }
  
  .category-form-row {
    flex-direction: column;
  }
  
  .category-stats {
    grid-template-columns: 1fr;
  }
  
  .category-tree-item.level-2 { padding-left: var(--spacing-6); }
  .category-tree-item.level-3 { padding-left: var(--spacing-8); }
  .category-tree-item.level-4 { padding-left: var(--spacing-10); }
}

@media (max-width: 576px) {
  .category-tree-body {
    padding: var(--spacing-3);
  }
  
  .category-form {
    padding: var(--spacing-4);
  }
  
  .category-tree-actions-btn {
    opacity: 1;
  }
  
  .category-icon-selector {
    grid-template-columns: repeat(auto-fill, minmax(35px, 1fr));
  }
  
  .category-icon-option {
    width: 35px;
    height: 35px;
  }
}
