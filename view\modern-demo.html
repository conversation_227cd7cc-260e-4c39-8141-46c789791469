{extend name="layout/base" /}

{block name="title"}现代化设计演示 - 卡密兑换管理系统{/block}

{block name="content"}

<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">现代化设计演示</h1>
        <p class="text-muted mb-0">展示新的UI设计风格和组件</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline">
            <i class="fas fa-refresh"></i>
            刷新
        </button>
        <button class="modern-btn modern-btn-primary">
            <i class="fas fa-plus"></i>
            新建
        </button>
    </div>
</div>

<!-- 统计卡片演示 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="fas fa-credit-card"></i>
        </div>
        <div class="modern-stats-value">1,234</div>
        <div class="modern-stats-label">总卡密数</div>
        <div class="modern-stats-trend positive">
            <i class="fas fa-arrow-up"></i>
            12.5%
        </div>
    </div>
    
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="modern-stats-value">856</div>
        <div class="modern-stats-label">已使用</div>
        <div class="modern-stats-trend positive">
            <i class="fas fa-arrow-up"></i>
            8.3%
        </div>
    </div>
    
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="modern-stats-value">378</div>
        <div class="modern-stats-label">未使用</div>
        <div class="modern-stats-trend negative">
            <i class="fas fa-arrow-down"></i>
            -2.1%
        </div>
    </div>
    
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
            <i class="fas fa-tags"></i>
        </div>
        <div class="modern-stats-value">25</div>
        <div class="modern-stats-label">分类总数</div>
        <div class="modern-stats-trend positive">
            <i class="fas fa-arrow-up"></i>
            4.2%
        </div>
    </div>
</div>

<!-- 现代化卡片演示 -->
<div class="row g-4 mb-4">
    <div class="col-md-8">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-table me-2"></i>
                    数据表格
                </h5>
                <div class="d-flex gap-2">
                    <button class="modern-btn modern-btn-outline btn-sm">
                        <i class="fas fa-filter"></i>
                        筛选
                    </button>
                    <button class="modern-btn modern-btn-success btn-sm">
                        <i class="fas fa-download"></i>
                        导出
                    </button>
                </div>
            </div>
            <div class="modern-card-body p-0">
                <div class="modern-table-container">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>001</td>
                                <td>示例项目</td>
                                <td><span class="modern-badge modern-badge-success">活跃</span></td>
                                <td>2024-01-15</td>
                                <td>
                                    <button class="modern-btn modern-btn-outline btn-sm">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>002</td>
                                <td>测试项目</td>
                                <td><span class="modern-badge modern-badge-warning">待审核</span></td>
                                <td>2024-01-14</td>
                                <td>
                                    <button class="modern-btn modern-btn-outline btn-sm">编辑</button>
                                </td>
                            </tr>
                            <tr>
                                <td>003</td>
                                <td>演示项目</td>
                                <td><span class="modern-badge modern-badge-error">已停用</span></td>
                                <td>2024-01-13</td>
                                <td>
                                    <button class="modern-btn modern-btn-outline btn-sm">编辑</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-edit me-2"></i>
                    表单示例
                </h5>
            </div>
            <div class="modern-card-body">
                <form>
                    <div class="modern-form-group">
                        <label class="modern-form-label">项目名称</label>
                        <input type="text" class="modern-form-control" placeholder="请输入项目名称">
                    </div>
                    
                    <div class="modern-form-group">
                        <label class="modern-form-label">项目类型</label>
                        <select class="modern-form-control">
                            <option>请选择类型</option>
                            <option>Web应用</option>
                            <option>移动应用</option>
                            <option>桌面应用</option>
                        </select>
                    </div>
                    
                    <div class="modern-form-group">
                        <label class="modern-form-label">项目描述</label>
                        <textarea class="modern-form-control" rows="3" placeholder="请输入项目描述"></textarea>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="modern-btn modern-btn-primary flex-fill">
                            <i class="fas fa-save"></i>
                            保存
                        </button>
                        <button type="reset" class="modern-btn modern-btn-outline">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 按钮演示 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-mouse-pointer me-2"></i>
            按钮样式演示
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="d-flex flex-wrap gap-3">
            <button class="modern-btn modern-btn-primary">
                <i class="fas fa-star"></i>
                主要按钮
            </button>
            <button class="modern-btn modern-btn-success">
                <i class="fas fa-check"></i>
                成功按钮
            </button>
            <button class="modern-btn modern-btn-outline">
                <i class="fas fa-edit"></i>
                轮廓按钮
            </button>
            <button class="modern-btn modern-btn-primary" disabled>
                <i class="fas fa-spinner fa-spin"></i>
                加载中...
            </button>
        </div>
    </div>
</div>

<!-- 状态标签演示 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-tags me-2"></i>
            状态标签演示
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="d-flex flex-wrap gap-3">
            <span class="modern-badge modern-badge-success">
                <i class="fas fa-check-circle"></i>
                成功
            </span>
            <span class="modern-badge modern-badge-warning">
                <i class="fas fa-exclamation-triangle"></i>
                警告
            </span>
            <span class="modern-badge modern-badge-error">
                <i class="fas fa-times-circle"></i>
                错误
            </span>
            <span class="modern-badge modern-badge-primary">
                <i class="fas fa-info-circle"></i>
                信息
            </span>
        </div>
    </div>
</div>

<style>
/* 演示页面专用样式 */
.content-wrapper {
    padding: 1.5rem !important;
    max-width: none !important;
    margin: 0 !important;
}

/* 按钮尺寸变体 */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
}

/* 优化统计卡片网格 */
.modern-stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* 确保统计卡片内容居中 */
.modern-stats-card {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    min-height: 160px;
}

.modern-stats-icon {
    margin: 0 auto 1rem auto !important;
}

.modern-stats-value {
    text-align: center !important;
}

.modern-stats-label {
    text-align: center !important;
}

.modern-stats-trend {
    justify-content: center !important;
}

/* 优化内容区域间距 */
.row.g-4 {
    margin-bottom: 2rem;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .modern-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .content-wrapper {
        padding: 1rem !important;
    }

    .modern-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .d-flex.gap-2 {
        flex-direction: column;
    }

    .modern-btn {
        justify-content: center;
    }

    .modern-stats-card {
        min-height: 140px;
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .modern-stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>

{/block}
