/**
 * 公共函数和工具
 * 包含全局使用的工具函数和常量
 */

// 全局配置
const Config = {
    // API基础路径
    apiBase: '/cards',
    
    // 卡密状态常量
    cardStatus: {
        UNUSED: 0,    // 未使用
        USED: 1,      // 已使用
        DISABLED: 2   // 已禁用
    },
    
    // 状态文本映射
    statusText: {
        0: '未使用',
        1: '已使用', 
        2: '已禁用'
    },
    
    // 状态样式映射
    statusClass: {
        0: 'modern-badge-warning',
        1: 'modern-badge-success',
        2: 'modern-badge-danger'
    }
};

// 工具函数
const Utils = {
    /**
     * 发送AJAX请求
     * @param {string} url 请求URL
     * @param {object} options 请求选项
     * @returns {Promise}
     */
    ajax(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const finalOptions = Object.assign(defaultOptions, options);
        
        return fetch(url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            });
    },
    
    /**
     * 显示确认对话框
     * @param {string} message 确认消息
     * @returns {boolean}
     */
    confirm(message) {
        return confirm(message);
    },
    
    /**
     * 显示提示消息
     * @param {string} message 消息内容
     * @param {string} type 消息类型 (success, error, warning, info)
     */
    showMessage(message, type = 'info') {
        // 简单的alert实现，后续可以替换为更好的提示组件
        alert(message);
    },
    
    /**
     * 刷新页面
     */
    reload() {
        location.reload();
    },
    
    /**
     * 格式化日期时间
     * @param {string} datetime 日期时间字符串
     * @returns {string}
     */
    formatDateTime(datetime) {
        if (!datetime) return '未知';
        return datetime;
    },
    
    /**
     * 获取状态显示文本
     * @param {number} status 状态值
     * @returns {string}
     */
    getStatusText(status) {
        return Config.statusText[status] || '未知';
    },
    
    /**
     * 获取状态样式类
     * @param {number} status 状态值
     * @returns {string}
     */
    getStatusClass(status) {
        return Config.statusClass[status] || 'modern-badge-secondary';
    },

    /**
     * 显示确认对话框
     * @param {string} message 确认消息
     * @returns {boolean}
     */
    confirm(message) {
        return confirm(message);
    }
};

// 导出到全局
window.Config = Config;
window.Utils = Utils;
