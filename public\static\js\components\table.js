/**
 * 表格组件
 * 提供表格的通用操作功能
 */

const Table = {
    /**
     * 全选/取消全选
     * @param {HTMLElement} checkbox 全选复选框
     * @param {string} itemSelector 项目复选框选择器
     */
    toggleSelectAll(checkbox, itemSelector = '.item-checkbox') {
        const items = document.querySelectorAll(itemSelector);
        items.forEach(item => {
            item.checked = checkbox.checked;
        });
        this.updateBatchButtons();
    },
    
    /**
     * 单项选择
     * @param {string} selectAllSelector 全选复选框选择器
     * @param {string} itemSelector 项目复选框选择器
     */
    toggleSelectItem(selectAllSelector = '#selectAll', itemSelector = '.item-checkbox') {
        const selectAll = document.querySelector(selectAllSelector);
        const items = document.querySelectorAll(itemSelector);
        const checkedItems = document.querySelectorAll(`${itemSelector}:checked`);
        
        if (selectAll) {
            selectAll.checked = items.length === checkedItems.length;
            selectAll.indeterminate = checkedItems.length > 0 && checkedItems.length < items.length;
        }
        
        this.updateBatchButtons();
    },
    
    /**
     * 获取选中的项目ID
     * @param {string} itemSelector 项目复选框选择器
     * @returns {Array}
     */
    getSelectedIds(itemSelector = '.item-checkbox:checked') {
        const checkedItems = document.querySelectorAll(itemSelector);
        return Array.from(checkedItems).map(item => item.value);
    },
    
    /**
     * 更新批量操作按钮状态
     * @param {string} buttonSelector 批量操作按钮选择器
     * @param {string} itemSelector 项目复选框选择器
     */
    updateBatchButtons(buttonSelector = '.batch-btn', itemSelector = '.item-checkbox:checked') {
        const buttons = document.querySelectorAll(buttonSelector);
        const checkedItems = document.querySelectorAll(itemSelector);
        const hasSelected = checkedItems.length > 0;
        
        buttons.forEach(button => {
            button.disabled = !hasSelected;
            if (hasSelected) {
                button.classList.remove('disabled');
            } else {
                button.classList.add('disabled');
            }
        });
    },
    
    /**
     * 排序表格
     * @param {string} column 排序列
     * @param {string} direction 排序方向 (asc/desc)
     */
    sort(column, direction = 'asc') {
        const url = new URL(window.location);
        url.searchParams.set('sort', column);
        url.searchParams.set('order', direction);
        window.location.href = url.toString();
    },
    
    /**
     * 改变每页显示数量
     * @param {number} limit 每页显示数量
     */
    changePageSize(limit) {
        const url = new URL(window.location);
        url.searchParams.set('limit', limit);
        url.searchParams.set('page', '1'); // 重置到第一页
        window.location.href = url.toString();
    },
    
    /**
     * 跳转到指定页面
     * @param {number} page 页码
     */
    goToPage(page) {
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    },
    
    /**
     * 搜索
     * @param {string} keyword 搜索关键词
     * @param {string} field 搜索字段
     */
    search(keyword, field = 'keyword') {
        const url = new URL(window.location);
        if (keyword) {
            url.searchParams.set(field, keyword);
        } else {
            url.searchParams.delete(field);
        }
        url.searchParams.set('page', '1'); // 重置到第一页
        window.location.href = url.toString();
    },
    
    /**
     * 筛选
     * @param {object} filters 筛选条件
     */
    filter(filters) {
        const url = new URL(window.location);
        
        // 清除现有筛选参数
        const filterKeys = ['category_id', 'status', 'date_start', 'date_end'];
        filterKeys.forEach(key => url.searchParams.delete(key));
        
        // 设置新的筛选参数
        Object.keys(filters).forEach(key => {
            if (filters[key] !== '' && filters[key] !== null && filters[key] !== undefined) {
                url.searchParams.set(key, filters[key]);
            }
        });
        
        url.searchParams.set('page', '1'); // 重置到第一页
        window.location.href = url.toString();
    },
    
    /**
     * 重置筛选
     */
    resetFilter() {
        const url = new URL(window.location);
        const filterKeys = ['category_id', 'status', 'date_start', 'date_end', 'keyword'];
        filterKeys.forEach(key => url.searchParams.delete(key));
        url.searchParams.set('page', '1');
        window.location.href = url.toString();
    }
};

// 导出到全局
window.Table = Table;
