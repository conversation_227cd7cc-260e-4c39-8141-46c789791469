/**
 * 表单组件样式
 * 统一的表单控件样式系统
 */

/* 表单基础样式 */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: inline-block;
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

.form-label.required::after {
  content: ' *';
  color: var(--danger-color);
}

/* 表单帮助文本 */
.form-text {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* 输入框基础样式 */
.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
  background-color: var(--white);
  background-clip: padding-box;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
  color: var(--gray-700);
  background-color: var(--white);
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.form-control::placeholder {
  color: var(--gray-500);
  opacity: 1;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: var(--gray-200);
  opacity: 1;
}

/* 输入框尺寸 */
.form-control-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-sm);
}

.form-control-lg {
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-lg);
}

/* 文本域 */
.form-textarea {
  min-height: 100px;
  resize: vertical;
}

/* 选择框 */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--spacing-10);
}

/* 复选框和单选框 */
.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: var(--spacing-2);
}

.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  margin-left: -1.5em;
  vertical-align: top;
  background-color: var(--white);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: var(--border-width) solid var(--border-color);
  appearance: none;
  color-adjust: exact;
}

.form-check-input[type="checkbox"] {
  border-radius: var(--border-radius-sm);
}

.form-check-input[type="radio"] {
  border-radius: 50%;
}

.form-check-input:active {
  filter: brightness(90%);
}

.form-check-input:focus {
  border-color: var(--primary-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.form-check-input:checked[type="checkbox"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
}

.form-check-input:checked[type="radio"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='white'/%3e%3c/svg%3e");
}

.form-check-label {
  color: var(--gray-700);
  cursor: pointer;
}

/* 开关 */
.form-switch {
  padding-left: 2.5em;
}

.form-switch .form-check-input {
  width: 2em;
  margin-left: -2.5em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  background-position: left center;
  border-radius: 2em;
  transition: background-position var(--transition-fast);
}

.form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 123, 255, 0.25%29'/%3e%3c/svg%3e");
}

.form-switch .form-check-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='white'/%3e%3c/svg%3e");
}

/* 输入组 */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group > .form-control {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--gray-700);
  text-align: center;
  white-space: nowrap;
  background-color: var(--gray-200);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
}

.input-group > :not(:first-child):not(.dropdown-menu) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.input-group > :not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* 表单验证状态 */
.is-valid {
  border-color: var(--success-color);
}

.is-valid:focus {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
}

.is-invalid {
  border-color: var(--danger-color);
}

.is-invalid:focus {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.25);
}

.valid-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--success-color);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: var(--spacing-1);
  font-size: var(--font-size-sm);
  color: var(--danger-color);
}

/* 表单布局 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc(var(--spacing-3) * -1);
  margin-left: calc(var(--spacing-3) * -1);
}

.form-row > .col,
.form-row > [class*="col-"] {
  padding-right: var(--spacing-3);
  padding-left: var(--spacing-3);
}

.form-inline {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
}

.form-inline .form-group {
  display: flex;
  flex: 0 0 auto;
  flex-flow: row wrap;
  align-items: center;
  margin-bottom: 0;
  margin-right: var(--spacing-4);
}

.form-inline .form-control {
  display: inline-block;
  width: auto;
  vertical-align: middle;
}

/* 浮动标签 */
.form-floating {
  position: relative;
}

.form-floating > .form-control {
  height: calc(3.5rem + 2px);
  padding: 1rem 0.75rem;
}

.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: 1rem 0.75rem;
  pointer-events: none;
  border: var(--border-width) solid transparent;
  transform-origin: 0 0;
  transition: opacity var(--transition-fast), transform var(--transition-fast);
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* 现代化表单样式 */
.modern-form-group {
  margin-bottom: 1.5rem;
}

.modern-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.modern-form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background: white;
}

.modern-form-control::placeholder {
  color: var(--gray-500);
}

.modern-form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 2.5rem;
}
