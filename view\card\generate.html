{extend name="layout/base" /}

{block name="title"}生成卡密{/block}

{block name="style"}
<!-- 使用统一的CSS架构，移除内联样式 -->
<link rel="stylesheet" href="/static/css/main.css">
<link rel="stylesheet" href="/static/css/pages/cards.css">
{/block}

{block name="content"}
<!-- 页面头部 -->

<div class="page-header">
    <div class="d-flex justify-content-between align-items-start">
        <div>
            <h1 class="page-title-main">生成卡密</h1>
            <p class="page-subtitle">批量生成卡密，支持自定义分类、数量和内容</p>
        </div>
        <div class="header-actions">
            <a href="/cards" class="header-btn btn-outline">
                <i class="fas fa-arrow-left"></i>
                <span>返回列表</span>
            </a>
        </div>
    </div>
</div>

<!-- 生成卡片 -->
<div class="generate-card">
    <div class="card-header">
        <h3>
            <i class="fas fa-magic"></i>
            卡密生成器
        </h3>
    </div>

    <div class="card-body">
        <!-- 生成说明 -->
        <div class="generate-tips">
            <h6>
                <i class="fas fa-info-circle"></i>
                生成说明
            </h6>
            <ul>
                <li>每次最多可生成1000个卡密，建议分批生成大量卡密</li>
                <li>卡密编号将自动生成16位唯一标识符，确保不重复</li>
                <li>过期时间为可选项，不设置则卡密永不过期</li>
                <li>生成后的卡密状态为"未使用"，可在卡密管理中查看</li>
                <li>兑换内容支持文本、链接、账号密码等多种格式</li>
            </ul>
        </div>

        <!-- 生成表单 -->
        <form id="generateForm" method="post">
            <div class="form-row">
                <div class="form-group">
                    <label for="category_id">选择分类 <span style="color: #ff4d4f;">*</span></label>
                    <select id="category_id" name="category_id" class="form-select" required>
                        <option value="">请选择分类</option>
                        {volist name="categories" id="category"}
                        <option value="{$category.id}"
                                class="category-option-level-{$category.level}">
                            {switch name="category.level"}
                                {case value="1"}
                                    ■ {$category.name}
                                {/case}
                                {case value="2"}
                                    &nbsp;&nbsp;&nbsp;&nbsp;▶ {$category.name}
                                {/case}
                                {case value="3"}
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;● {$category.name}
                                {/case}
                            {/switch}
                        </option>
                        {/volist}
                    </select>
                    <div class="form-text">
                        <i class="fas fa-exclamation-triangle"></i>
                        请选择卡密所属的分类，用于后续管理和统计
                    </div>
                </div>

                <div class="form-group">
                    <label for="count">生成数量 <span style="color: #ff4d4f;">*</span></label>
                    <input type="number" id="count" name="count" class="form-control"
                           min="1" max="1000" value="10" required>
                    <div class="form-text">
                        <i class="fas fa-calculator"></i>
                        请输入1-1000之间的数字，建议单次不超过500个
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="content">兑换内容 <span style="color: #ff4d4f;">*</span></label>
                <textarea id="content" name="content" class="form-control"
                          placeholder="请输入用户兑换卡密后显示的内容，支持多行文本..."
                          maxlength="5000"
                          oninput="updateCharCount()"
                          required></textarea>
                <div class="char-count" id="charCount">0 / 5000</div>
                <div class="form-text">
                    <i class="fas fa-edit"></i>
                    用户成功兑换卡密后将看到此内容，可以是账号密码、下载链接、激活码等
                </div>
            </div>

            <div class="form-group">
                <label for="expire_at">过期时间</label>
                <input type="datetime-local" id="expire_at" name="expire_at" class="form-control">
                <div class="form-text">
                    <i class="fas fa-clock"></i>
                    不设置则卡密永不过期，建议根据业务需求设置合理的过期时间
                </div>
            </div>
        </form>

        <!-- 结果容器 -->
        <div id="resultContainer" class="result-container" style="display: none;"></div>
    </div>

    <div class="card-footer">
        <div class="form-text">
            <i class="fas fa-shield-alt"></i>
            生成的卡密将自动保存到数据库，请确保信息准确无误
        </div>
        <div class="btn-group">
            <button type="button" class="btn btn-outline" onclick="resetForm()">
                <i class="fas fa-undo"></i>
                重置表单
            </button>
            <button type="button" id="generateBtn" class="btn btn-primary" onclick="submitGenerate()">
                <span class="btn-text">
                    <i class="fas fa-magic"></i>
                    开始生成
                </span>
                <span class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    生成中...
                </span>
            </button>
        </div>
    </div>
</div>

{/block}

{block name="script"}
<!-- 引入JavaScript模块 -->
<script src="/static/js/common.js"></script>
<script src="/static/js/api.js"></script>
<script src="/static/js/components/modal.js"></script>
<script src="/static/js/components/toast.js"></script>
<script src="/static/js/components/form.js"></script>
<script src="/static/js/utils/validator.js"></script>
<script src="/static/js/modules/card/generate-form.js"></script>
<script src="/static/js/modules/card/generate-index.js"></script>
<script>




</script>



{/block}

    /* 按钮样式 */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        border: none;
        text-align: center;
    }

    .btn:hover {
        text-decoration: none;
        transform: translateY(-1px);
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: #0056b3;
        color: white;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
    }

    .btn-primary:disabled {
        background: var(--gray-500);
        transform: none;
        box-shadow: none;
        opacity: 0.6;
        cursor: not-allowed;
    }

    .btn-outline {
        background: white;
        color: var(--text-secondary);
        border: 1px solid var(--border-color);
    }

    .btn-outline:hover {
        background: var(--content-bg);
        color: var(--text-primary);
        border-color: var(--gray-500);
    }

    /* 操作区域 */
    .card-footer {
        padding: 24px 32px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .btn-group {
        display: flex;
        gap: 12px;
    }

    /* 加载状态 */
    .loading-spinner {
        display: none;
    }

    .loading-spinner.show {
        display: inline-flex;
    }

    /* 结果提示 */
    .result-container {
        margin-top: 24px;
    }

    .alert {
        padding: 16px 20px;
        border-radius: 8px;
        border: 1px solid transparent;
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
        line-height: 1.5;
    }

    .alert-success {
        background: #f6ffed;
        border-color: #b7eb8f;
        color: #389e0d;
    }

    .alert-danger {
        background: #fff2f0;
        border-color: #ffccc7;
        color: #cf1322;
    }

    .alert i {
        font-size: 16px;
        flex-shrink: 0;
    }

    /* 字符计数 */
    .char-count {
        text-align: right;
        font-size: 12px;
        color: var(--text-secondary);
        margin-top: 6px;
    }

    .char-count.warning {
        color: var(--warning-color);
    }

    .char-count.danger {
        color: var(--danger-color);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .page-header .d-flex {
            flex-direction: column !important;
            align-items: stretch !important;
            gap: 16px;
        }

        .header-actions {
            justify-content: center;
        }

        .card-body {
            padding: 24px;
        }

        .card-footer {
            padding: 20px 24px;
            flex-direction: column;
            gap: 16px;
        }

        .form-row {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .btn-group {
            width: 100%;
            justify-content: center;
        }
    }
</style>
    
    .btn-generate {
        background: var(--primary-color);
        border: none;
        border-radius: 6px;
        padding: 12px 32px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        font-size: 14px;
    }

    .btn-generate:hover {
        background: #40a9ff;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        color: white;
    }

    .btn-generate:disabled {
        background: var(--text-secondary);
        transform: none;
        box-shadow: none;
        opacity: 0.6;
    }
    
    .generate-tips {
        background: #f0f9ff;
        border-left: 4px solid var(--primary-color);
        padding: 16px;
        border-radius: 0 6px 6px 0;
        margin-bottom: 24px;
        border: 1px solid #bae7ff;
        border-left: 4px solid var(--primary-color);
    }

    .generate-tips h6 {
        color: var(--primary-color);
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 600;
    }

    .generate-tips ul {
        margin: 0;
        padding-left: 20px;
    }

    .generate-tips li {
        color: var(--text-secondary);
        font-size: 13px;
        margin-bottom: 4px;
        line-height: 1.5;
    }
    
    .loading-spinner {
        display: none;
    }
    
    .loading-spinner.show {
        display: inline-block;
    }
    
    .result-alert {
        margin-top: 1rem;
        border-radius: 8px;
    }
</style>
{/block}

{block name="content"}
<div class="page-title">
    <h1>生成卡密</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/dashboard">控制台</a></li>
            <li class="breadcrumb-item"><a href="/cards">卡密管理</a></li>
            <li class="breadcrumb-item active">生成卡密</li>
        </ol>
    </nav>
</div>

<div class="generate-card">
    <div class="generate-tips">
        <h6><i class="fas fa-info-circle"></i> 生成说明</h6>
        <ul>
            <li>每次最多可生成1000个卡密</li>
            <li>卡密编号将自动生成，确保唯一性</li>
            <li>过期时间为可选项，不设置则永不过期</li>
            <li>生成后的卡密状态为"未使用"</li>
        </ul>
    </div>
    
    <form id="generateForm">
        <div class="mb-3">
            <label for="category_id" class="form-label">选择分类 <span class="text-danger">*</span></label>
            <select class="form-select" id="category_id" name="category_id" required>
                <option value="">请选择分类</option>
                {volist name="categories" id="category"}
                <option value="{$category.id}">{$category.name}</option>
                {/volist}
            </select>
        </div>
        
        <div class="mb-3">
            <label for="count" class="form-label">生成数量 <span class="text-danger">*</span></label>
            <input type="number" class="form-control" id="count" name="count" min="1" max="1000" value="10" required>
            <div class="form-text">请输入1-1000之间的数字</div>
        </div>
        
        <div class="mb-3">
            <label for="content" class="form-label">兑换内容 <span class="text-danger">*</span></label>
            <textarea class="form-control" id="content" name="content" rows="4" placeholder="请输入卡密兑换后显示的内容..." required></textarea>
            <div class="form-text">用户兑换卡密后将看到此内容</div>
        </div>
        
        <div class="mb-3">
            <label for="expire_at" class="form-label">过期时间</label>
            <input type="datetime-local" class="form-control" id="expire_at" name="expire_at">
            <div class="form-text">不设置则永不过期</div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a href="/cards" class="btn btn-outline-secondary me-md-2">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
            <button type="submit" class="btn btn-generate">
                <span class="btn-text">
                    <i class="fas fa-magic"></i> 开始生成
                </span>
                <span class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> 生成中...
                </span>
            </button>
        </div>
    </form>
    
    <div id="resultContainer"></div>
</div>
{/block}

{block name="script"}
<script>
    document.getElementById('generateForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        // 验证表单
        if (!data.category_id || !data.count || !data.content) {
            showResult('请填写所有必填项', 'danger');
            return;
        }
        
        const count = parseInt(data.count);
        if (count < 1 || count > 1000) {
            showResult('生成数量必须在1-1000之间', 'danger');
            return;
        }
        
        // 显示加载状态
        setLoading(true);
        
        // 发送请求
        fetch('/cards/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.code === 200) {
                showResult(result.message, 'success');
                // 清空表单
                document.getElementById('content').value = '';
                document.getElementById('count').value = '10';
                document.getElementById('expire_at').value = '';
                // 3秒后跳转到列表页
                setTimeout(() => {
                    window.location.href = '/cards';
                }, 3000);
            } else {
                showResult(result.message, 'danger');
            }
        })
        .catch(error => {
            showResult('生成失败，请稍后重试', 'danger');
        })
        .finally(() => {
            setLoading(false);
        });
    });
    
    function setLoading(loading) {
        const btnText = document.querySelector('.btn-text');
        const loadingSpinner = document.querySelector('.loading-spinner');
        const submitBtn = document.querySelector('.btn-generate');
        
        if (loading) {
            btnText.style.display = 'none';
            loadingSpinner.classList.add('show');
            submitBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            loadingSpinner.classList.remove('show');
            submitBtn.disabled = false;
        }
    }
    
    function showResult(message, type) {
        const container = document.getElementById('resultContainer');
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
        
        container.innerHTML = `
            <div class="alert ${alertClass} result-alert" role="alert">
                <i class="fas ${icon} me-2"></i>${message}
            </div>
        `;
        
        // 滚动到结果区域
        container.scrollIntoView({ behavior: 'smooth' });
        
        // 5秒后自动隐藏
        setTimeout(() => {
            container.innerHTML = '';
        }, 5000);
    }
    
    // 设置默认过期时间为30天后
    document.addEventListener('DOMContentLoaded', function() {
        const expireInput = document.getElementById('expire_at');
        const now = new Date();
        now.setDate(now.getDate() + 30);
        
        // 格式化为 datetime-local 格式
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        
        expireInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
    });
</script>
{/block}
