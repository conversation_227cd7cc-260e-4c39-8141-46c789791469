/**
 * 系统设置导航组件
 * 处理设置页面的导航切换和面板显示
 */

const SettingsNavigation = {
    /**
     * 初始化导航功能
     */
    init() {
        this.bindEvents();
        this.initActivePanel();
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 绑定导航项点击事件
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const target = item.dataset.target;
                if (target) {
                    this.switchPanel(target);
                }
            });
        });
        
        // 绑定键盘导航
        document.addEventListener('keydown', (e) => {
            // Alt + 数字键快速切换面板
            if (e.altKey && e.key >= '1' && e.key <= '4') {
                e.preventDefault();
                const panelIndex = parseInt(e.key) - 1;
                const panels = ['basic', 'security', 'advanced', 'system'];
                if (panels[panelIndex]) {
                    this.switchPanel(panels[panelIndex]);
                }
            }
        });
    },
    
    /**
     * 初始化激活面板
     */
    initActivePanel() {
        // 从URL hash获取要显示的面板
        const hash = window.location.hash.substring(1);
        const validPanels = ['basic', 'security', 'advanced', 'system'];
        
        if (hash && validPanels.includes(hash)) {
            this.switchPanel(hash);
        } else {
            // 默认显示基本设置面板
            this.switchPanel('basic');
        }
    },
    
    /**
     * 切换面板
     * @param {string} panelName 面板名称
     */
    switchPanel(panelName) {
        // 更新导航状态
        this.updateNavigation(panelName);
        
        // 显示对应面板
        this.showPanel(panelName);
        
        // 更新URL hash
        this.updateHash(panelName);
        
        // 触发面板切换事件
        this.triggerPanelChange(panelName);
    },
    
    /**
     * 更新导航状态
     * @param {string} activePanelName 激活的面板名称
     */
    updateNavigation(activePanelName) {
        // 移除所有激活状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // 添加激活状态到当前项
        const activeItem = document.querySelector(`[data-target="${activePanelName}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }
    },
    
    /**
     * 显示指定面板
     * @param {string} panelName 面板名称
     */
    showPanel(panelName) {
        // 隐藏所有面板
        document.querySelectorAll('.settings-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        
        // 显示目标面板
        const targetPanel = document.getElementById(`${panelName}-panel`);
        if (targetPanel) {
            targetPanel.classList.add('active');
            
            // 添加淡入动画
            targetPanel.style.opacity = '0';
            targetPanel.style.transform = 'translateY(10px)';
            
            requestAnimationFrame(() => {
                targetPanel.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                targetPanel.style.opacity = '1';
                targetPanel.style.transform = 'translateY(0)';
            });
        }
    },
    
    /**
     * 更新URL hash
     * @param {string} panelName 面板名称
     */
    updateHash(panelName) {
        // 更新URL hash但不触发页面跳转
        if (history.replaceState) {
            history.replaceState(null, null, `#${panelName}`);
        } else {
            window.location.hash = panelName;
        }
    },
    
    /**
     * 触发面板切换事件
     * @param {string} panelName 面板名称
     */
    triggerPanelChange(panelName) {
        const event = new CustomEvent('panelChange', {
            detail: { panelName }
        });
        document.dispatchEvent(event);
    },
    
    /**
     * 获取当前激活的面板
     * @returns {string} 当前激活的面板名称
     */
    getCurrentPanel() {
        const activePanel = document.querySelector('.settings-panel.active');
        if (activePanel) {
            return activePanel.id.replace('-panel', '');
        }
        return 'basic';
    },
    
    /**
     * 检查面板是否有未保存的更改
     * @param {string} panelName 面板名称
     * @returns {boolean}
     */
    hasUnsavedChanges(panelName) {
        const panel = document.getElementById(`${panelName}-panel`);
        if (!panel) return false;
        
        // 检查表单字段是否有更改
        const inputs = panel.querySelectorAll('input, textarea, select');
        for (let input of inputs) {
            if (input.dataset.originalValue !== undefined && 
                input.value !== input.dataset.originalValue) {
                return true;
            }
        }
        
        return false;
    },
    
    /**
     * 保存当前表单状态
     */
    saveFormState() {
        document.querySelectorAll('input, textarea, select').forEach(input => {
            input.dataset.originalValue = input.value;
        });
    },
    
    /**
     * 重置表单状态
     */
    resetFormState() {
        document.querySelectorAll('input, textarea, select').forEach(input => {
            if (input.dataset.originalValue !== undefined) {
                input.value = input.dataset.originalValue;
            }
        });
    },
    
    /**
     * 显示面板切换确认对话框
     * @param {string} targetPanel 目标面板
     * @returns {Promise<boolean>}
     */
    async confirmPanelSwitch(targetPanel) {
        const currentPanel = this.getCurrentPanel();
        
        if (this.hasUnsavedChanges(currentPanel)) {
            return new Promise((resolve) => {
                const result = confirm(
                    '当前面板有未保存的更改，确定要切换到其他面板吗？\n\n' +
                    '点击"确定"将丢失未保存的更改\n' +
                    '点击"取消"继续编辑当前面板'
                );
                resolve(result);
            });
        }
        
        return true;
    },
    
    /**
     * 安全切换面板（检查未保存更改）
     * @param {string} panelName 面板名称
     */
    async safeSwitchPanel(panelName) {
        const canSwitch = await this.confirmPanelSwitch(panelName);
        if (canSwitch) {
            this.switchPanel(panelName);
        }
    }
};

// 全局函数，供HTML调用
window.switchPanel = function(panelName) {
    SettingsNavigation.safeSwitchPanel(panelName);
};

// 导出模块
window.SettingsNavigation = SettingsNavigation;
