/**
 * 分页组件
 * 提供分页相关的功能
 */

const Pagination = {
    /**
     * 跳转到指定页面
     * @param {number} page 页码
     */
    goToPage(page) {
        if (page < 1) return;
        
        const url = new URL(window.location);
        url.searchParams.set('page', page);
        window.location.href = url.toString();
    },
    
    /**
     * 上一页
     */
    prevPage() {
        const currentPage = this.getCurrentPage();
        if (currentPage > 1) {
            this.goToPage(currentPage - 1);
        }
    },
    
    /**
     * 下一页
     * @param {number} totalPages 总页数
     */
    nextPage(totalPages) {
        const currentPage = this.getCurrentPage();
        if (currentPage < totalPages) {
            this.goToPage(currentPage + 1);
        }
    },
    
    /**
     * 第一页
     */
    firstPage() {
        this.goToPage(1);
    },
    
    /**
     * 最后一页
     * @param {number} totalPages 总页数
     */
    lastPage(totalPages) {
        this.goToPage(totalPages);
    },
    
    /**
     * 获取当前页码
     * @returns {number}
     */
    getCurrentPage() {
        const url = new URL(window.location);
        return parseInt(url.searchParams.get('page')) || 1;
    },
    
    /**
     * 获取每页显示数量
     * @returns {number}
     */
    getPageSize() {
        const url = new URL(window.location);
        return parseInt(url.searchParams.get('limit')) || 20;
    },
    
    /**
     * 改变每页显示数量
     * @param {number} limit 每页显示数量
     */
    changePageSize(limit) {
        const url = new URL(window.location);
        url.searchParams.set('limit', limit);
        url.searchParams.set('page', '1'); // 重置到第一页
        window.location.href = url.toString();
    },
    
    /**
     * 生成分页HTML
     * @param {object} options 分页选项
     * @returns {string}
     */
    generateHTML(options) {
        const {
            currentPage = 1,
            totalPages = 1,
            totalItems = 0,
            pageSize = 20,
            showSizeChanger = true,
            showQuickJumper = true,
            showTotal = true
        } = options;
        
        let html = '<div class="pagination-wrapper">';
        
        // 显示总数
        if (showTotal) {
            html += `<div class="pagination-total">共 ${totalItems} 条记录</div>`;
        }
        
        // 分页按钮
        html += '<div class="pagination-buttons">';
        
        // 上一页
        const prevDisabled = currentPage <= 1 ? 'disabled' : '';
        html += `<button class="pagination-btn ${prevDisabled}" onclick="Pagination.prevPage()" ${prevDisabled}>上一页</button>`;
        
        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);
        
        if (startPage > 1) {
            html += `<button class="pagination-btn" onclick="Pagination.goToPage(1)">1</button>`;
            if (startPage > 2) {
                html += '<span class="pagination-ellipsis">...</span>';
            }
        }
        
        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            html += `<button class="pagination-btn ${activeClass}" onclick="Pagination.goToPage(${i})">${i}</button>`;
        }
        
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html += '<span class="pagination-ellipsis">...</span>';
            }
            html += `<button class="pagination-btn" onclick="Pagination.goToPage(${totalPages})">${totalPages}</button>`;
        }
        
        // 下一页
        const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
        html += `<button class="pagination-btn ${nextDisabled}" onclick="Pagination.nextPage(${totalPages})" ${nextDisabled}>下一页</button>`;
        
        html += '</div>';
        
        // 每页显示数量选择器
        if (showSizeChanger) {
            html += '<div class="pagination-size-changer">';
            html += '<select onchange="Pagination.changePageSize(this.value)">';
            const sizes = [10, 20, 50, 100];
            sizes.forEach(size => {
                const selected = size === pageSize ? 'selected' : '';
                html += `<option value="${size}" ${selected}>${size}条/页</option>`;
            });
            html += '</select>';
            html += '</div>';
        }
        
        // 快速跳转
        if (showQuickJumper) {
            html += '<div class="pagination-quick-jumper">';
            html += '跳转到 <input type="number" min="1" max="' + totalPages + '" onkeypress="if(event.key===\'Enter\')Pagination.goToPage(this.value)"> 页';
            html += '</div>';
        }
        
        html += '</div>';
        
        return html;
    }
};

// 导出到全局
window.Pagination = Pagination;
