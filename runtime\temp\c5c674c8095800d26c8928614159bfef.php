<?php /*a:1:{s:42:"F:\linshi\thphp\kmxt\view\admin\login.html";i:1753954731;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 卡密兑换管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 50px 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .login-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 32px;
            color: white;
        }

        .login-title {
            font-size: 28px;
            font-weight: 700;
            color: #262626;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #8c8c8c;
            margin-bottom: 40px;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 25px;
            text-align: left;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #262626;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #d9d9d9;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
        }

        .input-icon {
            position: relative;
        }

        .input-icon i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #8c8c8c;
            font-size: 16px;
        }

        .input-icon .form-input {
            padding-left: 45px;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(24, 144, 255, 0.3);
        }

        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading-spinner {
            display: none;
        }

        .loading-spinner.show {
            display: inline-block;
        }

        .alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            display: none;
        }

        .alert-success {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
        }

        .alert-danger {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #cf1322;
        }

        .alert i {
            margin-right: 8px;
        }

        .back-link {
            color: #1890ff;
            text-decoration: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: #0050b3;
            text-decoration: none;
        }

        .default-account {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
            text-align: left;
        }

        .default-account h6 {
            margin-bottom: 8px;
            color: #333;
            font-size: 13px;
        }

        .default-account p {
            margin: 4px 0;
        }

        .default-account code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
            .login-container {
                padding: 40px 30px;
                margin: 10px;
            }
            
            .login-title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-icon">
            <i class="fas fa-user-shield"></i>
        </div>
        
        <h1 class="login-title">管理员登录</h1>
        <p class="login-subtitle">卡密兑换管理系统</p>
        
        <div id="alertContainer"></div>
        
        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <div class="input-icon">
                    <i class="fas fa-user"></i>
                    <input type="text" 
                           id="username" 
                           name="username" 
                           class="form-input" 
                           placeholder="请输入用户名"
                           required>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <div class="input-icon">
                    <i class="fas fa-lock"></i>
                    <input type="password" 
                           id="password" 
                           name="password" 
                           class="form-input" 
                           placeholder="请输入密码"
                           required>
                </div>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">
                <span class="btn-text">
                    <i class="fas fa-sign-in-alt"></i>
                    立即登录
                </span>
                <span class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    登录中...
                </span>
            </button>
        </form>
        
        <a href="/" class="back-link">
            <i class="fas fa-arrow-left"></i>
            返回前台
        </a>
        
        <div class="default-account">
            <h6><i class="fas fa-info-circle"></i> 默认管理员账号</h6>
            <p>用户名: <code>admin</code></p>
            <p>密码: <code>admin</code></p>
            <p style="margin-top: 8px; color: #999; font-size: 11px;">
                首次使用请登录后及时修改密码
            </p>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const btn = document.getElementById('loginBtn');
            const alertContainer = document.getElementById('alertContainer');
            
            // 自动聚焦到用户名输入框
            document.getElementById('username').focus();
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();
                
                if (!username || !password) {
                    showAlert('请输入用户名和密码', 'danger');
                    return;
                }
                
                // 显示加载状态
                setLoadingState(true);
                
                // 发送登录请求
                fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showAlert(data.message, 'success');
                        setTimeout(() => {
                            window.location.href = data.redirect || '/dashboard';
                        }, 1000);
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    showAlert('网络错误，请稍后重试', 'danger');
                })
                .finally(() => {
                    setLoadingState(false);
                });
            });
            
            function setLoadingState(loading) {
                const btnText = btn.querySelector('.btn-text');
                const loadingSpinner = btn.querySelector('.loading-spinner');
                
                if (loading) {
                    btnText.style.display = 'none';
                    loadingSpinner.classList.add('show');
                    btn.disabled = true;
                } else {
                    btnText.style.display = 'flex';
                    loadingSpinner.classList.remove('show');
                    btn.disabled = false;
                }
            }
            
            function showAlert(message, type) {
                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
                
                alertContainer.innerHTML = `
                    <div class="alert ${alertClass}">
                        <i class="${icon}"></i>
                        ${message}
                    </div>
                `;
                
                alertContainer.querySelector('.alert').style.display = 'block';
                
                // 3秒后自动隐藏错误信息
                if (type === 'danger') {
                    setTimeout(() => {
                        const alert = alertContainer.querySelector('.alert');
                        if (alert) {
                            alert.style.display = 'none';
                        }
                    }, 3000);
                }
            }
            
            // 回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    form.dispatchEvent(new Event('submit'));
                }
            });
        });
    </script>
</body>
</html>
