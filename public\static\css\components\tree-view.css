/**
 * 树形视图组件样式
 * 用于分类管理等层级结构展示
 */

/* 树形容器 */
.tree-view {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(229, 231, 235, 0.3);
}

/* 树形列表 */
.tree-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

/* 树形节点 */
.tree-node {
  position: relative;
  border-bottom: 1px solid rgba(229, 231, 235, 0.2);
}

.tree-node:last-child {
  border-bottom: none;
}

/* 节点内容 */
.tree-node-content {
  display: flex;
  align-items: center;
  padding: 0.875rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  min-height: 56px;
}

.tree-node-content:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.04) 0%, rgba(168, 85, 247, 0.04) 100%);
}

.tree-node-content.selected {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(168, 85, 247, 0.08) 100%);
  border-left: 3px solid var(--primary-color);
}

/* 展开/收起按钮 */
.tree-expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  margin-right: 0.5rem;
  color: var(--gray-500);
}

.tree-expand-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

.tree-expand-btn.expanded {
  transform: rotate(90deg);
}

.tree-expand-btn.no-children {
  visibility: hidden;
}

/* 节点图标 */
.tree-node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 8px;
  margin-right: 0.75rem;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

/* 一级分类图标 */
.tree-node.level-1 .tree-node-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

/* 二级分类图标 */
.tree-node.level-2 .tree-node-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

/* 三级分类图标 */
.tree-node.level-3 .tree-node-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

/* 节点文本 */
.tree-node-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.tree-node-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9375rem;
  line-height: 1.4;
}

.tree-node-subtitle {
  font-size: 0.8125rem;
  color: var(--gray-500);
  line-height: 1.3;
}

/* 节点状态 */
.tree-node-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
}

.tree-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.tree-status-badge.status-active {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-color);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.tree-status-badge.status-inactive {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 子节点容器 */
.tree-children {
  display: none;
  background: rgba(248, 250, 252, 0.5);
  border-left: 2px solid rgba(229, 231, 235, 0.3);
  margin-left: 1rem;
}

.tree-children.expanded {
  display: block;
}

/* 层级缩进 */
.tree-node.level-1 .tree-node-content {
  padding-left: 1rem;
}

.tree-node.level-2 .tree-node-content {
  padding-left: 2.5rem;
  position: relative;
}

.tree-node.level-2 .tree-node-content::before {
  content: '';
  position: absolute;
  left: 1.75rem;
  top: 0;
  bottom: 0;
  width: 1px;
  background: rgba(229, 231, 235, 0.5);
}

.tree-node.level-3 .tree-node-content {
  padding-left: 4rem;
  position: relative;
}

.tree-node.level-3 .tree-node-content::before {
  content: '';
  position: absolute;
  left: 3.25rem;
  top: 0;
  bottom: 0;
  width: 1px;
  background: rgba(229, 231, 235, 0.5);
}

/* 操作按钮 */
.tree-node-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tree-node-content:hover .tree-node-actions {
  opacity: 1;
}

.tree-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.tree-action-btn.btn-add {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-color);
}

.tree-action-btn.btn-add:hover {
  background: rgba(34, 197, 94, 0.2);
  transform: translateY(-1px);
}

.tree-action-btn.btn-edit {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.tree-action-btn.btn-edit:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.tree-action-btn.btn-delete {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.tree-action-btn.btn-delete:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

/* 空状态 */
.tree-empty {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--gray-500);
}

.tree-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}

.tree-empty h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.tree-empty p {
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tree-node-content {
    padding: 0.75rem 0.75rem;
    min-height: 48px;
  }
  
  .tree-node.level-2 .tree-node-content {
    padding-left: 2rem;
  }
  
  .tree-node.level-3 .tree-node-content {
    padding-left: 3rem;
  }
  
  .tree-node-icon {
    width: 28px;
    height: 28px;
    margin-right: 0.5rem;
  }
  
  .tree-node-title {
    font-size: 0.875rem;
  }
  
  .tree-node-subtitle {
    font-size: 0.75rem;
  }
}

/* 动画效果 */
.tree-children {
  transition: all 0.3s ease;
  overflow: hidden;
}

.tree-children.expanding {
  animation: expandDown 0.3s ease;
}

.tree-children.collapsing {
  animation: collapseUp 0.3s ease;
}

@keyframes expandDown {
  from {
    max-height: 0;
    opacity: 0;
  }
  to {
    max-height: 1000px;
    opacity: 1;
  }
}

@keyframes collapseUp {
  from {
    max-height: 1000px;
    opacity: 1;
  }
  to {
    max-height: 0;
    opacity: 0;
  }
}
