/**
 * 分页组件样式
 * 统一的分页样式系统
 */

/* 现代分页样式 */
.pagination {
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: var(--border-radius-lg);
  align-items: center;
  justify-content: center;
  gap: 0.375rem;
  background: var(--white);
  padding: 0.75rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(229, 231, 235, 0.3);
}

.page-item {
  display: flex;
}

.page-link {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 0.875rem;
  margin-left: 0;
  line-height: 1;
  color: var(--gray-600);
  text-decoration: none;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 40px;
  min-height: 40px;
  text-align: center;
  font-weight: 500;
  font-size: 0.875rem;
}

.page-link:hover {
  z-index: 2;
  color: var(--primary-color);
  text-decoration: none;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
  border-color: rgba(99, 102, 241, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.15);
}

.page-link:focus {
  z-index: 3;
  outline: 0;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.25);
}

.page-item:first-child .page-link {
  margin-left: 0;
}

.page-item.active .page-link {
  z-index: 3;
  color: var(--white);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
}

.page-item.active .page-link:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
  color: var(--white);
}

.page-item.disabled .page-link {
  color: var(--gray-400);
  pointer-events: none;
  cursor: not-allowed;
  background-color: transparent;
  border-color: transparent;
  opacity: 0.5;
}

.page-item.disabled .page-link:hover {
  transform: none;
  box-shadow: none;
}

/* 分页信息样式 */
.pagination-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(229, 231, 235, 0.3);
  font-size: 0.875rem;
  color: var(--gray-600);
}

.pagination-info .info-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-info .info-text i {
  color: var(--primary-color);
  font-size: 0.75rem;
}

.pagination-info .info-numbers {
  font-weight: 600;
  color: var(--text-primary);
}

/* 分页容器 */
.modern-pagination-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 1.5rem 0;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .modern-pagination-wrapper {
    flex-direction: column;
    gap: 1rem;
  }

  .pagination {
    order: 2;
  }

  .pagination-info {
    order: 1;
    width: 100%;
    justify-content: center;
  }
}

/* 分页尺寸 */
.pagination-lg .page-link {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-lg);
  line-height: 1.5;
  min-width: 48px;
}

.pagination-sm .page-link {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-sm);
  line-height: 1.5;
  min-width: 32px;
}

/* 现代分页样式 */
.modern-pagination {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-6);
  background: var(--white);
  border-top: var(--border-width) solid var(--border-color);
}

.modern-pagination-info {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.modern-pagination-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.modern-pagination .page-link {
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) var(--spacing-4);
}

.modern-pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  box-shadow: var(--shadow-sm);
}

/* 简单分页 */
.pagination-simple {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) 0;
}

.pagination-simple .page-info {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.pagination-simple .page-nav {
  display: flex;
  gap: var(--spacing-3);
}

.pagination-simple .page-link {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-lg);
}

/* 紧凑分页 */
.pagination-compact {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.pagination-compact .page-link {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-sm);
}

.pagination-compact .page-ellipsis {
  padding: 0 var(--spacing-2);
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

/* 分页跳转 */
.pagination-jumper {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-left: var(--spacing-4);
}

.pagination-jumper-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  white-space: nowrap;
}

.pagination-jumper-input {
  width: 60px;
  padding: var(--spacing-1) var(--spacing-2);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  text-align: center;
}

.pagination-jumper-btn {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius);
}

/* 每页显示数量选择器 */
.pagination-size-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-right: var(--spacing-4);
}

.pagination-size-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  white-space: nowrap;
}

.pagination-size-select {
  padding: var(--spacing-1) var(--spacing-2);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  background-color: var(--white);
}

/* 分页统计信息 */
.pagination-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) 0;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.pagination-total {
  font-weight: var(--font-weight-medium);
}

.pagination-range {
  color: var(--gray-500);
}

/* 加载分页 */
.pagination-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
}

.pagination-loading-spinner {
  margin-right: var(--spacing-2);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 无数据分页 */
.pagination-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8);
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

/* 响应式分页 */
@media (max-width: 768px) {
  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .modern-pagination {
    flex-direction: column;
    gap: var(--spacing-3);
    text-align: center;
  }
  
  .pagination-simple {
    flex-direction: column;
    gap: var(--spacing-3);
    text-align: center;
  }
  
  .pagination-jumper {
    margin-left: 0;
    margin-top: var(--spacing-3);
  }
  
  .pagination-size-selector {
    margin-right: 0;
    margin-bottom: var(--spacing-3);
  }
  
  /* 隐藏部分页码 */
  .page-item:not(.active):not(:first-child):not(:last-child):not(.page-prev):not(.page-next) {
    display: none;
  }
  
  .page-item:nth-child(2),
  .page-item:nth-last-child(2) {
    display: flex;
  }
}

@media (max-width: 576px) {
  .pagination-compact .page-link {
    width: 28px;
    height: 28px;
    font-size: var(--font-size-xs);
  }
  
  .modern-pagination {
    padding: var(--spacing-3) var(--spacing-4);
  }
  
  .pagination-jumper {
    flex-direction: column;
    gap: var(--spacing-2);
  }
  
  .pagination-size-selector {
    flex-direction: column;
    gap: var(--spacing-2);
  }
}

/* 分页主题变体 */
.pagination-primary .page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.pagination-success .page-item.active .page-link {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.pagination-warning .page-item.active .page-link {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: var(--gray-800);
}

.pagination-danger .page-item.active .page-link {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

/* 圆形分页 */
.pagination-rounded .page-link {
  border-radius: var(--border-radius-full);
}

/* 无边框分页 */
.pagination-borderless .page-link {
  border: none;
  background-color: transparent;
}

.pagination-borderless .page-item.active .page-link {
  background-color: var(--primary-color);
  color: var(--white);
}
