/**
 * 系统设置页面样式
 * 专门为系统设置页面设计的样式
 */

/* 设置页面布局 */
.settings-layout {
  display: flex;
  gap: var(--spacing-6);
  align-items: flex-start;
}

.settings-sidebar {
  flex: 0 0 250px;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
  position: sticky;
  top: var(--spacing-6);
}

.settings-content {
  flex: 1;
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

/* 设置导航 */
.settings-nav {
  padding: var(--spacing-4) 0;
}

.settings-nav-title {
  padding: 0 var(--spacing-4);
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.settings-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.settings-nav-item {
  margin-bottom: var(--spacing-1);
}

.settings-nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--gray-600);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: 0 var(--border-radius-full) var(--border-radius-full) 0;
  margin-right: var(--spacing-4);
}

.settings-nav-link:hover {
  color: var(--primary-color);
  background-color: var(--primary-light);
  text-decoration: none;
  transform: translateX(4px);
}

.settings-nav-link.active {
  color: var(--primary-color);
  background-color: var(--primary-light);
  font-weight: var(--font-weight-medium);
}

.settings-nav-icon {
  width: 20px;
  margin-right: var(--spacing-3);
  text-align: center;
  font-size: var(--font-size-base);
}

.settings-nav-text {
  flex: 1;
}

/* 设置内容区域 */
.settings-header {
  padding: var(--spacing-5) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
}

.settings-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
}

.settings-description {
  font-size: var(--font-size-base);
  color: var(--gray-600);
  margin: 0;
}

.settings-body {
  padding: var(--spacing-6);
}

/* 设置表单 */
.settings-form {
  max-width: 600px;
}

.settings-section {
  margin-bottom: var(--spacing-8);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.settings-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: var(--border-width) solid var(--border-color);
}

.settings-section-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-relaxed);
}

.settings-field {
  margin-bottom: var(--spacing-5);
}

.settings-field:last-child {
  margin-bottom: 0;
}

.settings-field-label {
  display: block;
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.settings-field-description {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  margin-top: var(--spacing-1);
  line-height: var(--line-height-relaxed);
}

.settings-field-row {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
}

.settings-field-col {
  flex: 1;
}

.settings-field-col-auto {
  flex: 0 0 auto;
}

/* 设置开关 */
.settings-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.settings-switch-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.settings-switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: var(--transition-fast);
  border-radius: 24px;
}

.settings-switch-slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: var(--white);
  transition: var(--transition-fast);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.settings-switch-input:checked + .settings-switch-slider {
  background-color: var(--primary-color);
}

.settings-switch-input:checked + .settings-switch-slider:before {
  transform: translateX(24px);
}

/* 设置卡片 */
.settings-card {
  background: var(--gray-50);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-5);
  margin-bottom: var(--spacing-4);
}

.settings-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-3);
}

.settings-card-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.settings-card-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  line-height: var(--line-height-relaxed);
}

/* 设置按钮组 */
.settings-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding-top: var(--spacing-6);
  border-top: var(--border-width) solid var(--border-color);
  margin-top: var(--spacing-6);
}

.settings-actions-left {
  flex: 1;
}

.settings-actions-right {
  display: flex;
  gap: var(--spacing-3);
}

/* 系统信息 */
.system-info {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

.system-info-header {
  padding: var(--spacing-5) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
}

.system-info-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.system-info-body {
  padding: var(--spacing-6);
}

.system-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
}

.system-info-item {
  padding: var(--spacing-4);
  background: var(--gray-50);
  border-radius: var(--border-radius);
  border: var(--border-width) solid var(--border-color);
}

.system-info-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-600);
  margin-bottom: var(--spacing-2);
}

.system-info-value {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
}

.system-info-status {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}

.system-info-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--border-radius-full);
}

.system-info-indicator.status-good {
  background-color: var(--success-color);
}

.system-info-indicator.status-warning {
  background-color: var(--warning-color);
}

.system-info-indicator.status-error {
  background-color: var(--danger-color);
}

/* 设置标签页 */
.settings-tabs {
  display: flex;
  border-bottom: var(--border-width) solid var(--border-color);
  margin-bottom: var(--spacing-6);
}

.settings-tab {
  padding: var(--spacing-3) var(--spacing-4);
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

.settings-tab:hover {
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.settings-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.settings-tab-content {
  display: none;
}

.settings-tab-content.active {
  display: block;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-layout {
    flex-direction: column;
  }
  
  .settings-sidebar {
    flex: none;
    position: static;
  }
  
  .settings-nav {
    display: flex;
    overflow-x: auto;
    padding: var(--spacing-2) 0;
  }
  
  .settings-nav-list {
    display: flex;
    gap: var(--spacing-2);
    padding: 0 var(--spacing-4);
  }
  
  .settings-nav-item {
    margin-bottom: 0;
    white-space: nowrap;
  }
  
  .settings-nav-link {
    margin-right: 0;
    border-radius: var(--border-radius-full);
    padding: var(--spacing-2) var(--spacing-4);
  }
  
  .settings-nav-link:hover {
    transform: none;
  }
  
  .settings-field-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .system-info-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .settings-actions-right {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .settings-header {
    padding: var(--spacing-4);
  }
  
  .settings-body {
    padding: var(--spacing-4);
  }
  
  .settings-title {
    font-size: var(--font-size-xl);
  }
  
  .settings-tabs {
    flex-wrap: wrap;
  }
  
  .settings-tab {
    flex: 1;
    text-align: center;
  }
}
