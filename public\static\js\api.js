/**
 * API接口封装
 * 统一管理所有API请求
 */

const API = {
    // 基础配置
    baseURL: '',
    timeout: 30000,
    
    /**
     * 发送HTTP请求
     * @param {string} url 请求URL
     * @param {object} options 请求选项
     * @returns {Promise}
     */
    request(url, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };
        
        const finalOptions = Object.assign(defaultOptions, options);
        
        return fetch(url, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('API请求错误:', error);
                throw error;
            });
    },
    
    /**
     * GET请求
     * @param {string} url 请求URL
     * @param {object} params 查询参数
     * @returns {Promise}
     */
    get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl);
    },
    
    /**
     * POST请求
     * @param {string} url 请求URL
     * @param {object} data 请求数据
     * @returns {Promise}
     */
    post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    },
    
    /**
     * PUT请求
     * @param {string} url 请求URL
     * @param {object} data 请求数据
     * @returns {Promise}
     */
    put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    },
    
    /**
     * DELETE请求
     * @param {string} url 请求URL
     * @returns {Promise}
     */
    delete(url) {
        return this.request(url, {
            method: 'DELETE'
        });
    },
    
    // 卡密相关API
    card: {
        // 获取卡密列表
        list: (params) => API.get('/cards', params),
        
        // 获取单个卡密
        get: (id) => API.get(`/cards/getCard/${id}`),
        
        // 生成卡密
        generate: (data) => API.post('/cards/generate', data),
        
        // 更新卡密
        update: (data) => API.post('/cards/update', data),
        
        // 删除卡密
        delete: (data) => API.post('/cards/delete', data),
        
        // 更新状态
        updateStatus: (data) => API.post('/cards/updateStatus', data),
        
        // 批量删除
        batchDelete: (data) => API.post('/cards/batchDelete', data),
        
        // 导出
        export: (data) => API.post('/cards/export', data)
    },
    
    // 分类相关API
    category: {
        // 获取分类列表
        list: (params) => API.get('/categories', params),

        // 获取分类树（用于卡密生成）
        tree: () => API.get('/cards/getCategories'),

        // 获取分类数据（用于分类管理）
        getCategories: (params) => API.get('/categories/getCategories', params),

        // 获取父分类选项
        getParentOptions: (params) => API.get('/categories/getParentOptions', params),

        // 创建分类
        create: (data) => API.post('/categories', data),

        // 更新分类
        update: (id, data) => API.put(`/categories/${id}`, data),

        // 删除分类
        delete: (id) => API.delete(`/categories/${id}`)
    },
    
    // 内容相关API
    content: {
        // 获取内容列表
        list: (params) => API.get('/contents', params),

        // 获取分类下的内容（用于卡密生成）
        getByCategory: (categoryId) => API.get('/cards/getCategoryContents', { category_id: categoryId }),

        // 获取单个内容
        get: (id) => API.get(`/contents/${id}`),

        // 创建内容
        create: (data) => API.post('/contents', data),

        // 更新内容
        update: (id, data) => API.put(`/contents/${id}`, data),

        // 删除内容
        delete: (id) => API.delete(`/contents/${id}`)
    },
    
    // 系统设置相关API
    setting: {
        // 获取设置
        get: (key) => API.get(`/settings/${key}`),
        
        // 更新设置
        update: (data) => API.post('/settings', data)
    },
    
    // 分类相关API
    category: {
        // 获取分类列表
        list: (params) => API.get('/categories', params),

        // 获取分类详情
        get: (id) => API.get(`/categories/${id}`),

        // 创建分类
        create: (data) => API.post('/categories', data),

        // 更新分类
        update: (id, data) => API.put(`/categories/${id}`, data),

        // 删除分类
        delete: (id) => API.delete(`/categories/${id}`),

        // 获取父分类选项
        getParentOptions: (params) => API.get('/categories/parent-options', params),

        // 获取分类路径
        getPath: (id) => API.get(`/categories/${id}/path`)
    },

    // 控制台相关API
    dashboard: {
        // 获取统计数据
        stats: () => API.get('/dashboard/stats'),

        // 获取图表数据
        charts: (params) => API.get('/dashboard/charts', params)
    }
};

// 导出到全局
window.API = API;
