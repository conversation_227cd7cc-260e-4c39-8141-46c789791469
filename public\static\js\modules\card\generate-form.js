/**
 * 卡密生成表单组件
 * 处理卡密生成表单的验证、提交和交互功能
 */

const CardGenerateForm = {
    /**
     * 初始化表单功能
     */
    init() {
        this.bindEvents();
        this.initFormState();
        this.loadCategories();
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 绑定表单提交事件
        const form = document.getElementById('generateForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitForm();
            });
        }
        
        // 绑定生成按钮事件
        const generateBtn = document.getElementById('generateBtn');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.submitForm();
            });
        }
        
        // 绑定重置按钮事件
        const resetBtn = document.querySelector('[onclick="resetForm()"]');
        if (resetBtn) {
            resetBtn.removeAttribute('onclick');
            resetBtn.addEventListener('click', () => {
                this.resetForm();
            });
        }
        
        // 绑定分类选择事件
        const categorySelect = document.getElementById('category_id');
        if (categorySelect) {
            categorySelect.addEventListener('change', (e) => {
                this.onCategoryChange(e.target.value);
            });
        }
        
        // 绑定内容输入事件
        const contentTextarea = document.getElementById('content');
        if (contentTextarea) {
            contentTextarea.addEventListener('input', () => {
                this.updateCharCount();
            });
        }
        
        // 绑定数量输入事件
        const countInput = document.getElementById('count');
        if (countInput) {
            countInput.addEventListener('input', () => {
                this.validateCount();
            });
        }
        
        // 绑定键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+Enter 快速生成
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.submitForm();
            }
            // Ctrl+R 重置表单
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.resetForm();
            }
        });
    },
    
    /**
     * 初始化表单状态
     */
    initFormState() {
        this.updateCharCount();
        this.setDefaultExpireTime();
    },
    
    /**
     * 加载分类数据
     */
    loadCategories() {
        API.category.getList()
            .then(data => {
                if (data.code === 200) {
                    this.renderCategoryOptions(data.data);
                }
            })
            .catch(error => {
                console.error('加载分类失败:', error);
            });
    },
    
    /**
     * 渲染分类选项
     * @param {Array} categories 分类数据
     */
    renderCategoryOptions(categories) {
        const categorySelect = document.getElementById('category_id');
        if (!categorySelect) return;
        
        // 清空现有选项
        categorySelect.innerHTML = '<option value="">请选择分类</option>';
        
        // 添加分类选项
        categories.forEach(category => {
            if (category.status === 1) { // 只显示启用的分类
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            }
        });
    },
    
    /**
     * 分类选择变化处理
     * @param {string} categoryId 分类ID
     */
    onCategoryChange(categoryId) {
        const contentSelect = document.getElementById('content_id');
        if (!contentSelect) return;
        
        // 清空内容选项
        contentSelect.innerHTML = '<option value="">请先选择分类</option>';
        
        if (!categoryId) return;
        
        // 加载该分类下的内容
        API.content.getByCategory(categoryId)
            .then(data => {
                if (data.code === 200) {
                    this.renderContentOptions(data.data);
                } else {
                    Toast.warning('该分类下暂无可用内容');
                }
            })
            .catch(error => {
                console.error('加载内容失败:', error);
                Toast.error('加载内容失败');
            });
    },
    
    /**
     * 渲染内容选项
     * @param {Array} contents 内容数据
     */
    renderContentOptions(contents) {
        const contentSelect = document.getElementById('content_id');
        if (!contentSelect) return;
        
        // 清空现有选项
        contentSelect.innerHTML = '<option value="">请选择内容</option>';
        
        // 添加内容选项
        contents.forEach(content => {
            if (content.status === 1) { // 只显示启用的内容
                const option = document.createElement('option');
                option.value = content.id;
                option.textContent = content.title;
                option.dataset.content = content.content;
                contentSelect.appendChild(option);
            }
        });
        
        // 绑定内容选择事件
        contentSelect.addEventListener('change', (e) => {
            this.onContentChange(e.target);
        });
    },
    
    /**
     * 内容选择变化处理
     * @param {HTMLElement} select 选择框元素
     */
    onContentChange(select) {
        const selectedOption = select.options[select.selectedIndex];
        const contentTextarea = document.getElementById('content');
        
        if (selectedOption && selectedOption.dataset.content && contentTextarea) {
            contentTextarea.value = selectedOption.dataset.content;
            this.updateCharCount();
        }
    },
    
    /**
     * 更新字符计数
     */
    updateCharCount() {
        const contentTextarea = document.getElementById('content');
        const charCountElement = document.getElementById('charCount');
        
        if (contentTextarea && charCountElement) {
            const currentLength = contentTextarea.value.length;
            const maxLength = contentTextarea.maxLength || 5000;
            
            charCountElement.textContent = `${currentLength} / ${maxLength}`;
            
            // 根据字符数量设置样式
            if (currentLength > maxLength * 0.9) {
                charCountElement.style.color = '#ff4d4f';
            } else if (currentLength > maxLength * 0.7) {
                charCountElement.style.color = '#fa8c16';
            } else {
                charCountElement.style.color = '#8c8c8c';
            }
        }
    },
    
    /**
     * 验证生成数量
     */
    validateCount() {
        const countInput = document.getElementById('count');
        if (!countInput) return true;
        
        const count = parseInt(countInput.value);
        const min = parseInt(countInput.min) || 1;
        const max = parseInt(countInput.max) || 1000;
        
        if (isNaN(count) || count < min || count > max) {
            countInput.classList.add('is-invalid');
            return false;
        } else {
            countInput.classList.remove('is-invalid');
            return true;
        }
    },
    
    /**
     * 设置默认过期时间
     */
    setDefaultExpireTime() {
        const expireInput = document.getElementById('expire_at');
        if (expireInput && !expireInput.value) {
            // 设置默认过期时间为30天后
            const defaultDate = new Date();
            defaultDate.setDate(defaultDate.getDate() + 30);
            
            // 格式化为 YYYY-MM-DDTHH:MM 格式
            const year = defaultDate.getFullYear();
            const month = String(defaultDate.getMonth() + 1).padStart(2, '0');
            const day = String(defaultDate.getDate()).padStart(2, '0');
            const hours = String(defaultDate.getHours()).padStart(2, '0');
            const minutes = String(defaultDate.getMinutes()).padStart(2, '0');
            
            expireInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
        }
    },
    
    /**
     * 验证表单
     * @returns {object}
     */
    validateForm() {
        const form = document.getElementById('generateForm');
        if (!form) return { valid: false, errors: { form: '表单不存在' } };
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        const errors = {};
        
        // 验证分类
        if (!data.category_id) {
            errors.category_id = '请选择分类';
        }
        
        // 验证内容
        if (!data.content_id) {
            errors.content_id = '请选择内容';
        }
        
        // 验证生成数量
        const count = parseInt(data.count);
        if (!count || count < 1 || count > 1000) {
            errors.count = '生成数量必须在1-1000之间';
        }
        
        // 验证过期时间（如果设置了）
        if (data.expire_at) {
            const expireDate = new Date(data.expire_at);
            const now = new Date();
            if (expireDate <= now) {
                errors.expire_at = '过期时间必须大于当前时间';
            }
        }
        
        return {
            valid: Object.keys(errors).length === 0,
            errors: errors,
            data: data
        };
    },
    
    /**
     * 提交表单
     */
    submitForm() {
        // 验证表单
        const validation = this.validateForm();
        if (!validation.valid) {
            const firstError = Object.values(validation.errors)[0];
            Toast.error(firstError);
            return;
        }
        
        // 设置加载状态
        this.setLoadingState(true);
        
        // 发送请求
        API.card.generate(validation.data)
            .then(data => {
                if (data.code === 200) {
                    Toast.success(data.message || '生成成功');
                    this.showSuccessResult(data);
                } else {
                    Toast.error(data.message || '生成失败');
                }
            })
            .catch(error => {
                console.error('生成卡密失败:', error);
                Toast.error('生成失败，请稍后重试');
            })
            .finally(() => {
                this.setLoadingState(false);
            });
    },
    
    /**
     * 显示成功结果
     * @param {object} data 响应数据
     */
    showSuccessResult(data) {
        // 询问用户是否跳转到卡密管理页面
        setTimeout(() => {
            if (Utils.confirm('生成成功！是否跳转到卡密管理页面查看？')) {
                window.location.href = '/cards';
            } else {
                this.resetForm();
            }
        }, 1500);
    },
    
    /**
     * 重置表单
     */
    resetForm() {
        if (!Utils.confirm('确定要重置表单吗？这将清空所有已填写的内容。')) {
            return;
        }
        
        const form = document.getElementById('generateForm');
        if (form) {
            form.reset();
            
            // 重置内容选择器
            const contentSelect = document.getElementById('content_id');
            if (contentSelect) {
                contentSelect.innerHTML = '<option value="">请先选择分类</option>';
            }
            
            // 重置字符计数
            this.updateCharCount();
            
            // 重置默认过期时间
            this.setDefaultExpireTime();
        }
        
        Toast.info('表单已重置');
    },
    
    /**
     * 设置加载状态
     * @param {boolean} loading 是否加载中
     */
    setLoadingState(loading) {
        const generateBtn = document.getElementById('generateBtn');
        const btnText = generateBtn?.querySelector('.btn-text');
        const loadingSpinner = generateBtn?.querySelector('.loading-spinner');
        
        if (generateBtn) {
            generateBtn.disabled = loading;
            
            if (btnText && loadingSpinner) {
                if (loading) {
                    btnText.style.display = 'none';
                    loadingSpinner.style.display = 'inline-flex';
                } else {
                    btnText.style.display = 'inline-flex';
                    loadingSpinner.style.display = 'none';
                }
            }
        }
    }
};

// 全局函数，供HTML调用
window.submitGenerate = function() {
    CardGenerateForm.submitForm();
};

window.resetForm = function() {
    CardGenerateForm.resetForm();
};

window.updateCharCount = function() {
    CardGenerateForm.updateCharCount();
};

window.onCategoryChange = function(categoryId) {
    CardGenerateForm.onCategoryChange(categoryId);
};

// 导出模块
window.CardGenerateForm = CardGenerateForm;
