/**
 * 头部布局样式
 * 统一的头部导航和布局样式
 */

/* 主头部样式 */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  height: var(--header-height);
  background: var(--white);
  border-bottom: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-6);
}

/* 头部品牌区域 */
.header-brand {
  display: flex;
  align-items: center;
  margin-right: var(--spacing-8);
}

.header-logo {
  height: 32px;
  width: auto;
  margin-right: var(--spacing-3);
}

.header-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  text-decoration: none;
  margin: 0;
}

.header-title:hover {
  color: var(--primary-color);
  text-decoration: none;
}

/* 头部导航 */
.header-nav {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: var(--spacing-6);
}

.header-nav-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--spacing-6);
}

.header-nav-item {
  position: relative;
}

.header-nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--gray-600);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
}

.header-nav-link:hover {
  color: var(--primary-color);
  background-color: var(--primary-light);
  text-decoration: none;
}

.header-nav-link.active {
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.header-nav-icon {
  margin-right: var(--spacing-2);
  font-size: var(--font-size-base);
}

/* 头部操作区域 */
.header-actions {
  display: flex;
  align-items: center;
  margin-left: auto;
  gap: var(--spacing-4);
}

/* 搜索框 */
.header-search {
  position: relative;
  width: 300px;
}

.header-search-input {
  width: 100%;
  padding: var(--spacing-2) var(--spacing-4);
  padding-left: var(--spacing-10);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-full);
  background-color: var(--gray-100);
  font-size: var(--font-size-sm);
  transition: all var(--transition-fast);
}

.header-search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: var(--white);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.header-search-icon {
  position: absolute;
  left: var(--spacing-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-500);
  font-size: var(--font-size-sm);
}

/* 通知按钮 */
.header-notification {
  position: relative;
  padding: var(--spacing-2);
  color: var(--gray-600);
  background: transparent;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.header-notification:hover {
  color: var(--primary-color);
  background-color: var(--primary-light);
}

.header-notification-badge {
  position: absolute;
  top: 0;
  right: 0;
  width: 18px;
  height: 18px;
  background-color: var(--danger-color);
  color: var(--white);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(25%, -25%);
}

/* 用户菜单 */
.header-user {
  position: relative;
}

.header-user-toggle {
  display: flex;
  align-items: center;
  padding: var(--spacing-2);
  background: transparent;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.header-user-toggle:hover {
  background-color: var(--gray-100);
}

.header-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-full);
  margin-right: var(--spacing-2);
  object-fit: cover;
}

.header-user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-right: var(--spacing-2);
}

.header-user-arrow {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
  transition: transform var(--transition-fast);
}

.header-user-toggle[aria-expanded="true"] .header-user-arrow {
  transform: rotate(180deg);
}

/* 用户下拉菜单 */
.header-user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: var(--z-dropdown);
  min-width: 200px;
  margin-top: var(--spacing-2);
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all var(--transition-fast);
}

.header-user-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.header-user-menu-header {
  padding: var(--spacing-4);
  border-bottom: var(--border-width) solid var(--border-color);
}

.header-user-menu-name {
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-1);
}

.header-user-menu-email {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.header-user-menu-body {
  padding: var(--spacing-2) 0;
}

.header-user-menu-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-4);
  color: var(--gray-700);
  text-decoration: none;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.header-user-menu-item:hover {
  background-color: var(--gray-100);
  color: var(--gray-800);
  text-decoration: none;
}

.header-user-menu-icon {
  margin-right: var(--spacing-3);
  font-size: var(--font-size-sm);
  width: 16px;
  text-align: center;
}

.header-user-menu-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-2) 0;
}

/* 移动端菜单按钮 */
.header-mobile-toggle {
  display: none;
  padding: var(--spacing-2);
  background: transparent;
  border: none;
  color: var(--gray-600);
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
}

.header-mobile-toggle:hover {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

/* 面包屑导航 */
.header-breadcrumb {
  display: flex;
  align-items: center;
  margin-left: var(--spacing-6);
  font-size: var(--font-size-sm);
}

.header-breadcrumb-item {
  color: var(--gray-600);
}

.header-breadcrumb-item + .header-breadcrumb-item::before {
  content: '/';
  margin: 0 var(--spacing-2);
  color: var(--gray-400);
}

.header-breadcrumb-link {
  color: var(--gray-600);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.header-breadcrumb-link:hover {
  color: var(--primary-color);
  text-decoration: none;
}

.header-breadcrumb-current {
  color: var(--gray-800);
  font-weight: var(--font-weight-medium);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-header {
    padding: 0 var(--spacing-4);
  }
  
  .header-nav {
    display: none;
  }
  
  .header-search {
    width: 200px;
  }
  
  .header-mobile-toggle {
    display: block;
    margin-right: var(--spacing-3);
  }
  
  .header-user-name {
    display: none;
  }
  
  .header-breadcrumb {
    display: none;
  }
}

@media (max-width: 576px) {
  .header-search {
    width: 150px;
  }
  
  .header-search-input {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-3);
    padding-left: var(--spacing-8);
  }
  
  .header-actions {
    gap: var(--spacing-2);
  }
  
  .header-notification {
    padding: var(--spacing-1);
  }
}
