/**
 * 模态框组件
 * 提供模态框的显示、隐藏和管理功能
 */

const Modal = {
    /**
     * 显示模态框
     * @param {string} modalId 模态框ID
     */
    show(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    },
    
    /**
     * 隐藏模态框
     * @param {string} modalId 模态框ID
     */
    hide(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    },
    
    /**
     * 重置表单
     * @param {string} formId 表单ID
     */
    resetForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.reset();
        }
    },
    
    /**
     * 设置按钮加载状态
     * @param {string} buttonSelector 按钮选择器
     * @param {boolean} loading 是否加载中
     */
    setButtonLoading(buttonSelector, loading) {
        const button = document.querySelector(buttonSelector);
        if (!button) return;
        
        const btnText = button.querySelector('.btn-text');
        const spinner = button.querySelector('.loading-spinner');
        
        if (loading) {
            button.disabled = true;
            if (btnText) btnText.style.display = 'none';
            if (spinner) spinner.style.display = 'inline-block';
        } else {
            button.disabled = false;
            if (btnText) btnText.style.display = 'inline-block';
            if (spinner) spinner.style.display = 'none';
        }
    },
    
    /**
     * 填充表单数据
     * @param {object} data 数据对象
     * @param {string} prefix 字段前缀
     */
    fillForm(data, prefix = '') {
        Object.keys(data).forEach(key => {
            const fieldId = prefix ? `${prefix}_${key}` : key;
            const element = document.getElementById(fieldId);
            
            if (element) {
                if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                    element.value = data[key] || '';
                } else if (element.tagName === 'SELECT') {
                    element.value = data[key] || '';
                } else {
                    element.textContent = data[key] || '';
                }
            }
        });
    }
};

// 导出到全局
window.Modal = Modal;
