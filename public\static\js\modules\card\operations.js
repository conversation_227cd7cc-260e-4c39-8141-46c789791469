/**
 * 卡密操作功能模块
 * 处理卡密的各种操作：删除、状态切换、批量操作等
 */

const CardOperations = {
    /**
     * 删除单个卡密
     * @param {number} cardId 卡密ID
     */
    delete(cardId) {
        if (!cardId) {
            Toast.error('卡密ID不能为空');
            return;
        }
        
        if (!Utils.confirm('确定要删除此卡密吗？删除后无法恢复！')) {
            return;
        }
        
        API.card.delete({ id: cardId })
            .then(data => {
                if (data.code === 200) {
                    Toast.success('删除成功');
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '删除失败');
                }
            })
            .catch(error => {
                console.error('删除错误:', error);
                Toast.error('删除失败，请稍后重试');
            });
    },
    
    /**
     * 切换卡密状态
     * @param {number} cardId 卡密ID
     * @param {number} currentStatus 当前状态
     */
    toggleStatus(cardId, currentStatus) {
        if (!cardId) {
            Toast.error('卡密ID不能为空');
            return;
        }
        
        // 计算新状态：0=未使用 <-> 2=禁用
        const newStatus = currentStatus === Config.cardStatus.UNUSED ? 
                         Config.cardStatus.DISABLED : 
                         Config.cardStatus.UNUSED;
        
        const actionText = newStatus === Config.cardStatus.DISABLED ? '禁用' : '启用';
        
        if (!Utils.confirm(`确定要${actionText}此卡密吗？`)) {
            return;
        }
        
        API.card.updateStatus({ id: cardId, status: newStatus })
            .then(data => {
                if (data.code === 200) {
                    Toast.success(`${actionText}成功`);
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || `${actionText}失败`);
                }
            })
            .catch(error => {
                console.error('状态更新错误:', error);
                Toast.error(`${actionText}失败，请稍后重试`);
            });
    },
    
    /**
     * 批量删除卡密
     * @param {Array} cardIds 卡密ID数组
     */
    batchDelete(cardIds = null) {
        const ids = cardIds || Table.getSelectedIds();
        
        if (!ids || ids.length === 0) {
            Toast.warning('请选择要删除的卡密');
            return;
        }
        
        if (!Utils.confirm(`确定要删除选中的 ${ids.length} 个卡密吗？删除后无法恢复！`)) {
            return;
        }
        
        API.card.batchDelete({ ids: ids })
            .then(data => {
                if (data.code === 200) {
                    Toast.success(`成功删除 ${ids.length} 个卡密`);
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '批量删除失败');
                }
            })
            .catch(error => {
                console.error('批量删除错误:', error);
                Toast.error('批量删除失败，请稍后重试');
            });
    },
    
    /**
     * 批量更新状态
     * @param {number} status 新状态
     * @param {Array} cardIds 卡密ID数组
     */
    batchUpdateStatus(status, cardIds = null) {
        const ids = cardIds || Table.getSelectedIds();
        
        if (!ids || ids.length === 0) {
            Toast.warning('请选择要操作的卡密');
            return;
        }
        
        const statusText = Utils.getStatusText(status);
        
        if (!Utils.confirm(`确定要将选中的 ${ids.length} 个卡密设置为"${statusText}"吗？`)) {
            return;
        }
        
        // 批量更新状态（需要后端支持）
        const promises = ids.map(id => API.card.updateStatus({ id: id, status: status }));
        
        Promise.all(promises)
            .then(results => {
                const successCount = results.filter(result => result.code === 200).length;
                const failCount = results.length - successCount;
                
                if (failCount === 0) {
                    Toast.success(`成功更新 ${successCount} 个卡密状态`);
                } else {
                    Toast.warning(`成功更新 ${successCount} 个，失败 ${failCount} 个`);
                }
                
                setTimeout(() => {
                    Utils.reload();
                }, 1000);
            })
            .catch(error => {
                console.error('批量更新状态错误:', error);
                Toast.error('批量更新状态失败，请稍后重试');
            });
    },
    
    /**
     * 导出卡密
     * @param {Array} cardIds 卡密ID数组
     */
    export(cardIds = null) {
        const ids = cardIds || Table.getSelectedIds();
        
        if (!ids || ids.length === 0) {
            Toast.warning('请选择要导出的卡密');
            return;
        }
        
        // 创建导出表单
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/cards/export';
        form.style.display = 'none';
        
        // 添加卡密ID
        ids.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'ids[]';
            input.value = id;
            form.appendChild(input);
        });
        
        // 添加到页面并提交
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        
        Toast.info(`正在导出 ${ids.length} 个卡密...`);
    },
    
    /**
     * 初始化操作功能
     */
    init() {
        // 绑定全选/取消全选
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                Table.toggleSelectAll(this);
            });
        }
        
        // 绑定单项选择
        const itemCheckboxes = document.querySelectorAll('.item-checkbox');
        itemCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                Table.toggleSelectItem();
            });
        });
        
        // 绑定批量操作按钮
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', () => this.batchDelete());
        }
        
        const batchDisableBtn = document.getElementById('batchDisableBtn');
        if (batchDisableBtn) {
            batchDisableBtn.addEventListener('click', () => 
                this.batchUpdateStatus(Config.cardStatus.DISABLED));
        }
        
        const batchEnableBtn = document.getElementById('batchEnableBtn');
        if (batchEnableBtn) {
            batchEnableBtn.addEventListener('click', () => 
                this.batchUpdateStatus(Config.cardStatus.UNUSED));
        }
        
        const exportBtn = document.getElementById('exportBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.export());
        }
        
        // 初始化按钮状态
        Table.updateBatchButtons();
    }
};

// 全局函数，供HTML调用
window.deleteCard = function(cardId) {
    CardOperations.delete(cardId);
};

window.toggleCardStatus = function(cardId, currentStatus) {
    CardOperations.toggleStatus(cardId, currentStatus);
};

window.batchAction = function(action) {
    switch (action) {
        case 'delete':
            CardOperations.batchDelete();
            break;
        case 'disable':
            CardOperations.batchUpdateStatus(Config.cardStatus.DISABLED);
            break;
        case 'enable':
            CardOperations.batchUpdateStatus(Config.cardStatus.UNUSED);
            break;
        case 'export':
            CardOperations.export();
            break;
        default:
            Toast.warning('未知的批量操作');
    }
};

// 导出模块
window.CardOperations = CardOperations;
