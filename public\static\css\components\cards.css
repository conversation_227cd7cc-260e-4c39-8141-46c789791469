/**
 * 卡片组件样式
 * 统一的卡片样式系统
 */

/* 基础卡片样式 */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--white);
  background-clip: border-box;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* 卡片头部 */
.card-header {
  padding: var(--spacing-4) var(--spacing-5);
  margin-bottom: 0;
  background-color: var(--gray-100);
  border-bottom: var(--border-width) solid var(--border-color);
  border-top-left-radius: var(--border-radius-lg);
  border-top-right-radius: var(--border-radius-lg);
}

.card-header:first-child {
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.card-header + .list-group .list-group-item:first-child {
  border-top: 0;
}

/* 卡片主体 */
.card-body {
  flex: 1 1 auto;
  padding: var(--spacing-5);
}

.card-title {
  margin-bottom: var(--spacing-3);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
}

.card-subtitle {
  margin-top: calc(var(--spacing-3) * -0.5);
  margin-bottom: 0;
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link:hover {
  text-decoration: none;
}

.card-link + .card-link {
  margin-left: var(--spacing-4);
}

/* 卡片底部 */
.card-footer {
  padding: var(--spacing-4) var(--spacing-5);
  background-color: var(--gray-100);
  border-top: var(--border-width) solid var(--border-color);
  border-bottom-right-radius: var(--border-radius-lg);
  border-bottom-left-radius: var(--border-radius-lg);
}

.card-footer:last-child {
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* 卡片图片 */
.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: var(--border-radius-lg);
  border-top-right-radius: var(--border-radius-lg);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: var(--border-radius-lg);
  border-bottom-left-radius: var(--border-radius-lg);
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: var(--spacing-4);
  border-radius: var(--border-radius-lg);
}

/* 现代卡片样式 */
.modern-card {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.modern-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.modern-card-header {
  padding: var(--spacing-5) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modern-card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.modern-card-body {
  padding: var(--spacing-6);
}

.modern-card-footer {
  padding: var(--spacing-4) var(--spacing-6);
  background: var(--gray-50);
  border-top: var(--border-width) solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 统计卡片 */
.stats-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-5);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

.stats-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
  line-height: 1;
}

.stats-label {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-2);
}

.stats-trend {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.stats-trend.positive {
  color: var(--success-color);
}

.stats-trend.negative {
  color: var(--danger-color);
}

/* 信息卡片 */
.info-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-5);
  box-shadow: var(--shadow-sm);
  border-left: 4px solid var(--primary-color);
  transition: all var(--transition-normal);
}

.info-card:hover {
  box-shadow: var(--shadow);
}

.info-card.info-card-success {
  border-left-color: var(--success-color);
}

.info-card.info-card-warning {
  border-left-color: var(--warning-color);
}

.info-card.info-card-danger {
  border-left-color: var(--danger-color);
}

.info-card.info-card-info {
  border-left-color: var(--info-color);
}

.info-card-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: var(--white);
  margin-bottom: var(--spacing-3);
}

.info-card-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
}

.info-card-text {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  line-height: var(--line-height-relaxed);
}

/* 操作卡片 */
.action-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  text-decoration: none;
  color: inherit;
  transition: all var(--transition-normal);
  display: block;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
  color: inherit;
}

.action-card-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white);
  margin-bottom: var(--spacing-4);
  background: var(--primary-color);
}

.action-card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
}

.action-card-description {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  line-height: var(--line-height-relaxed);
}

/* 卡片组 */
.card-group {
  display: flex;
  flex-direction: column;
}

.card-group > .card {
  margin-bottom: var(--spacing-4);
}

@media (min-width: 576px) {
  .card-group {
    flex-direction: row;
  }
  
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

/* 卡片网格 */
.card-grid {
  display: grid;
  gap: var(--spacing-6);
  grid-template-columns: 1fr;
}

@media (min-width: 576px) {
  .card-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 768px) {
  .card-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 992px) {
  .card-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 紧凑卡片 */
.card-compact {
  padding: var(--spacing-4);
}

.card-compact .card-header {
  padding: var(--spacing-3) var(--spacing-4);
}

.card-compact .card-body {
  padding: var(--spacing-4);
}

.card-compact .card-footer {
  padding: var(--spacing-3) var(--spacing-4);
}

/* 无边框卡片 */
.card-borderless {
  border: none;
  box-shadow: none;
}

/* 透明卡片 */
.card-transparent {
  background-color: transparent;
  border: none;
  box-shadow: none;
}

/* 现代化统计卡片 */
.modern-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.modern-stats-card {
  background: linear-gradient(135deg, var(--white) 0%, rgba(255, 255, 255, 0.8) 100%);
  border-radius: var(--border-radius-xl);
  padding: 2rem;
  box-shadow: var(--shadow);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modern-stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.modern-stats-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.modern-stats-icon {
  width: 3rem;
  height: 3rem;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: white;
  margin: 0 auto 1rem auto;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
}

.modern-stats-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-800);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.modern-stats-label {
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
}

.modern-stats-trend {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  margin-top: 0.5rem;
}

.modern-stats-trend.positive {
  color: var(--success-color);
}

.modern-stats-trend.negative {
  color: var(--danger-color);
}
