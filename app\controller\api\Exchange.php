<?php

namespace app\controller\api;

use app\BaseController;
use app\model\Card;
use app\model\Category;
use app\model\UsageRecord;
use think\Response;

/**
 * 用户端兑换API控制器
 */
class Exchange extends BaseController
{
    /**
     * 卡密兑换
     */
    public function redeem()
    {
        try {
            // 获取POST参数
            $input = $this->request->getInput();
            $data = json_decode($input, true);
            $cardCode = $data['card_code'] ?? $this->request->post('card_code', '');

            // 参数验证
            if (empty($cardCode)) {
                return $this->error('请输入卡密');
            }

            // 清理卡密编号（去除空格和特殊字符）
            $cardCode = strtoupper(trim($cardCode));

            // 基础格式验证
            if (strlen($cardCode) < 6 || strlen($cardCode) > 50) {
                return $this->error('卡密格式不正确');
            }

            // IP限制检查（可选）
            $clientIp = $this->request->ip();
            if ($this->checkIpLimit($clientIp)) {
                return $this->error('操作过于频繁，请稍后再试');
            }

            // 查找卡密
            $card = Card::where('card_code', $cardCode)->find();

            if (!$card) {
                return $this->error('卡密不存在或已失效');
            }

            // 检查卡密状态
            if ($card->status == Card::STATUS_USED) {
                return $this->error('卡密已被使用');
            }

            if ($card->status == Card::STATUS_DISABLED) {
                return $this->error('卡密已被禁用');
            }

            // 检查是否过期
            if ($card->expire_at && strtotime($card->expire_at) < time()) {
                return $this->error('卡密已过期');
            }

            // 获取分类信息
            $category = Category::find($card->category_id);
            if (!$category || $category->status != Category::STATUS_ENABLED) {
                return $this->error('卡密分类不可用');
            }

            // 开始事务
            \think\facade\Db::startTrans();

            try {
                // 更新卡密状态
                $card->status = Card::STATUS_USED;
                $card->used_at = date('Y-m-d H:i:s');
                $card->used_ip = $clientIp;
                $card->save();

                // 创建使用记录
                UsageRecord::createRecord(
                    $card->id,
                    $card->card_code,
                    $card->category_id,
                    $clientIp,
                    $this->request->header('user-agent')
                );

                // 提交事务
                \think\facade\Db::commit();

                return $this->success('兑换成功', [
                    'card_code' => $card->masked_card_code,
                    'category' => $category->name,
                    'content' => $card->content,
                    'used_at' => $card->used_at
                ]);

            } catch (\Exception $e) {
                \think\facade\Db::rollback();
                return $this->error('兑换失败，请稍后重试');
            }

        } catch (\Exception $e) {
            return $this->error('系统错误，请稍后重试');
        }
    }

    /**
     * 检查IP限制
     */
    private function checkIpLimit($ip)
    {
        // 检查同一IP在短时间内的请求次数
        $cacheKey = 'ip_limit_' . md5($ip);
        $requests = cache($cacheKey) ?: 0;

        if ($requests >= 10) { // 每分钟最多10次请求
            return true;
        }

        cache($cacheKey, $requests + 1, 60); // 缓存1分钟
        return false;
    }
    
    /**
     * 查询兑换记录
     */
    public function records()
    {
        try {
            $cardCode = $this->request->param('card_code', '');

            if (empty($cardCode)) {
                return $this->error('请输入卡密');
            }

            // 清理卡密编号
            $cardCode = strtoupper(trim($cardCode));

            // 查询使用记录
            $records = UsageRecord::alias('ur')
                ->leftJoin('km_categories c', 'ur.category_id = c.id')
                ->leftJoin('km_cards card', 'ur.card_id = card.id')
                ->field([
                    'ur.card_code',
                    'ur.used_at',
                    'ur.used_ip',
                    'c.name as category_name',
                    'card.content',
                    'card.expire_at'
                ])
                ->where('ur.card_code', $cardCode)
                ->order('ur.used_at', 'desc')
                ->select();

            if ($records->isEmpty()) {
                return $this->error('未找到兑换记录');
            }

            // 处理数据
            $result = [];
            foreach ($records as $record) {
                $result[] = [
                    'card_code' => $record['card_code'],
                    'masked_card_code' => $this->maskCardCode($record['card_code']),
                    'category_name' => $record['category_name'],
                    'content' => $record['content'],
                    'used_at' => $record['used_at'],
                    'used_ip' => $record['used_ip'],
                    'expire_at' => $record['expire_at']
                ];
            }

            return $this->success('查询成功', $result);

        } catch (\Exception $e) {
            return $this->error('查询失败，请稍后重试');
        }
    }

    /**
     * 卡密掩码处理
     */
    private function maskCardCode($cardCode)
    {
        if (strlen($cardCode) <= 8) {
            return $cardCode;
        }
        return substr($cardCode, 0, 4) . '****' . substr($cardCode, -4);
    }
    
    /**
     * 获取系统信息
     */
    public function info()
    {
        try {
            return $this->success('获取成功', [
                'system_name' => '红嘴鸥教育',
                'title' => '电子资料兑换系统',
                'description' => '此为电子版资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888',
                'contact' => 'hzoedu888',
                'version' => '1.0.0'
            ]);
        } catch (\Exception $e) {
            return $this->error('获取系统信息失败');
        }
    }
    
    /**
     * 成功响应
     */
    private function success($message = 'success', $data = [])
    {
        return json([
            'code' => 200,
            'message' => $message,
            'data' => $data,
            'timestamp' => time()
        ]);
    }
    
    /**
     * 错误响应
     */
    private function error($message = 'error', $code = 400)
    {
        return json([
            'code' => $code,
            'message' => $message,
            'data' => null,
            'timestamp' => time()
        ]);
    }
}
