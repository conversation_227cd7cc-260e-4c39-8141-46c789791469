/**
 * 分类树组件
 * 处理分类树的展示、选择和交互功能
 */

const CategoryTree = {
    selectedCategoryId: null,
    
    /**
     * 初始化分类树
     */
    init() {
        this.bindEvents();
        this.initTreeState();
    },

    /**
     * 初始化树形状态
     */
    initTreeState() {
        // 默认收起所有分类，确保初始状态正确
        document.querySelectorAll('.tree-node').forEach(node => {
            const childrenContainer = node.querySelector('.tree-children');
            const expandBtn = node.querySelector('.tree-expand-btn');

            if (childrenContainer && !expandBtn.classList.contains('no-children')) {
                childrenContainer.style.display = 'none';
                childrenContainer.classList.remove('expanded');
                expandBtn.classList.remove('expanded');
            }
        });
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 使用事件委托绑定树形节点点击事件
        document.addEventListener('click', (e) => {
            // 处理展开/收起按钮点击
            if (e.target.closest('.tree-expand-btn')) {
                e.stopPropagation();
                const btn = e.target.closest('.tree-expand-btn');

                // 检查是否有子分类
                if (btn.classList.contains('no-children')) {
                    return;
                }

                const node = btn.closest('.tree-node');
                if (node) {
                    const categoryId = node.dataset.id;
                    this.toggleTreeNode(categoryId);
                }
                return;
            }

            // 处理树形节点内容点击
            if (e.target.closest('.tree-node-content')) {
                const content = e.target.closest('.tree-node-content');
                const node = content.closest('.tree-node');
                if (node) {
                    const categoryId = node.dataset.id;
                    this.selectCategory(categoryId);
                }
                return;
            }
        });
    },
    
    /**
     * 选择分类
     * @param {number} id 分类ID
     */
    selectCategory(id) {
        // 移除之前的选中状态
        document.querySelectorAll('.tree-node-content.selected').forEach(content => {
            content.classList.remove('selected');
        });

        // 添加新的选中状态
        const node = document.querySelector(`.tree-node[data-id="${id}"]`);
        if (node) {
            const content = node.querySelector('.tree-node-content');
            if (content) {
                content.classList.add('selected');
            }
        }

        this.selectedCategoryId = id;
    },

    /**
     * 切换树形节点展开/收起
     * @param {number} id 分类ID
     */
    toggleTreeNode(id) {
        const node = document.querySelector(`.tree-node[data-id="${id}"]`);
        if (!node) return;

        const expandBtn = node.querySelector('.tree-expand-btn');
        const childrenContainer = node.querySelector('.tree-children');

        if (!childrenContainer || expandBtn.classList.contains('no-children')) {
            // 如果没有子分类，只选中当前节点
            this.selectCategory(id);
            return;
        }

        const isExpanded = expandBtn.classList.contains('expanded');

        if (isExpanded) {
            // 收起
            childrenContainer.classList.remove('expanded');
            childrenContainer.style.display = 'none';
            expandBtn.classList.remove('expanded');
        } else {
            // 展开
            childrenContainer.classList.add('expanded');
            childrenContainer.style.display = 'block';
            expandBtn.classList.add('expanded');
        }
    },
    
    /**
     * 切换分类展开/收起（一次只能展开一个）
     * @param {number} id 分类ID
     */
    toggleCategory(id) {
        const currentRow = document.querySelector(`[data-id="${id}"]`);
        const arrow = currentRow.querySelector('.expand-arrow');
        const children = document.querySelectorAll(`[data-parent="${id}"]`);
        
        if (children.length === 0) {
            // 如果没有子分类，只选中当前行
            this.selectCategory(id);
            return;
        }
        
        const isExpanded = arrow.classList.contains('expanded');
        
        if (isExpanded) {
            // 收起：隐藏所有子分类
            this.hideChildren(id);
            arrow.classList.remove('expanded');
            arrow.classList.remove('fa-chevron-down');
            arrow.classList.add('fa-chevron-right');
        } else {
            // 先收起所有其他展开的分类
            this.collapseAll();
            
            // 展开：显示直接子分类
            children.forEach(child => {
                if (child.dataset.parent === id) {
                    child.style.display = 'table-row';
                }
            });
            
            arrow.classList.add('expanded');
            arrow.classList.remove('fa-chevron-right');
            arrow.classList.add('fa-chevron-down');
        }
        
        // 选中当前分类
        this.selectCategory(id);
    },
    
    /**
     * 隐藏指定分类的所有子分类
     * @param {number} parentId 父分类ID
     */
    hideChildren(parentId) {
        const children = document.querySelectorAll(`[data-parent="${parentId}"]`);
        children.forEach(child => {
            child.style.display = 'none';
            
            // 递归隐藏子分类的子分类
            const childId = child.dataset.id;
            this.hideChildren(childId);
            
            // 重置展开状态
            const arrow = child.querySelector('.expand-arrow');
            if (arrow) {
                arrow.classList.remove('expanded');
                arrow.classList.remove('fa-chevron-down');
                arrow.classList.add('fa-chevron-right');
            }
        });
    },
    
    /**
     * 收起所有分类
     */
    collapseAll() {
        // 收起所有树形节点
        document.querySelectorAll('.tree-node').forEach(node => {
            const expandBtn = node.querySelector('.tree-expand-btn');
            const childrenContainer = node.querySelector('.tree-children');

            if (childrenContainer && !expandBtn.classList.contains('no-children')) {
                childrenContainer.style.display = 'none';
                childrenContainer.classList.remove('expanded');
                expandBtn.classList.remove('expanded');
            }
        });
    },

    /**
     * 展开所有分类
     */
    expandAll() {
        // 展开所有树形节点
        document.querySelectorAll('.tree-node').forEach(node => {
            const expandBtn = node.querySelector('.tree-expand-btn');
            const childrenContainer = node.querySelector('.tree-children');

            if (childrenContainer && !expandBtn.classList.contains('no-children')) {
                childrenContainer.style.display = 'block';
                childrenContainer.classList.add('expanded');
                expandBtn.classList.add('expanded');
            }
        });
    },
    
    /**
     * 展开到指定分类
     * @param {number} categoryId 分类ID
     */
    expandToCategory(categoryId) {
        const targetRow = document.querySelector(`[data-id="${categoryId}"]`);
        if (!targetRow) return;
        
        // 获取所有父级分类
        const parents = this.getParentCategories(categoryId);
        
        // 展开所有父级分类
        parents.forEach(parentId => {
            const parentRow = document.querySelector(`[data-id="${parentId}"]`);
            const arrow = parentRow.querySelector('.expand-arrow');
            const children = document.querySelectorAll(`[data-parent="${parentId}"]`);
            
            // 显示直接子分类
            children.forEach(child => {
                if (child.dataset.parent === parentId) {
                    child.style.display = 'table-row';
                }
            });
            
            // 更新箭头状态
            if (arrow) {
                arrow.classList.add('expanded');
                arrow.classList.remove('fa-chevron-right');
                arrow.classList.add('fa-chevron-down');
            }
        });
        
        // 选中目标分类
        this.selectCategory(categoryId);
    },
    
    /**
     * 获取指定分类的所有父级分类ID
     * @param {number} categoryId 分类ID
     * @returns {Array} 父级分类ID数组
     */
    getParentCategories(categoryId) {
        const parents = [];
        const targetRow = document.querySelector(`[data-id="${categoryId}"]`);
        if (!targetRow) return parents;
        
        let currentParentId = targetRow.dataset.parent;
        while (currentParentId && currentParentId !== '0') {
            parents.unshift(currentParentId);
            const parentRow = document.querySelector(`[data-id="${currentParentId}"]`);
            if (parentRow) {
                currentParentId = parentRow.dataset.parent;
            } else {
                break;
            }
        }
        
        return parents;
    },
    
    /**
     * 获取选中的分类ID
     * @returns {number|null}
     */
    getSelectedCategoryId() {
        return this.selectedCategoryId;
    },
    
    /**
     * 构建分类树数据结构
     * @param {Array} categories 分类数组
     * @returns {Array} 树形结构数据
     */
    buildTree(categories) {
        const tree = [];
        const map = {};
        
        // 创建映射
        categories.forEach(category => {
            map[category.id] = { ...category, children: [] };
        });
        
        // 构建树形结构
        categories.forEach(category => {
            if (category.parent_id === 0) {
                tree.push(map[category.id]);
            } else if (map[category.parent_id]) {
                map[category.parent_id].children.push(map[category.id]);
            }
        });
        
        return tree;
    },
    
    /**
     * 渲染分类树选择器
     * @param {Array} categories 分类数据
     * @param {string} containerId 容器ID
     * @param {number} selectedId 选中的分类ID
     */
    renderTreeSelector(categories, containerId, selectedId = null) {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        // 清空容器
        container.innerHTML = '';
        
        // 添加顶级分类选项
        const topItem = document.createElement('div');
        topItem.className = `tree-item ${selectedId == 0 ? 'selected' : ''}`;
        topItem.dataset.id = '0';
        topItem.innerHTML = `
            <i class="fas fa-home text-primary"></i>
            <span>顶级分类</span>
        `;
        topItem.addEventListener('click', () => {
            this.selectTreeCategory(0, '顶级分类');
        });
        container.appendChild(topItem);
        
        // 构建树形结构
        const tree = this.buildTree(categories);
        this.renderTreeItems(tree, container, selectedId);
    },
    
    /**
     * 渲染树形项目
     * @param {Array} items 树形项目
     * @param {HTMLElement} container 容器元素
     * @param {number} selectedId 选中的分类ID
     * @param {number} level 层级
     */
    renderTreeItems(items, container, selectedId = null, level = 0) {
        items.forEach(item => {
            const treeItem = document.createElement('div');
            treeItem.className = `tree-item level-${level + 1} ${selectedId == item.id ? 'selected' : ''}`;
            treeItem.dataset.id = item.id;
            
            const indent = '　'.repeat(level);
            const hasChildren = item.children && item.children.length > 0;
            
            treeItem.innerHTML = `
                ${hasChildren ? `<i class="fas fa-chevron-right expand-icon"></i>` : '<span class="expand-icon-placeholder"></span>'}
                <i class="fas fa-folder text-warning"></i>
                <span>${indent}${item.name}</span>
            `;
            
            // 绑定点击事件
            treeItem.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectTreeCategory(item.id, item.name);
            });
            
            // 绑定展开事件
            if (hasChildren) {
                const expandIcon = treeItem.querySelector('.expand-icon');
                expandIcon.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleTreeNode(expandIcon, item.children, container, selectedId, level + 1);
                });
            }
            
            container.appendChild(treeItem);
        });
    },
    
    /**
     * 选择树形分类
     * @param {number} categoryId 分类ID
     * @param {string} categoryName 分类名称
     */
    selectTreeCategory(categoryId, categoryName) {
        // 更新隐藏字段
        const hiddenInput = document.getElementById('modal_parent_id');
        if (hiddenInput) {
            hiddenInput.value = categoryId;
        }
        
        // 更新显示文本
        const selectedText = document.querySelector('.selected-text');
        if (selectedText) {
            selectedText.textContent = categoryName;
        }
        
        // 更新选中状态
        document.querySelectorAll('.tree-item').forEach(item => {
            item.classList.remove('selected');
        });
        
        const selectedItem = document.querySelector(`[data-id="${categoryId}"]`);
        if (selectedItem) {
            selectedItem.classList.add('selected');
        }
        
        // 关闭下拉框
        this.closeTreeSelector();
    },
    
    /**
     * 切换树节点展开状态
     * @param {HTMLElement} expandIcon 展开图标
     * @param {Array} children 子项目
     * @param {HTMLElement} container 容器
     * @param {number} selectedId 选中ID
     * @param {number} level 层级
     */
    toggleTreeNode(expandIcon, children, container, selectedId, level) {
        const isExpanded = expandIcon.classList.contains('fa-chevron-down');
        
        if (isExpanded) {
            // 收起
            expandIcon.classList.remove('fa-chevron-down');
            expandIcon.classList.add('fa-chevron-right');
            
            // 移除子项目
            const treeItem = expandIcon.closest('.tree-item');
            let nextSibling = treeItem.nextElementSibling;
            while (nextSibling && nextSibling.classList.contains(`level-${level}`)) {
                const toRemove = nextSibling;
                nextSibling = nextSibling.nextElementSibling;
                toRemove.remove();
            }
        } else {
            // 展开
            expandIcon.classList.remove('fa-chevron-right');
            expandIcon.classList.add('fa-chevron-down');
            
            // 添加子项目
            const treeItem = expandIcon.closest('.tree-item');
            const tempContainer = document.createElement('div');
            this.renderTreeItems(children, tempContainer, selectedId, level);
            
            // 插入子项目
            let nextSibling = treeItem.nextElementSibling;
            while (tempContainer.firstChild) {
                container.insertBefore(tempContainer.firstChild, nextSibling);
            }
        }
    },
    
    /**
     * 打开树形选择器
     */
    openTreeSelector() {
        const dropdown = document.getElementById('treeSelectorDropdown');
        const input = document.querySelector('.tree-selector-input');
        
        if (dropdown) {
            dropdown.style.display = 'block';
        }
        if (input) {
            input.classList.add('active');
        }
    },
    
    /**
     * 关闭树形选择器
     */
    closeTreeSelector() {
        const dropdown = document.getElementById('treeSelectorDropdown');
        const input = document.querySelector('.tree-selector-input');
        
        if (dropdown) {
            dropdown.style.display = 'none';
        }
        if (input) {
            input.classList.remove('active');
        }
    }
};

// 全局函数，供HTML调用
window.selectCategory = function(id) {
    CategoryTree.selectCategory(id);
};

window.toggleCategory = function(id) {
    CategoryTree.toggleCategory(id);
};

window.toggleTreeNode = function(id) {
    CategoryTree.toggleTreeNode(id);
};

window.selectCategory = function(id) {
    CategoryTree.selectCategory(id);
};

window.selectTreeCategory = function(categoryId, categoryName) {
    CategoryTree.selectTreeCategory(categoryId, categoryName);
};

// 导出模块
window.CategoryTree = CategoryTree;
