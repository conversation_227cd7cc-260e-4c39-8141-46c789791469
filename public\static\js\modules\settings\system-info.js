/**
 * 系统信息组件
 * 处理系统信息的加载和显示
 */

const SystemInfo = {
    /**
     * 初始化系统信息功能
     */
    init() {
        this.loadSystemInfo();
        this.bindEvents();
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 绑定刷新按钮事件
        const refreshBtn = document.querySelector('[data-action="refresh-system-info"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.refreshSystemInfo();
            });
        }
        
        // 监听面板切换事件，当切换到系统面板时刷新信息
        document.addEventListener('panelChange', (e) => {
            if (e.detail.panelName === 'system') {
                this.loadSystemInfo();
            }
        });
    },
    
    /**
     * 加载系统信息
     */
    loadSystemInfo() {
        const container = document.getElementById('systemInfo');
        if (!container) return;
        
        // 显示加载状态
        this.showLoading(container);
        
        // 发送请求获取系统信息
        API.settings.getSystemInfo()
            .then(data => {
                if (data.code === 200) {
                    this.displaySystemInfo(data.data);
                } else {
                    this.showError(container, data.message || '获取系统信息失败');
                }
            })
            .catch(error => {
                console.error('加载系统信息失败:', error);
                this.showError(container, '获取系统信息失败，请稍后重试');
            });
    },
    
    /**
     * 刷新系统信息
     */
    refreshSystemInfo() {
        Toast.info('正在刷新系统信息...');
        this.loadSystemInfo();
    },
    
    /**
     * 显示加载状态
     * @param {HTMLElement} container 容器元素
     */
    showLoading(container) {
        container.innerHTML = `
            <div class="loading-container">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                </div>
                <div class="loading-text">正在加载系统信息...</div>
            </div>
        `;
    },
    
    /**
     * 显示错误信息
     * @param {HTMLElement} container 容器元素
     * @param {string} message 错误消息
     */
    showError(container, message) {
        container.innerHTML = `
            <div class="error-container">
                <div class="error-icon">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                </div>
                <div class="error-message">${message}</div>
                <button class="btn btn-outline btn-sm" onclick="SystemInfo.loadSystemInfo()">
                    <i class="fas fa-redo"></i> 重试
                </button>
            </div>
        `;
    },
    
    /**
     * 显示系统信息
     * @param {object} data 系统信息数据
     */
    displaySystemInfo(data) {
        const container = document.getElementById('systemInfo');
        if (!container) return;
        
        const html = `
            <div class="info-section">
                <h4><i class="fas fa-server"></i> 服务器信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>操作系统</label>
                        <span>${data.server?.os || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>PHP版本</label>
                        <span>${data.server?.php_version || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>Web服务器</label>
                        <span>${data.server?.web_server || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>服务器时间</label>
                        <span>${data.server?.server_time || 'Unknown'}</span>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <h4><i class="fas fa-database"></i> 数据库信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>数据库类型</label>
                        <span>${data.database?.type || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>数据库版本</label>
                        <span>${data.database?.version || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>连接状态</label>
                        <span class="status-badge ${data.database?.status === 'connected' ? 'status-success' : 'status-error'}">
                            ${data.database?.status === 'connected' ? '已连接' : '连接失败'}
                        </span>
                    </div>
                    <div class="info-item">
                        <label>数据库大小</label>
                        <span>${data.database?.size || 'Unknown'}</span>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <h4><i class="fas fa-chart-line"></i> 系统状态</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>CPU使用率</label>
                        <span>${data.system?.cpu_usage || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>内存使用率</label>
                        <span>${data.system?.memory_usage || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>磁盘使用率</label>
                        <span>${data.system?.disk_usage || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>系统负载</label>
                        <span>${data.system?.load_average || 'Unknown'}</span>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <h4><i class="fas fa-cog"></i> 应用信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>应用版本</label>
                        <span>${data.app?.version || '1.0.0'}</span>
                    </div>
                    <div class="info-item">
                        <label>框架版本</label>
                        <span>${data.app?.framework_version || 'Unknown'}</span>
                    </div>
                    <div class="info-item">
                        <label>运行模式</label>
                        <span class="status-badge ${data.app?.debug_mode ? 'status-warning' : 'status-success'}">
                            ${data.app?.debug_mode ? '调试模式' : '生产模式'}
                        </span>
                    </div>
                    <div class="info-item">
                        <label>缓存状态</label>
                        <span class="status-badge ${data.app?.cache_enabled ? 'status-success' : 'status-error'}">
                            ${data.app?.cache_enabled ? '已启用' : '已禁用'}
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="info-section">
                <h4><i class="fas fa-chart-bar"></i> 统计信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>总卡密数量</label>
                        <span class="text-primary">${data.stats?.total_cards || 0}</span>
                    </div>
                    <div class="info-item">
                        <label>已使用卡密</label>
                        <span class="text-success">${data.stats?.used_cards || 0}</span>
                    </div>
                    <div class="info-item">
                        <label>未使用卡密</label>
                        <span class="text-warning">${data.stats?.unused_cards || 0}</span>
                    </div>
                    <div class="info-item">
                        <label>过期卡密</label>
                        <span class="text-danger">${data.stats?.expired_cards || 0}</span>
                    </div>
                </div>
            </div>
            
            <div class="info-actions">
                <button class="btn btn-outline btn-sm" onclick="SystemInfo.refreshSystemInfo()">
                    <i class="fas fa-sync-alt"></i> 刷新信息
                </button>
                <button class="btn btn-outline btn-sm" onclick="SystemInfo.exportSystemInfo()">
                    <i class="fas fa-download"></i> 导出报告
                </button>
                <button class="btn btn-outline btn-sm" onclick="SystemInfo.checkSystemHealth()">
                    <i class="fas fa-heartbeat"></i> 系统检测
                </button>
            </div>
        `;
        
        container.innerHTML = html;
        
        // 添加动画效果
        this.animateInfoItems();
    },
    
    /**
     * 添加信息项动画效果
     */
    animateInfoItems() {
        const items = document.querySelectorAll('.info-item');
        items.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateY(10px)';
            
            setTimeout(() => {
                item.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 50);
        });
    },
    
    /**
     * 导出系统信息报告
     */
    exportSystemInfo() {
        Toast.info('正在生成系统报告...');
        
        API.settings.exportSystemReport()
            .then(data => {
                if (data.code === 200) {
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = data.data.download_url;
                    link.download = data.data.filename;
                    link.style.display = 'none';
                    
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    Toast.success('系统报告导出成功');
                } else {
                    Toast.error(data.message || '导出失败');
                }
            })
            .catch(error => {
                console.error('导出系统报告失败:', error);
                Toast.error('导出失败，请稍后重试');
            });
    },
    
    /**
     * 系统健康检测
     */
    checkSystemHealth() {
        Toast.info('正在进行系统健康检测...');
        
        API.settings.checkSystemHealth()
            .then(data => {
                if (data.code === 200) {
                    this.showHealthReport(data.data);
                } else {
                    Toast.error(data.message || '检测失败');
                }
            })
            .catch(error => {
                console.error('系统健康检测失败:', error);
                Toast.error('检测失败，请稍后重试');
            });
    },
    
    /**
     * 显示健康检测报告
     * @param {object} report 检测报告
     */
    showHealthReport(report) {
        const issues = report.issues || [];
        const score = report.score || 0;
        
        let message = `系统健康评分: ${score}/100\n\n`;
        
        if (issues.length === 0) {
            message += '✅ 系统运行正常，未发现问题';
        } else {
            message += '发现以下问题:\n';
            issues.forEach((issue, index) => {
                message += `${index + 1}. ${issue.message}\n`;
            });
        }
        
        alert(message);
    }
};

// 全局函数，供HTML调用
window.loadSystemInfo = function() {
    SystemInfo.loadSystemInfo();
};

// 导出模块
window.SystemInfo = SystemInfo;
