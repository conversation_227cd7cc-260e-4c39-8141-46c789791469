/**
 * 卡密管理页面样式
 * 专门为卡密管理页面设计的样式
 */

/* 卡密统计卡片 */
.card-stats {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-5);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.card-stats:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.card-stats.stats-total::before {
  background: linear-gradient(90deg, var(--primary-color), var(--primary-hover));
}

.card-stats.stats-used::before {
  background: linear-gradient(90deg, var(--success-color), var(--success-hover));
}

.card-stats.stats-unused::before {
  background: linear-gradient(90deg, var(--warning-color), var(--warning-hover));
}

.card-stats.stats-expired::before {
  background: linear-gradient(90deg, var(--danger-color), var(--danger-hover));
}

.card-stats-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  color: var(--white);
  margin-bottom: var(--spacing-4);
}

.card-stats-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
  line-height: 1;
}

.card-stats-label {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-2);
}

.card-stats-trend {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.card-stats-trend.positive {
  color: var(--success-color);
}

.card-stats-trend.negative {
  color: var(--danger-color);
}

/* 卡密表格样式 */
.cards-table {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
}

.cards-table-header {
  padding: var(--spacing-5) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.cards-table-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.cards-table-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

/* 卡密状态样式 */
.card-status {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.card-status.status-unused {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.card-status.status-used {
  background-color: var(--success-light);
  color: var(--success-color);
}

.card-status.status-expired {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.card-status.status-disabled {
  background-color: var(--gray-200);
  color: var(--gray-600);
}

/* 卡密类型样式 */
.card-type {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius);
  background-color: var(--primary-light);
  color: var(--primary-color);
}

/* 卡密操作按钮 */
.card-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.card-action-btn {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius);
  border: var(--border-width) solid transparent;
  background: transparent;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
}

.card-action-btn:hover {
  background-color: var(--gray-100);
  color: var(--gray-800);
}

.card-action-btn.btn-view:hover {
  background-color: var(--info-light);
  color: var(--info-color);
}

.card-action-btn.btn-edit:hover {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.card-action-btn.btn-delete:hover {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

/* 卡密筛选器 */
.cards-filters {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4);
  box-shadow: var(--shadow-sm);
  border: var(--border-width) solid var(--border-color);
  margin-bottom: var(--spacing-6);
}

.cards-filters-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  flex-wrap: wrap;
}

.cards-filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  min-width: 150px;
}

.cards-filter-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
}

.cards-filter-input {
  padding: var(--spacing-2) var(--spacing-3);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  transition: border-color var(--transition-fast);
}

.cards-filter-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* 批量操作栏 */
.cards-bulk-actions {
  display: none;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-6);
  background: var(--primary-light);
  border-bottom: var(--border-width) solid var(--primary-color);
}

.cards-bulk-actions.show {
  display: flex;
}

.cards-bulk-info {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.cards-bulk-buttons {
  display: flex;
  gap: var(--spacing-2);
}

/* 卡密详情模态框 */
.card-detail-modal .modal-body {
  padding: 0;
}

.card-detail-header {
  padding: var(--spacing-6);
  border-bottom: var(--border-width) solid var(--border-color);
  background: var(--gray-50);
}

.card-detail-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
}

.card-detail-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.card-detail-content {
  padding: var(--spacing-6);
}

.card-detail-row {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) 0;
  border-bottom: var(--border-width) solid var(--gray-200);
}

.card-detail-row:last-child {
  border-bottom: none;
}

.card-detail-label {
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  width: 120px;
  flex-shrink: 0;
}

.card-detail-value {
  color: var(--gray-800);
  flex: 1;
}

.card-detail-code {
  font-family: var(--font-family-monospace);
  background: var(--gray-100);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  border: var(--border-width) solid var(--border-color);
}

/* 卡密生成表单 */
.card-generate-form {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
}

.card-generate-section {
  margin-bottom: var(--spacing-6);
}

.card-generate-section:last-child {
  margin-bottom: 0;
}

.card-generate-section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-4);
  padding-bottom: var(--spacing-2);
  border-bottom: var(--border-width) solid var(--border-color);
}

.card-generate-row {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.card-generate-col {
  flex: 1;
}

.card-generate-col-auto {
  flex: 0 0 auto;
}

/* 卡密预览 */
.card-preview {
  background: var(--gray-100);
  border-radius: var(--border-radius);
  padding: var(--spacing-4);
  margin-top: var(--spacing-4);
}

.card-preview-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-bottom: var(--spacing-3);
}

.card-preview-list {
  max-height: 200px;
  overflow-y: auto;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--white);
}

.card-preview-item {
  padding: var(--spacing-2) var(--spacing-3);
  border-bottom: var(--border-width) solid var(--gray-200);
  font-family: var(--font-family-monospace);
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

.card-preview-item:last-child {
  border-bottom: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-filters-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .cards-filter-group {
    min-width: auto;
  }
  
  .cards-table-header {
    flex-direction: column;
    gap: var(--spacing-3);
    align-items: stretch;
  }
  
  .cards-table-actions {
    justify-content: center;
  }
  
  .cards-bulk-actions {
    flex-direction: column;
    gap: var(--spacing-3);
  }
  
  .card-generate-row {
    flex-direction: column;
  }
  
  .card-detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }
  
  .card-detail-label {
    width: auto;
  }
}

@media (max-width: 576px) {
  .card-actions {
    flex-direction: column;
    gap: var(--spacing-1);
  }
  
  .card-action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .cards-table-header {
    padding: var(--spacing-4);
  }
  
  .card-generate-form {
    padding: var(--spacing-4);
  }
}
