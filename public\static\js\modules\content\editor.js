/**
 * 内容编辑器组件
 * 处理内容的编辑、预览和验证功能
 */

const ContentEditor = {
    /**
     * 初始化编辑器
     */
    init() {
        this.bindEvents();
        this.initPreview();
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 绑定实时预览事件
        const titleInput = document.querySelector('input[name="title"]');
        const contentTextarea = document.querySelector('textarea[name="content"]');
        
        if (titleInput) {
            titleInput.addEventListener('input', () => {
                this.updatePreview();
                this.updateCharCount();
            });
        }
        
        if (contentTextarea) {
            contentTextarea.addEventListener('input', () => {
                this.updatePreview();
                this.updateCharCount();
            });
            
            // 绑定键盘快捷键
            contentTextarea.addEventListener('keydown', (e) => {
                this.handleKeyboardShortcuts(e);
            });
        }
        
        // 绑定表单提交事件
        const forms = document.querySelectorAll('#createForm, #editForm');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitForm(form);
            });
        });
    },
    
    /**
     * 初始化预览
     */
    initPreview() {
        this.updatePreview();
        this.updateCharCount();
    },
    
    /**
     * 更新实时预览
     */
    updatePreview() {
        const titleInput = document.querySelector('input[name="title"]');
        const contentTextarea = document.querySelector('textarea[name="content"]');
        const previewDiv = document.getElementById('contentPreview');
        
        if (!previewDiv) return;
        
        const title = titleInput ? titleInput.value.trim() : '';
        const content = contentTextarea ? contentTextarea.value.trim() : '';
        
        if (title || content) {
            let previewText = '';
            if (title) {
                previewText += `${title}\n\n`;
            }
            if (content) {
                previewText += content;
            }
            previewDiv.textContent = previewText;
            previewDiv.classList.remove('text-muted');
        } else {
            previewDiv.textContent = '在上方输入内容，这里将显示预览效果...';
            previewDiv.classList.add('text-muted');
        }
    },
    
    /**
     * 更新字符计数
     */
    updateCharCount() {
        const contentTextarea = document.querySelector('textarea[name="content"]');
        const charCountElement = document.getElementById('charCount');
        
        if (!contentTextarea || !charCountElement) return;
        
        const currentLength = contentTextarea.value.length;
        const maxLength = contentTextarea.maxLength || 5000;
        
        charCountElement.textContent = `${currentLength} / ${maxLength}`;
        
        // 根据字符数量设置样式
        if (currentLength > maxLength * 0.9) {
            charCountElement.className = 'char-count text-danger';
        } else if (currentLength > maxLength * 0.8) {
            charCountElement.className = 'char-count text-warning';
        } else {
            charCountElement.className = 'char-count text-muted';
        }
    },
    
    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl+S 保存
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const form = e.target.closest('form');
            if (form) {
                this.submitForm(form);
            }
        }
        
        // Ctrl+Enter 快速保存并返回
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            const form = e.target.closest('form');
            if (form) {
                this.submitForm(form, true);
            }
        }
        
        // Tab 键插入缩进
        if (e.key === 'Tab') {
            e.preventDefault();
            const textarea = e.target;
            const start = textarea.selectionStart;
            const end = textarea.selectionEnd;
            
            // 插入4个空格作为缩进
            const value = textarea.value;
            textarea.value = value.substring(0, start) + '    ' + value.substring(end);
            
            // 重新设置光标位置
            textarea.selectionStart = textarea.selectionEnd = start + 4;
            
            // 触发input事件以更新预览
            textarea.dispatchEvent(new Event('input'));
        }
    },
    
    /**
     * 验证表单
     * @param {HTMLFormElement} form 表单元素
     * @returns {object}
     */
    validateForm(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        const errors = {};
        
        // 验证标题
        if (!data.title || data.title.trim() === '') {
            errors.title = '请输入内容标题';
        } else if (data.title.length > 100) {
            errors.title = '标题长度不能超过100个字符';
        }
        
        // 验证分类
        if (!data.category_id) {
            errors.category_id = '请选择分类';
        }
        
        // 验证内容
        if (!data.content || data.content.trim() === '') {
            errors.content = '请输入内容';
        } else if (data.content.length > 5000) {
            errors.content = '内容长度不能超过5000个字符';
        }
        
        // 验证排序
        const sortOrder = parseInt(data.sort_order);
        if (isNaN(sortOrder) || sortOrder < 0 || sortOrder > 9999) {
            errors.sort_order = '排序值必须是0-9999之间的数字';
        }
        
        return {
            valid: Object.keys(errors).length === 0,
            errors: errors,
            data: data
        };
    },
    
    /**
     * 提交表单
     * @param {HTMLFormElement} form 表单元素
     * @param {boolean} quickSave 是否快速保存（保存后返回列表）
     */
    submitForm(form, quickSave = false) {
        // 验证表单
        const validation = this.validateForm(form);
        if (!validation.valid) {
            const firstError = Object.values(validation.errors)[0];
            Toast.error(firstError);
            return;
        }
        
        const formData = new FormData(form);
        const isEdit = formData.has('id') && formData.get('id');
        
        // 设置加载状态
        this.setSubmitLoading(form, true);
        
        // 确定API端点
        const endpoint = isEdit ? '/content/edit' : '/content/create';
        
        // 发送请求
        fetch(endpoint, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                Toast.success(data.message || (isEdit ? '更新成功' : '创建成功'));
                
                if (quickSave) {
                    // 快速保存，直接返回列表
                    setTimeout(() => {
                        window.location.href = '/content';
                    }, 1000);
                } else {
                    // 普通保存，询问是否返回列表
                    setTimeout(() => {
                        if (Utils.confirm('保存成功！是否返回内容列表？')) {
                            window.location.href = '/content';
                        }
                    }, 1500);
                }
            } else {
                Toast.error(data.message || (isEdit ? '更新失败' : '创建失败'));
            }
        })
        .catch(error => {
            console.error('提交失败:', error);
            Toast.error('提交失败，请稍后重试');
        })
        .finally(() => {
            this.setSubmitLoading(form, false);
        });
    },
    
    /**
     * 设置提交按钮加载状态
     * @param {HTMLFormElement} form 表单元素
     * @param {boolean} loading 是否加载中
     */
    setSubmitLoading(form, loading) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (!submitBtn) return;
        
        if (loading) {
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        } else {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save"></i> 保存';
        }
    },
    
    /**
     * 插入文本到光标位置
     * @param {string} text 要插入的文本
     */
    insertText(text) {
        const contentTextarea = document.querySelector('textarea[name="content"]');
        if (!contentTextarea) return;
        
        const start = contentTextarea.selectionStart;
        const end = contentTextarea.selectionEnd;
        const value = contentTextarea.value;
        
        contentTextarea.value = value.substring(0, start) + text + value.substring(end);
        
        // 重新设置光标位置
        const newPosition = start + text.length;
        contentTextarea.selectionStart = contentTextarea.selectionEnd = newPosition;
        
        // 触发input事件以更新预览
        contentTextarea.dispatchEvent(new Event('input'));
        contentTextarea.focus();
    },
    
    /**
     * 格式化内容
     * @param {string} type 格式化类型
     */
    formatContent(type) {
        const contentTextarea = document.querySelector('textarea[name="content"]');
        if (!contentTextarea) return;
        
        const start = contentTextarea.selectionStart;
        const end = contentTextarea.selectionEnd;
        const selectedText = contentTextarea.value.substring(start, end);
        
        let formattedText = '';
        
        switch (type) {
            case 'bold':
                formattedText = `**${selectedText}**`;
                break;
            case 'italic':
                formattedText = `*${selectedText}*`;
                break;
            case 'code':
                formattedText = `\`${selectedText}\``;
                break;
            case 'link':
                const url = prompt('请输入链接地址:');
                if (url) {
                    formattedText = `[${selectedText || '链接文本'}](${url})`;
                }
                break;
            case 'list':
                formattedText = selectedText.split('\n').map(line => `- ${line}`).join('\n');
                break;
            default:
                return;
        }
        
        if (formattedText) {
            const value = contentTextarea.value;
            contentTextarea.value = value.substring(0, start) + formattedText + value.substring(end);
            
            // 重新设置光标位置
            contentTextarea.selectionStart = start;
            contentTextarea.selectionEnd = start + formattedText.length;
            
            // 触发input事件以更新预览
            contentTextarea.dispatchEvent(new Event('input'));
            contentTextarea.focus();
        }
    },
    
    /**
     * 清空编辑器
     */
    clear() {
        if (Utils.confirm('确定要清空所有内容吗？')) {
            const titleInput = document.querySelector('input[name="title"]');
            const contentTextarea = document.querySelector('textarea[name="content"]');
            
            if (titleInput) titleInput.value = '';
            if (contentTextarea) contentTextarea.value = '';
            
            this.updatePreview();
            this.updateCharCount();
            
            Toast.info('内容已清空');
        }
    }
};

// 全局函数，供HTML调用
window.updatePreview = function() {
    ContentEditor.updatePreview();
};

window.insertText = function(text) {
    ContentEditor.insertText(text);
};

window.formatContent = function(type) {
    ContentEditor.formatContent(type);
};

window.clearEditor = function() {
    ContentEditor.clear();
};

// 导出模块
window.ContentEditor = ContentEditor;
