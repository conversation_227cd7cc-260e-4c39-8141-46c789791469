/**
 * 按钮组件样式
 * 统一的按钮样式系统
 */

/* 基础按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-2) var(--spacing-4);
  font-family: var(--font-family-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  border: var(--border-width) solid transparent;
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.3);
}

.btn:disabled,
.btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮尺寸 */
.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-sm);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-lg);
}

.btn-xl {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-xl);
  border-radius: var(--border-radius-lg);
}

/* 主要按钮 */
.btn-primary {
  color: var(--white);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* 次要按钮 */
.btn-secondary {
  color: var(--white);
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
  border-color: var(--secondary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 成功按钮 */
.btn-success {
  color: var(--white);
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-success:hover {
  background-color: var(--success-hover);
  border-color: var(--success-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 警告按钮 */
.btn-warning {
  color: var(--white);
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.btn-warning:hover {
  background-color: var(--warning-hover);
  border-color: var(--warning-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 危险按钮 */
.btn-danger {
  color: var(--white);
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-danger:hover {
  background-color: var(--danger-hover);
  border-color: var(--danger-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 信息按钮 */
.btn-info {
  color: var(--white);
  background-color: var(--info-color);
  border-color: var(--info-color);
}

.btn-info:hover {
  background-color: var(--info-hover);
  border-color: var(--info-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 轮廓按钮 */
.btn-outline-primary {
  color: var(--primary-color);
  background-color: transparent;
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  color: var(--white);
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-secondary {
  color: var(--secondary-color);
  background-color: transparent;
  border-color: var(--secondary-color);
}

.btn-outline-secondary:hover {
  color: var(--white);
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-outline-success {
  color: var(--success-color);
  background-color: transparent;
  border-color: var(--success-color);
}

.btn-outline-success:hover {
  color: var(--white);
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-outline-danger {
  color: var(--danger-color);
  background-color: transparent;
  border-color: var(--danger-color);
}

.btn-outline-danger:hover {
  color: var(--white);
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

/* 链接按钮 */
.btn-link {
  color: var(--primary-color);
  background-color: transparent;
  border-color: transparent;
  text-decoration: underline;
}

.btn-link:hover {
  color: var(--primary-hover);
  text-decoration: none;
}

/* 按钮组 */
.btn-group {
  display: inline-flex;
  vertical-align: middle;
}

.btn-group .btn {
  position: relative;
  flex: 1 1 auto;
}

.btn-group .btn:not(:first-child) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.btn-group .btn:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group .btn:hover,
.btn-group .btn:focus,
.btn-group .btn:active {
  z-index: 1;
}

/* 加载状态 */
.btn-loading {
  position: relative;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 图标按钮 */
.btn-icon {
  padding: var(--spacing-2);
  width: 2.5rem;
  height: 2.5rem;
}

.btn-icon-sm {
  padding: var(--spacing-1);
  width: 2rem;
  height: 2rem;
}

.btn-icon-lg {
  padding: var(--spacing-3);
  width: 3rem;
  height: 3rem;
}

/* 浮动操作按钮 */
.btn-fab {
  position: fixed;
  bottom: var(--spacing-6);
  right: var(--spacing-6);
  width: 56px;
  height: 56px;
  border-radius: var(--border-radius-full);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
}

.btn-fab:hover {
  box-shadow: var(--shadow-xl);
  transform: scale(1.05);
}

/* 现代化按钮样式 */
.modern-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.75rem;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-btn:hover::before {
  left: 100%;
}

.modern-btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  box-shadow: 0 4px 14px 0 rgba(0, 123, 255, 0.3);
}

.modern-btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-color) 100%);
  box-shadow: 0 6px 20px 0 rgba(0, 123, 255, 0.4);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.modern-btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%);
  color: white;
  box-shadow: 0 4px 14px 0 rgba(40, 167, 69, 0.3);
}

.modern-btn-success:hover {
  background: linear-gradient(135deg, var(--success-hover) 0%, var(--success-color) 100%);
  box-shadow: 0 6px 20px 0 rgba(40, 167, 69, 0.4);
  transform: translateY(-2px);
  color: white;
  text-decoration: none;
}

.modern-btn-outline {
  background: rgba(255, 255, 255, 0.8);
  color: var(--gray-600);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}

.modern-btn-outline:hover {
  background: white;
  color: var(--gray-700);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
  text-decoration: none;
}
