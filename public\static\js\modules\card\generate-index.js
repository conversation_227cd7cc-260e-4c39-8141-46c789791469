/**
 * 卡密生成页面主控制器
 * 统一管理卡密生成页面的所有功能
 */

const CardGenerateManager = {
    /**
     * 初始化卡密生成功能
     */
    init() {
        console.log('初始化卡密生成功能...');
        
        // 初始化各个子模块
        this.initSubModules();
        
        // 绑定页面事件
        this.bindEvents();
        
        // 初始化页面状态
        this.initPageState();
        
        console.log('卡密生成功能初始化完成');
    },
    
    /**
     * 初始化子模块
     */
    initSubModules() {
        // 初始化表单功能
        if (typeof CardGenerateForm !== 'undefined') {
            CardGenerateForm.init();
        }
    },
    
    /**
     * 绑定页面事件
     */
    bindEvents() {
        // 绑定窗口关闭前确认
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开此页面吗？';
                return e.returnValue;
            }
        });
        
        // 绑定页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // 页面重新可见时，刷新分类数据
                this.refreshCategories();
            }
        });
        
        // 绑定在线状态变化事件
        window.addEventListener('online', () => {
            Toast.success('网络连接已恢复');
            this.refreshCategories();
        });
        
        window.addEventListener('offline', () => {
            Toast.warning('网络连接已断开，请检查网络设置');
        });
    },
    
    /**
     * 初始化页面状态
     */
    initPageState() {
        // 检查是否有保存的草稿
        this.loadDraft();
        
        // 设置自动保存草稿
        this.setupAutoSave();
        
        // 显示使用提示
        this.showUsageTips();
    },
    
    /**
     * 检查是否有未保存的更改
     * @returns {boolean}
     */
    hasUnsavedChanges() {
        const form = document.getElementById('generateForm');
        if (!form) return false;
        
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // 检查是否有填写内容
        return data.category_id || data.content_id || 
               (data.content && data.content.trim()) ||
               (data.count && data.count !== '10') ||
               data.expire_at;
    },
    
    /**
     * 加载草稿
     */
    loadDraft() {
        try {
            const draft = localStorage.getItem('card_generate_draft');
            if (draft) {
                const draftData = JSON.parse(draft);
                const draftTime = new Date(draftData.timestamp);
                const now = new Date();
                
                // 如果草稿是1小时内的，询问是否恢复
                if (now - draftTime < 60 * 60 * 1000) {
                    if (confirm('发现未完成的卡密生成草稿，是否恢复？')) {
                        this.restoreDraft(draftData.data);
                    }
                }
            }
        } catch (error) {
            console.error('加载草稿失败:', error);
        }
    },
    
    /**
     * 恢复草稿
     * @param {object} draftData 草稿数据
     */
    restoreDraft(draftData) {
        const form = document.getElementById('generateForm');
        if (!form) return;
        
        Object.keys(draftData).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                field.value = draftData[key];
                
                // 触发相关事件
                if (key === 'category_id') {
                    CardGenerateForm.onCategoryChange(draftData[key]);
                } else if (key === 'content') {
                    CardGenerateForm.updateCharCount();
                }
            }
        });
        
        Toast.info('草稿已恢复');
    },
    
    /**
     * 保存草稿
     */
    saveDraft() {
        try {
            const form = document.getElementById('generateForm');
            if (!form) return;
            
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                if (value) { // 只保存有值的字段
                    data[key] = value;
                }
            }
            
            // 如果没有任何数据，不保存草稿
            if (Object.keys(data).length === 0) return;
            
            const draft = {
                timestamp: new Date().toISOString(),
                data: data
            };
            
            localStorage.setItem('card_generate_draft', JSON.stringify(draft));
        } catch (error) {
            console.error('保存草稿失败:', error);
        }
    },
    
    /**
     * 清除草稿
     */
    clearDraft() {
        try {
            localStorage.removeItem('card_generate_draft');
        } catch (error) {
            console.error('清除草稿失败:', error);
        }
    },
    
    /**
     * 设置自动保存草稿
     */
    setupAutoSave() {
        // 每30秒自动保存草稿
        setInterval(() => {
            if (this.hasUnsavedChanges()) {
                this.saveDraft();
            }
        }, 30000);
        
        // 监听表单变化，延迟保存草稿
        let saveTimeout;
        document.addEventListener('input', () => {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                if (this.hasUnsavedChanges()) {
                    this.saveDraft();
                }
            }, 3000);
        });
    },
    
    /**
     * 显示使用提示
     */
    showUsageTips() {
        // 检查是否是首次访问
        const hasVisited = localStorage.getItem('card_generate_visited');
        if (!hasVisited) {
            setTimeout(() => {
                this.showWelcomeGuide();
                localStorage.setItem('card_generate_visited', 'true');
            }, 1000);
        }
    },
    
    /**
     * 显示欢迎引导
     */
    showWelcomeGuide() {
        const message = `
            欢迎使用卡密生成器！
            
            使用提示：
            • 选择分类后会自动加载该分类下的内容模板
            • 建议单次生成数量不超过500个，避免超时
            • 可以设置过期时间，不设置则永不过期
            • 支持 Ctrl+Enter 快速生成，Ctrl+R 重置表单
            • 系统会自动保存草稿，避免意外丢失
            
            祝您使用愉快！
        `;
        
        alert(message);
    },
    
    /**
     * 刷新分类数据
     */
    refreshCategories() {
        if (typeof CardGenerateForm !== 'undefined') {
            CardGenerateForm.loadCategories();
        }
    },
    
    /**
     * 预览生成结果
     */
    previewGenerate() {
        const validation = CardGenerateForm.validateForm();
        if (!validation.valid) {
            const firstError = Object.values(validation.errors)[0];
            Toast.error(firstError);
            return;
        }
        
        const data = validation.data;
        const count = parseInt(data.count);
        const categoryName = document.querySelector(`#category_id option[value="${data.category_id}"]`)?.textContent || '未知分类';
        const contentTitle = document.querySelector(`#content_id option[value="${data.content_id}"]`)?.textContent || '未知内容';
        
        const previewMessage = `
            生成预览：
            
            分类：${categoryName}
            内容：${contentTitle}
            数量：${count} 个
            过期时间：${data.expire_at ? new Date(data.expire_at).toLocaleString() : '永不过期'}
            
            确认要生成这些卡密吗？
        `;
        
        if (confirm(previewMessage)) {
            CardGenerateForm.submitForm();
        }
    },
    
    /**
     * 批量生成模式
     */
    enableBatchMode() {
        const countInput = document.getElementById('count');
        if (countInput) {
            countInput.max = '10000'; // 批量模式允许更多数量
            countInput.placeholder = '批量模式：1-10000';
            
            Toast.info('已启用批量生成模式，最多可生成10000个卡密');
        }
    },
    
    /**
     * 导出生成记录
     */
    exportGenerateHistory() {
        API.card.getGenerateHistory()
            .then(data => {
                if (data.code === 200) {
                    this.downloadHistory(data.data);
                } else {
                    Toast.error('获取生成记录失败');
                }
            })
            .catch(error => {
                console.error('导出生成记录失败:', error);
                Toast.error('导出失败，请稍后重试');
            });
    },
    
    /**
     * 下载生成记录
     * @param {Array} history 生成记录
     */
    downloadHistory(history) {
        const csv = this.convertToCSV(history);
        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `card_generate_history_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        Toast.success('生成记录已导出');
    },
    
    /**
     * 转换为CSV格式
     * @param {Array} data 数据
     * @returns {string}
     */
    convertToCSV(data) {
        const headers = ['生成时间', '分类', '内容', '数量', '过期时间'];
        const csvContent = [
            headers.join(','),
            ...data.map(row => [
                row.created_at,
                row.category_name,
                row.content_title,
                row.count,
                row.expire_at || '永不过期'
            ].join(','))
        ].join('\n');
        
        return '\uFEFF' + csvContent; // 添加BOM以支持中文
    }
};

// 全局函数，供HTML调用
window.previewGenerate = function() {
    CardGenerateManager.previewGenerate();
};

window.enableBatchMode = function() {
    CardGenerateManager.enableBatchMode();
};

window.exportGenerateHistory = function() {
    CardGenerateManager.exportGenerateHistory();
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保所有依赖都已加载
    if (typeof Utils !== 'undefined' && 
        typeof API !== 'undefined' && 
        typeof Toast !== 'undefined') {
        CardGenerateManager.init();
    } else {
        console.error('卡密生成功能初始化失败：缺少依赖模块');
    }
});

// 导出模块
window.CardGenerateManager = CardGenerateManager;
