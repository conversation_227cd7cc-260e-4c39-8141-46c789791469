/**
 * 导航组件样式
 * 统一的导航样式系统
 */

/* 导航栏基础样式 */
.navbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-4);
  background-color: var(--white);
  border-bottom: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.navbar-brand {
  display: inline-block;
  padding-top: var(--spacing-2);
  padding-bottom: var(--spacing-2);
  margin-right: var(--spacing-4);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  line-height: inherit;
  text-decoration: none;
  color: var(--gray-800);
}

.navbar-brand:hover {
  text-decoration: none;
  color: var(--primary-color);
}

/* 侧边栏导航 */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-fixed);
  width: var(--sidebar-width);
  height: 100vh;
  background-color: var(--white);
  border-right: var(--border-width) solid var(--border-color);
  box-shadow: var(--shadow);
  overflow-y: auto;
  transition: transform var(--transition-normal);
}

.sidebar-header {
  padding: var(--spacing-5) var(--spacing-4);
  border-bottom: var(--border-width) solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
}

.sidebar-brand {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--white);
  text-decoration: none;
}

.sidebar-nav {
  padding: var(--spacing-4) 0;
}

.sidebar-nav-item {
  margin-bottom: var(--spacing-1);
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--gray-600);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: 0 var(--border-radius-full) var(--border-radius-full) 0;
  margin-right: var(--spacing-4);
}

.sidebar-nav-link:hover {
  color: var(--primary-color);
  background-color: var(--primary-light);
  text-decoration: none;
}

.sidebar-nav-link.active {
  color: var(--primary-color);
  background-color: var(--primary-light);
  font-weight: var(--font-weight-medium);
}

.sidebar-nav-icon {
  width: 20px;
  margin-right: var(--spacing-3);
  text-align: center;
  font-size: var(--font-size-base);
}

/* 面包屑导航 */
.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: var(--spacing-3) var(--spacing-4);
  margin-bottom: var(--spacing-4);
  list-style: none;
  background-color: var(--gray-100);
  border-radius: var(--border-radius);
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: var(--spacing-2);
}

.breadcrumb-item + .breadcrumb-item::before {
  display: inline-block;
  padding-right: var(--spacing-2);
  color: var(--gray-500);
  content: "/";
}

.breadcrumb-item.active {
  color: var(--gray-600);
}

/* 标签页导航 */
.nav-tabs {
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
  border-bottom: var(--border-width) solid var(--border-color);
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
}

.nav-tabs .nav-link {
  border: var(--border-width) solid transparent;
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--border-radius);
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--gray-600);
  text-decoration: none;
  transition: all var(--transition-fast);
}

.nav-tabs .nav-link:hover {
  border-color: var(--gray-200) var(--gray-200) var(--border-color);
  color: var(--primary-color);
}

.nav-tabs .nav-link.active {
  color: var(--gray-700);
  background-color: var(--white);
  border-color: var(--border-color) var(--border-color) var(--white);
}

/* 下拉菜单 */
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: var(--z-dropdown);
  display: none;
  float: left;
  min-width: 10rem;
  padding: var(--spacing-2) 0;
  margin: var(--spacing-1) 0 0;
  font-size: var(--font-size-base);
  color: var(--gray-700);
  text-align: left;
  list-style: none;
  background-color: var(--white);
  background-clip: padding-box;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-4);
  clear: both;
  font-weight: var(--font-weight-normal);
  color: var(--gray-700);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.dropdown-item:hover {
  color: var(--gray-800);
  background-color: var(--gray-100);
  text-decoration: none;
}

.dropdown-item.active {
  color: var(--white);
  background-color: var(--primary-color);
  text-decoration: none;
}

/* 响应式导航 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
}
