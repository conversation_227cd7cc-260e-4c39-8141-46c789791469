{extend name="layout/base" /}

{block name="title"}编辑分类 - 卡密兑换管理系统{/block}

{block name="style"}
<style>
    .form-card {
        background: var(--card-bg);
        border-radius: 8px;
        padding: 32px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: 1px solid var(--border-color);
        max-width: 600px;
        margin: 0 auto;
    }
    
    .form-label {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
        font-size: 14px;
    }
    
    .form-control,
    .form-select {
        border-radius: 6px;
        border: 1px solid var(--border-color);
        padding: 12px 16px;
        transition: all 0.2s ease;
        font-size: 14px;
    }
    
    .form-control:focus,
    .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
    
    .form-tips {
        background: #fff7e6;
        border-left: 4px solid var(--warning-color);
        padding: 16px;
        border-radius: 0 6px 6px 0;
        margin-bottom: 24px;
        border: 1px solid #ffd591;
        border-left: 4px solid var(--warning-color);
    }
    
    .form-tips h6 {
        color: var(--warning-color);
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 600;
    }
    
    .form-tips ul {
        margin: 0;
        padding-left: 20px;
    }
    
    .form-tips li {
        color: var(--text-secondary);
        font-size: 13px;
        margin-bottom: 4px;
        line-height: 1.5;
    }
    
    .btn-submit {
        background: var(--primary-color);
        border: none;
        border-radius: 6px;
        padding: 12px 32px;
        font-weight: 600;
        color: white;
        transition: all 0.3s ease;
        font-size: 14px;
    }
    
    .btn-submit:hover {
        background: #40a9ff;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        color: white;
    }
    
    .btn-submit:disabled {
        background: var(--text-secondary);
        transform: none;
        box-shadow: none;
        opacity: 0.6;
    }
    
    .loading-spinner {
        display: none;
    }
    
    .loading-spinner.show {
        display: inline-block;
    }
    
    .result-alert {
        margin-top: 16px;
        border-radius: 8px;
    }
    
    .current-path {
        background: var(--content-bg);
        padding: 12px 16px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 16px;
    }
</style>
{/block}

{block name="content"}
<div class="page-title">
    <h1>编辑分类</h1>
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/dashboard">控制台</a></li>
            <li class="breadcrumb-item"><a href="/categories">分类管理</a></li>
            <li class="breadcrumb-item active">编辑分类</li>
        </ol>
    </nav>
</div>

<div class="form-card">
    <div class="form-tips">
        <h6><i class="fas fa-exclamation-triangle"></i> 编辑说明</h6>
        <ul>
            <li>修改父分类可能会影响分类层级结构</li>
            <li>不能将自己或子分类设置为父分类</li>
            <li>同级分类名称不能重复</li>
            <li>修改后会自动更新分类路径</li>
        </ul>
    </div>
    
    <div class="current-path">
        <strong>当前路径:</strong> {$category.getFullPath()}
    </div>
    
    <form id="categoryForm">
        <input type="hidden" name="id" value="{$category.id}">
        
        <div class="mb-3">
            <label for="parent_id" class="form-label">父分类</label>
            <select class="form-select" id="parent_id" name="parent_id">
                <option value="0" {$category.parent_id == 0 ? 'selected' : ''}>顶级分类</option>
                {volist name="parentCategories" id="parent"}
                <option value="{$parent.id}" {$category.parent_id == $parent.id ? 'selected' : ''}>
                    {$parent.name}
                </option>
                {/volist}
            </select>
            <div class="form-text">选择父分类，不选择则为顶级分类</div>
        </div>
        
        <div class="mb-3">
            <label for="name" class="form-label">分类名称 <span class="text-danger">*</span></label>
            <input type="text" class="form-control" id="name" name="name" value="{$category.name}" required maxlength="100">
            <div class="form-text">分类名称，最多100个字符</div>
        </div>
        
        <div class="mb-3">
            <label for="description" class="form-label">分类描述</label>
            <textarea class="form-control" id="description" name="description" rows="3" maxlength="500">{$category.description}</textarea>
            <div class="form-text">分类的详细描述，最多500个字符</div>
        </div>
        
        <div class="mb-3">
            <label for="sort_order" class="form-label">排序权重</label>
            <input type="number" class="form-control" id="sort_order" name="sort_order" value="{$category.sort_order}" min="0" max="9999">
            <div class="form-text">数字越小排序越靠前</div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <a href="/categories" class="btn btn-outline-secondary me-md-2">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
            <button type="submit" class="btn btn-submit">
                <span class="btn-text">
                    <i class="fas fa-save"></i> 保存修改
                </span>
                <span class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> 保存中...
                </span>
            </button>
        </div>
    </form>
    
    <div id="resultContainer"></div>
</div>
{/block}

{block name="script"}
<script>
    document.getElementById('categoryForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);
        
        // 验证表单
        if (!data.name || data.name.trim() === '') {
            showResult('请输入分类名称', 'danger');
            return;
        }
        
        if (data.name.length > 100) {
            showResult('分类名称不能超过100个字符', 'danger');
            return;
        }
        
        if (data.description && data.description.length > 500) {
            showResult('分类描述不能超过500个字符', 'danger');
            return;
        }
        
        // 显示加载状态
        setLoading(true);
        
        // 发送请求
        fetch('/categories/edit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.code === 200) {
                showResult(result.message, 'success');
                // 2秒后跳转到列表页
                setTimeout(() => {
                    window.location.href = '/categories';
                }, 2000);
            } else {
                showResult(result.message, 'danger');
            }
        })
        .catch(error => {
            showResult('保存失败，请稍后重试', 'danger');
        })
        .finally(() => {
            setLoading(false);
        });
    });
    
    function setLoading(loading) {
        const btnText = document.querySelector('.btn-text');
        const loadingSpinner = document.querySelector('.loading-spinner');
        const submitBtn = document.querySelector('.btn-submit');
        
        if (loading) {
            btnText.style.display = 'none';
            loadingSpinner.classList.add('show');
            submitBtn.disabled = true;
        } else {
            btnText.style.display = 'inline';
            loadingSpinner.classList.remove('show');
            submitBtn.disabled = false;
        }
    }
    
    function showResult(message, type) {
        const container = document.getElementById('resultContainer');
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
        
        container.innerHTML = `
            <div class="alert ${alertClass} result-alert" role="alert">
                <i class="fas ${icon} me-2"></i>${message}
            </div>
        `;
        
        // 滚动到结果区域
        container.scrollIntoView({ behavior: 'smooth' });
        
        // 5秒后自动隐藏
        setTimeout(() => {
            if (type !== 'success') {
                container.innerHTML = '';
            }
        }, 5000);
    }
</script>
{/block}
