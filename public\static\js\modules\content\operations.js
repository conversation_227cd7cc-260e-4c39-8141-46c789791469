/**
 * 内容操作功能模块
 * 处理内容的增删改查操作
 */

const ContentOperations = {
    /**
     * 初始化操作功能
     */
    init() {
        this.bindEvents();
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 绑定排序输入框事件
        document.querySelectorAll('.sort-input').forEach(input => {
            input.addEventListener('blur', (e) => {
                const contentId = e.target.dataset.id;
                const newSort = e.target.value;
                this.updateSort(contentId, newSort);
            });
            
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.target.blur();
                }
            });
        });
        
        // 绑定状态切换事件
        document.querySelectorAll('.status-toggle').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                const contentId = e.target.dataset.id;
                const newStatus = e.target.checked ? 1 : 0;
                this.updateStatus(contentId, newStatus);
            });
        });
        
        // 绑定批量操作事件
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', () => {
                this.toggleSelectAll();
            });
        }
        
        // 绑定单个复选框事件
        document.querySelectorAll('.content-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectAllState();
                this.updateBatchButtonsState();
            });
        });
    },
    
    /**
     * 显示内容详情模态框
     * @param {number} id 内容ID
     * @param {string} title 内容标题
     * @param {string} content 内容正文
     */
    showContentModal(id, title, content) {
        const modal = document.getElementById('contentModal');
        if (!modal) return;
        
        document.getElementById('contentModalTitle').textContent = title;
        document.getElementById('contentModalBody').textContent = content;
        
        // 使用Bootstrap模态框
        if (typeof bootstrap !== 'undefined') {
            new bootstrap.Modal(modal).show();
        } else {
            // 备用方案
            modal.style.display = 'block';
            modal.classList.add('show');
        }
    },
    
    /**
     * 删除内容
     * @param {number} contentId 内容ID
     */
    delete(contentId) {
        if (!Utils.confirm('确定要删除此内容吗？删除后无法恢复！')) {
            return;
        }
        
        API.content.delete(contentId)
            .then(data => {
                if (data.code === 200) {
                    Toast.success('删除成功');
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '删除失败');
                }
            })
            .catch(error => {
                console.error('删除错误:', error);
                Toast.error('删除失败，请稍后重试');
            });
    },
    
    /**
     * 更新排序
     * @param {number} contentId 内容ID
     * @param {number} newSort 新排序值
     */
    updateSort(contentId, newSort) {
        const sortValue = parseInt(newSort);
        if (isNaN(sortValue) || sortValue < 0 || sortValue > 9999) {
            Toast.warning('排序值必须是0-9999之间的数字');
            return;
        }
        
        API.content.updateSort({ id: contentId, sort_order: sortValue })
            .then(data => {
                if (data.code === 200) {
                    Toast.success('排序更新成功');
                } else {
                    Toast.error(data.message || '排序更新失败');
                }
            })
            .catch(error => {
                console.error('排序更新错误:', error);
                Toast.error('排序更新失败，请稍后重试');
            });
    },
    
    /**
     * 更新状态
     * @param {number} contentId 内容ID
     * @param {number} newStatus 新状态
     */
    updateStatus(contentId, newStatus) {
        API.content.updateStatus({ id: contentId, status: newStatus })
            .then(data => {
                if (data.code === 200) {
                    Toast.success('状态更新成功');
                } else {
                    Toast.error(data.message || '状态更新失败');
                    // 恢复开关状态
                    const toggle = document.querySelector(`[data-id="${contentId}"]`);
                    if (toggle) {
                        toggle.checked = !toggle.checked;
                    }
                }
            })
            .catch(error => {
                console.error('状态更新错误:', error);
                Toast.error('状态更新失败，请稍后重试');
                // 恢复开关状态
                const toggle = document.querySelector(`[data-id="${contentId}"]`);
                if (toggle) {
                    toggle.checked = !toggle.checked;
                }
            });
    },
    
    /**
     * 复制内容
     * @param {number} contentId 内容ID
     */
    copy(contentId) {
        API.content.get(contentId)
            .then(data => {
                if (data.code === 200) {
                    const content = data.data;
                    // 跳转到创建页面，并预填数据
                    const params = new URLSearchParams({
                        copy_from: contentId,
                        title: content.title + ' - 副本',
                        content: content.content,
                        category_id: content.category_id
                    });
                    window.location.href = `/content/create?${params.toString()}`;
                } else {
                    Toast.error(data.message || '获取内容失败');
                }
            })
            .catch(error => {
                console.error('复制内容错误:', error);
                Toast.error('复制失败，请稍后重试');
            });
    },
    
    /**
     * 全选/取消全选
     */
    toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.content-checkbox');
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAllCheckbox.checked;
        });
        
        this.updateBatchButtonsState();
    },
    
    /**
     * 更新全选复选框状态
     */
    updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const checkboxes = document.querySelectorAll('.content-checkbox');
        const checkedBoxes = document.querySelectorAll('.content-checkbox:checked');
        
        if (checkedBoxes.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedBoxes.length === checkboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    },
    
    /**
     * 更新批量操作按钮状态
     */
    updateBatchButtonsState() {
        const checkedBoxes = document.querySelectorAll('.content-checkbox:checked');
        const batchButtons = document.querySelectorAll('.batch-btn');
        
        batchButtons.forEach(btn => {
            btn.disabled = checkedBoxes.length === 0;
        });
        
        // 更新批量操作提示文本
        const batchInfo = document.getElementById('batchInfo');
        if (batchInfo) {
            if (checkedBoxes.length > 0) {
                batchInfo.textContent = `已选择 ${checkedBoxes.length} 项`;
                batchInfo.style.display = 'inline';
            } else {
                batchInfo.style.display = 'none';
            }
        }
    },
    
    /**
     * 批量操作
     * @param {string} operation 操作类型
     */
    batchOperation(operation) {
        const selectedIds = this.getSelectedIds();
        if (selectedIds.length === 0) {
            Toast.warning('请选择要操作的内容');
            return;
        }
        
        let confirmMessage = '';
        let apiCall = null;
        
        switch (operation) {
            case 'delete':
                confirmMessage = `确定要删除选中的 ${selectedIds.length} 个内容吗？删除后无法恢复！`;
                apiCall = API.content.batchDelete(selectedIds);
                break;
            case 'enable':
                confirmMessage = `确定要启用选中的 ${selectedIds.length} 个内容吗？`;
                apiCall = API.content.batchUpdateStatus(selectedIds, 1);
                break;
            case 'disable':
                confirmMessage = `确定要禁用选中的 ${selectedIds.length} 个内容吗？`;
                apiCall = API.content.batchUpdateStatus(selectedIds, 0);
                break;
            default:
                Toast.error('未知的批量操作');
                return;
        }
        
        if (!Utils.confirm(confirmMessage)) {
            return;
        }
        
        apiCall
            .then(data => {
                if (data.code === 200) {
                    Toast.success(data.message || '操作成功');
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '操作失败');
                }
            })
            .catch(error => {
                console.error('批量操作错误:', error);
                Toast.error('操作失败，请稍后重试');
            });
    },
    
    /**
     * 获取选中的内容ID
     * @returns {Array}
     */
    getSelectedIds() {
        const checkboxes = document.querySelectorAll('.content-checkbox:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    },
    
    /**
     * 导出内容
     */
    exportContents() {
        const selectedIds = this.getSelectedIds();
        
        if (selectedIds.length === 0) {
            if (!Utils.confirm('未选择任何内容，是否导出所有内容？')) {
                return;
            }
        }
        
        Toast.info('正在导出内容...');
        
        API.content.export(selectedIds)
            .then(data => {
                if (data.code === 200) {
                    // 创建下载链接
                    const link = document.createElement('a');
                    link.href = data.data.download_url;
                    link.download = data.data.filename;
                    link.style.display = 'none';
                    
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    
                    Toast.success('导出成功');
                } else {
                    Toast.error(data.message || '导出失败');
                }
            })
            .catch(error => {
                console.error('导出错误:', error);
                Toast.error('导出失败，请稍后重试');
            });
    },
    
    /**
     * 导入内容
     */
    importContents() {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.xlsx,.xls,.csv,.json';
        fileInput.style.display = 'none';
        
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            const formData = new FormData();
            formData.append('file', file);
            
            Toast.info('正在导入内容...');
            
            API.content.import(formData)
                .then(data => {
                    if (data.code === 200) {
                        Toast.success(data.message || '导入成功');
                        setTimeout(() => {
                            Utils.reload();
                        }, 1000);
                    } else {
                        Toast.error(data.message || '导入失败');
                    }
                })
                .catch(error => {
                    console.error('导入错误:', error);
                    Toast.error('导入失败，请稍后重试');
                });
        });
        
        document.body.appendChild(fileInput);
        fileInput.click();
        document.body.removeChild(fileInput);
    }
};

// 全局函数，供HTML调用
window.showContentModal = function(id, title, content) {
    ContentOperations.showContentModal(id, title, content);
};

window.deleteContent = function(contentId) {
    ContentOperations.delete(contentId);
};

window.copyContent = function(contentId) {
    ContentOperations.copy(contentId);
};

window.updateSort = function(contentId, newSort) {
    ContentOperations.updateSort(contentId, newSort);
};

window.batchOperation = function(operation) {
    ContentOperations.batchOperation(operation);
};

window.exportContents = function() {
    ContentOperations.exportContents();
};

window.importContents = function() {
    ContentOperations.importContents();
};

// 导出模块
window.ContentOperations = ContentOperations;
