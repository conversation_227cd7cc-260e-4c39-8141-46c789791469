/**
 * 控制台页面样式
 * 专门为控制台页面设计的样式
 */

/* 统计卡片样式 */
.stats-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-4) var(--spacing-5);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  transition: all var(--transition-normal);
  height: 100%;
  position: relative;
  overflow: hidden;
  min-height: 120px;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  color: var(--white);
  margin-bottom: var(--spacing-3);
  position: relative;
  z-index: 2;
}

.stats-value {
  font-size: 1.75rem;
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin-bottom: var(--spacing-2);
  line-height: 1;
}

.stats-label {
  color: var(--gray-600);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-2);
  font-weight: var(--font-weight-medium);
}

.stats-growth {
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  font-weight: var(--font-weight-medium);
}

.growth-positive {
  color: var(--success-color);
}

.growth-negative {
  color: var(--danger-color);
}

/* 图标颜色 */
.icon-primary { background-color: var(--primary-color); }
.icon-success { background-color: var(--success-color); }
.icon-warning { background-color: var(--warning-color); }
.icon-info { background-color: var(--info-color); }
.icon-purple { background-color: #722ed1; }

/* 概览容器样式 */
.overview-container {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

.overview-header {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
}

.overview-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 0;
  padding: 0;
}

.stats-grid .stats-card {
  border-radius: 0;
  border: none;
  border-right: var(--border-width) solid var(--border-color);
  box-shadow: none;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--spacing-5) var(--spacing-4);
  transition: all var(--transition-normal);
}

.stats-grid .stats-card:last-child {
  border-right: none;
}

.stats-grid .stats-card:hover {
  background-color: var(--gray-100);
  transform: none;
  box-shadow: inset 0 0 0 1px var(--primary-color);
}

/* 图表容器样式 */
.chart-container {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  margin-bottom: var(--spacing-6);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: var(--border-width) solid var(--border-color);
}

.chart-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.chart-controls {
  display: flex;
  gap: var(--spacing-2);
}

.chart-controls .btn {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-sm);
}

/* 快速操作样式 */
.quick-actions {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow);
  margin-bottom: var(--spacing-8);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-5) var(--spacing-4);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-lg);
  text-decoration: none;
  color: var(--gray-700);
  transition: all var(--transition-normal);
  background: var(--white);
  box-shadow: var(--shadow-sm);
}

.action-item:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  text-decoration: none;
}

.action-icon {
  font-size: 1.75rem;
  margin-bottom: var(--spacing-3);
  color: var(--gray-500);
  transition: color var(--transition-normal);
}

.action-item:hover .action-icon {
  color: var(--primary-color);
}

.action-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: center;
}

/* 全宽快速操作样式 */
.quick-actions-full {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

.quick-actions-header {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
}

.quick-actions-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.actions-grid-full {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 0;
  padding: 0;
}

.actions-grid-full .action-item {
  border-radius: 0;
  border: none;
  border-right: var(--border-width) solid var(--border-color);
  box-shadow: none;
  margin: 0;
  padding: var(--spacing-6) var(--spacing-4);
  background: var(--white);
}

.actions-grid-full .action-item:last-child {
  border-right: none;
}

.actions-grid-full .action-item:hover {
  background-color: var(--gray-100);
  transform: none;
  box-shadow: inset 0 0 0 1px var(--primary-color);
}

.actions-grid-full .action-item .action-icon {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-2);
}

/* 系统状态样式 */
.system-status-container {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow);
  border: var(--border-width) solid var(--border-color);
  overflow: hidden;
}

.system-status-header {
  padding: var(--spacing-4) var(--spacing-6);
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
  border-bottom: var(--border-width) solid var(--border-color);
}

.system-status-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
  margin: 0;
}

.system-status {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0;
  padding: 0;
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-5) var(--spacing-6);
  border-right: var(--border-width) solid var(--border-color);
  background-color: var(--white);
  transition: all var(--transition-normal);
}

.status-item:last-child {
  border-right: none;
}

.status-item:hover {
  background-color: var(--gray-100);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: var(--border-radius-full);
  display: inline-block;
}

.status-success {
  background-color: var(--success-color);
}

.status-warning {
  background-color: var(--warning-color);
}

.status-danger {
  background-color: var(--danger-color);
}

.status-text {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .actions-grid-full {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .system-status {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    padding: var(--spacing-4);
  }
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .actions-grid-full {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stats-value {
    font-size: 1.5rem;
  }
  
  .stats-icon {
    width: 36px;
    height: 36px;
    font-size: var(--font-size-base);
  }
}
