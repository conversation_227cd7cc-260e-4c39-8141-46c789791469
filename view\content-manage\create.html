{extend name="layout/base" /}

{block name="title"}创建内容{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">创建内容</h1>
        <p class="text-muted mb-0">创建新的内容，可用于生成卡密</p>
    </div>
    <div class="d-flex gap-2">
        <a href="/content-manage" class="modern-btn modern-btn-outline">
            <i class="fas fa-arrow-left"></i>
            返回列表
        </a>
    </div>
</div>

<!-- 创建表单 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-plus me-2"></i>
            内容信息
        </h5>
    </div>
    <div class="modern-card-body">
        <form id="createForm">
            <div class="row g-4">
                <div class="col-md-8">
                    <div class="modern-form-group">
                        <label class="modern-form-label">内容标题 <span class="text-danger">*</span></label>
                        <input type="text" name="title" class="modern-form-control" placeholder="请输入内容标题" required maxlength="255">
                        <small class="form-text text-muted">标题将在内容列表和卡密生成时显示</small>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="modern-form-group">
                        <label class="modern-form-label">所属分类 <span class="text-danger">*</span></label>
                        <select name="category_id" class="modern-form-control" required>
                            <option value="">请选择分类</option>
                            {volist name="categories" id="category"}
                            <option value="{$category.id}">{$category.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="modern-form-group">
                        <label class="modern-form-label">排序值</label>
                        <input type="number" name="sort_order" class="modern-form-control" placeholder="0" min="0" max="9999" value="0">
                        <small class="form-text text-muted">数字越大排序越靠前，默认为0</small>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="modern-form-group">
                        <label class="modern-form-label">状态</label>
                        <select name="status" class="modern-form-control">
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-12">
                    <div class="modern-form-group">
                        <label class="modern-form-label">内容详情 <span class="text-danger">*</span></label>
                        <textarea name="content" class="modern-form-control" rows="12" placeholder="请输入内容详情..." required></textarea>
                        <small class="form-text text-muted">支持换行和特殊字符，用户兑换卡密时将看到此内容</small>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-end gap-2 mt-4">
                <a href="/content-manage" class="modern-btn modern-btn-outline">
                    <i class="fas fa-times"></i>
                    取消
                </a>
                <button type="submit" class="modern-btn modern-btn-primary">
                    <i class="fas fa-save"></i>
                    保存内容
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 内容预览 -->
<div class="modern-card mt-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-eye me-2"></i>
            内容预览
        </h5>
    </div>
    <div class="modern-card-body">
        <div id="contentPreview" class="border rounded p-3 bg-light" style="min-height: 100px; white-space: pre-wrap;">
            在上方输入内容，这里将显示预览效果...
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createForm');
    const contentTextarea = form.querySelector('textarea[name="content"]');
    const titleInput = form.querySelector('input[name="title"]');
    const previewDiv = document.getElementById('contentPreview');
    
    // 实时预览
    function updatePreview() {
        const title = titleInput.value.trim();
        const content = contentTextarea.value.trim();
        
        if (title || content) {
            let previewText = '';
            if (title) {
                previewText += `标题: ${title}\n\n`;
            }
            if (content) {
                previewText += content;
            }
            previewDiv.textContent = previewText;
            previewDiv.classList.remove('text-muted');
        } else {
            previewDiv.textContent = '在上方输入内容，这里将显示预览效果...';
            previewDiv.classList.add('text-muted');
        }
    }
    
    titleInput.addEventListener('input', updatePreview);
    contentTextarea.addEventListener('input', updatePreview);
    
    // 表单提交
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // 显示加载状态
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        submitBtn.disabled = true;
        
        fetch('/content-manage/create', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                showToast('内容创建成功', 'success');
                setTimeout(() => {
                    window.location.href = '/content-manage';
                }, 1500);
            } else {
                showToast(data.message || '创建失败', 'error');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        })
        .catch(error => {
            showToast('网络错误，请重试', 'error');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});

// Toast消息提示函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}
</script>
{/block}
