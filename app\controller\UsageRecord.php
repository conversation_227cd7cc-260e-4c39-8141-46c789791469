<?php

namespace app\controller;

use app\BaseController;
use app\model\UsageRecord as UsageRecordModel;
use app\model\Category;
use think\facade\Db;

/**
 * 使用记录控制器
 */
class UsageRecord extends BaseController
{
    /**
     * 使用记录列表页面
     */
    public function index()
    {
        // 获取筛选参数
        $category_id = $this->request->param('category_id', '');
        $keyword = $this->request->param('keyword', '');
        $start_date = $this->request->param('start_date', '');
        $end_date = $this->request->param('end_date', '');
        $page = $this->request->param('page', 1);
        $limit = $this->request->param('limit', 20);

        // 构建查询条件
        $where = [];
        if ($category_id !== '') {
            $where[] = ['ur.category_id', '=', $category_id];
        }
        if ($keyword) {
            $where[] = ['ur.card_code', 'like', '%' . $keyword . '%'];
        }
        if ($start_date) {
            $where[] = ['ur.used_at', '>=', $start_date . ' 00:00:00'];
        }
        if ($end_date) {
            $where[] = ['ur.used_at', '<=', $end_date . ' 23:59:59'];
        }

        // 查询使用记录列表
        $records = UsageRecordModel::alias('ur')
            ->leftJoin('km_categories cat', 'ur.category_id = cat.id')
            ->leftJoin('km_cards c', 'ur.card_id = c.id')
            ->field([
                'ur.*',
                'cat.name as category_name',
                'c.content'
            ])
            ->where($where)
            ->order('ur.used_at', 'desc')
            ->paginate([
                'list_rows' => $limit,
                'page' => $page
            ]);

        // 获取分类列表
        $categories = Category::getEnabledCategories();

        // 获取统计数据
        $stats = $this->getUsageStats($where);

        // 构建导出URL
        $exportParams = [];
        if ($category_id !== '') $exportParams['category_id'] = $category_id;
        if ($keyword) $exportParams['keyword'] = $keyword;
        if ($start_date) $exportParams['start_date'] = $start_date;
        if ($end_date) $exportParams['end_date'] = $end_date;

        $exportUrl = empty($exportParams) ? '' : '?' . http_build_query($exportParams);

        return view('usage-record/index', [
            'records' => $records,
            'categories' => $categories,
            'stats' => $stats,
            'exportUrl' => $exportUrl,
            'filters' => [
                'category_id' => $category_id,
                'keyword' => $keyword,
                'start_date' => $start_date,
                'end_date' => $end_date
            ]
        ]);
    }

    /**
     * 导出使用记录
     */
    public function export()
    {
        // 获取筛选参数
        $category_id = $this->request->param('category_id', '');
        $keyword = $this->request->param('keyword', '');
        $start_date = $this->request->param('start_date', '');
        $end_date = $this->request->param('end_date', '');

        // 构建查询条件
        $where = [];
        if ($category_id !== '') {
            $where[] = ['ur.category_id', '=', $category_id];
        }
        if ($keyword) {
            $where[] = ['ur.card_code', 'like', '%' . $keyword . '%'];
        }
        if ($start_date) {
            $where[] = ['ur.used_at', '>=', $start_date . ' 00:00:00'];
        }
        if ($end_date) {
            $where[] = ['ur.used_at', '<=', $end_date . ' 23:59:59'];
        }

        // 查询数据
        $records = UsageRecordModel::alias('ur')
            ->leftJoin('km_categories cat', 'ur.category_id = cat.id')
            ->field([
                'ur.card_code',
                'cat.name as category_name',
                'ur.used_ip',
                'ur.user_agent',
                'ur.used_at'
            ])
            ->where($where)
            ->order('ur.used_at', 'desc')
            ->select();

        // 生成CSV内容
        $csv = "卡密编号,分类,使用IP,用户代理,使用时间\n";
        foreach ($records as $record) {
            $csv .= sprintf(
                "%s,%s,%s,%s,%s\n",
                $record['card_code'],
                $record['category_name'] ?: '未分类',
                $record['used_ip'] ?: '未知',
                str_replace(['"', ',', "\n", "\r"], ['""', '，', ' ', ' '], $record['user_agent'] ?: '未知'),
                $record['used_at']
            );
        }

        // 设置响应头
        $filename = '使用记录_' . date('Y-m-d_H-i-s') . '.csv';
        
        return response($csv, 200, [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            'Cache-Control' => 'max-age=0'
        ]);
    }

    /**
     * 获取使用统计数据
     */
    private function getUsageStats($where = [])
    {
        try {
            // 总使用次数
            $totalUsage = UsageRecordModel::alias('ur')->where($where)->count();

            // 今日使用次数
            $todayUsage = UsageRecordModel::alias('ur')
                ->where($where)
                ->where('ur.used_at', '>=', date('Y-m-d 00:00:00'))
                ->count();

            // 本周使用次数
            $weekStart = date('Y-m-d 00:00:00', strtotime('this week'));
            $weekUsage = UsageRecordModel::alias('ur')
                ->where($where)
                ->where('ur.used_at', '>=', $weekStart)
                ->count();

            // 本月使用次数
            $monthUsage = UsageRecordModel::alias('ur')
                ->where($where)
                ->where('ur.used_at', '>=', date('Y-m-01 00:00:00'))
                ->count();

            // 最近使用的IP数量
            $uniqueIps = UsageRecordModel::alias('ur')
                ->where($where)
                ->where('ur.used_at', '>=', date('Y-m-d 00:00:00', strtotime('-7 days')))
                ->group('ur.used_ip')
                ->count();

            return [
                'total_usage' => $totalUsage,
                'today_usage' => $todayUsage,
                'week_usage' => $weekUsage,
                'month_usage' => $monthUsage,
                'unique_ips' => $uniqueIps
            ];

        } catch (\Exception $e) {
            return [
                'total_usage' => 0,
                'today_usage' => 0,
                'week_usage' => 0,
                'month_usage' => 0,
                'unique_ips' => 0
            ];
        }
    }

    /**
     * 清理过期记录
     */
    public function cleanup()
    {
        try {
            $days = $this->request->param('days', 30);
            
            if ($days < 1) {
                return json(['code' => 400, 'message' => '保留天数必须大于0']);
            }

            $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
            
            $deletedCount = UsageRecordModel::where('used_at', '<', $cutoffDate)->delete();

            return json([
                'code' => 200, 
                'message' => "成功清理了 {$deletedCount} 条过期记录"
            ]);

        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '清理失败：' . $e->getMessage()]);
        }
    }
}
