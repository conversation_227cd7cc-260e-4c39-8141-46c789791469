/**
 * 本地存储工具
 * 提供localStorage和sessionStorage的封装
 */

const Storage = {
    /**
     * localStorage操作
     */
    local: {
        /**
         * 设置数据
         * @param {string} key 键
         * @param {any} value 值
         * @param {number} expire 过期时间（毫秒）
         */
        set(key, value, expire = null) {
            try {
                const data = {
                    value: value,
                    expire: expire ? Date.now() + expire : null
                };
                localStorage.setItem(key, JSON.stringify(data));
            } catch (error) {
                console.error('localStorage设置失败:', error);
            }
        },
        
        /**
         * 获取数据
         * @param {string} key 键
         * @param {any} defaultValue 默认值
         * @returns {any}
         */
        get(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                if (!item) return defaultValue;
                
                const data = JSON.parse(item);
                
                // 检查是否过期
                if (data.expire && Date.now() > data.expire) {
                    this.remove(key);
                    return defaultValue;
                }
                
                return data.value;
            } catch (error) {
                console.error('localStorage获取失败:', error);
                return defaultValue;
            }
        },
        
        /**
         * 删除数据
         * @param {string} key 键
         */
        remove(key) {
            try {
                localStorage.removeItem(key);
            } catch (error) {
                console.error('localStorage删除失败:', error);
            }
        },
        
        /**
         * 清空所有数据
         */
        clear() {
            try {
                localStorage.clear();
            } catch (error) {
                console.error('localStorage清空失败:', error);
            }
        },
        
        /**
         * 获取所有键
         * @returns {Array}
         */
        keys() {
            try {
                return Object.keys(localStorage);
            } catch (error) {
                console.error('localStorage获取键失败:', error);
                return [];
            }
        }
    },
    
    /**
     * sessionStorage操作
     */
    session: {
        /**
         * 设置数据
         * @param {string} key 键
         * @param {any} value 值
         */
        set(key, value) {
            try {
                sessionStorage.setItem(key, JSON.stringify(value));
            } catch (error) {
                console.error('sessionStorage设置失败:', error);
            }
        },
        
        /**
         * 获取数据
         * @param {string} key 键
         * @param {any} defaultValue 默认值
         * @returns {any}
         */
        get(key, defaultValue = null) {
            try {
                const item = sessionStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (error) {
                console.error('sessionStorage获取失败:', error);
                return defaultValue;
            }
        },
        
        /**
         * 删除数据
         * @param {string} key 键
         */
        remove(key) {
            try {
                sessionStorage.removeItem(key);
            } catch (error) {
                console.error('sessionStorage删除失败:', error);
            }
        },
        
        /**
         * 清空所有数据
         */
        clear() {
            try {
                sessionStorage.clear();
            } catch (error) {
                console.error('sessionStorage清空失败:', error);
            }
        },
        
        /**
         * 获取所有键
         * @returns {Array}
         */
        keys() {
            try {
                return Object.keys(sessionStorage);
            } catch (error) {
                console.error('sessionStorage获取键失败:', error);
                return [];
            }
        }
    },
    
    /**
     * Cookie操作
     */
    cookie: {
        /**
         * 设置Cookie
         * @param {string} name 名称
         * @param {string} value 值
         * @param {number} days 过期天数
         * @param {string} path 路径
         * @param {string} domain 域名
         */
        set(name, value, days = 7, path = '/', domain = '') {
            try {
                let expires = '';
                if (days) {
                    const date = new Date();
                    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                    expires = '; expires=' + date.toUTCString();
                }
                
                const domainStr = domain ? `; domain=${domain}` : '';
                document.cookie = `${name}=${value}${expires}; path=${path}${domainStr}`;
            } catch (error) {
                console.error('Cookie设置失败:', error);
            }
        },
        
        /**
         * 获取Cookie
         * @param {string} name 名称
         * @returns {string|null}
         */
        get(name) {
            try {
                const nameEQ = name + '=';
                const ca = document.cookie.split(';');
                
                for (let i = 0; i < ca.length; i++) {
                    let c = ca[i];
                    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                    if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
                }
                return null;
            } catch (error) {
                console.error('Cookie获取失败:', error);
                return null;
            }
        },
        
        /**
         * 删除Cookie
         * @param {string} name 名称
         * @param {string} path 路径
         * @param {string} domain 域名
         */
        remove(name, path = '/', domain = '') {
            this.set(name, '', -1, path, domain);
        }
    },
    
    /**
     * 检查存储支持
     * @returns {object}
     */
    checkSupport() {
        return {
            localStorage: typeof Storage !== 'undefined' && !!window.localStorage,
            sessionStorage: typeof Storage !== 'undefined' && !!window.sessionStorage,
            cookie: navigator.cookieEnabled
        };
    },
    
    /**
     * 获取存储使用情况
     * @returns {object}
     */
    getUsage() {
        const usage = {
            localStorage: { used: 0, total: 0 },
            sessionStorage: { used: 0, total: 0 }
        };
        
        try {
            // 计算localStorage使用量
            let localUsed = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    localUsed += localStorage[key].length + key.length;
                }
            }
            usage.localStorage.used = localUsed;
            
            // 计算sessionStorage使用量
            let sessionUsed = 0;
            for (let key in sessionStorage) {
                if (sessionStorage.hasOwnProperty(key)) {
                    sessionUsed += sessionStorage[key].length + key.length;
                }
            }
            usage.sessionStorage.used = sessionUsed;
            
            // 估算总容量（通常为5MB）
            usage.localStorage.total = 5 * 1024 * 1024;
            usage.sessionStorage.total = 5 * 1024 * 1024;
            
        } catch (error) {
            console.error('获取存储使用情况失败:', error);
        }
        
        return usage;
    }
};

// 导出到全局
window.Storage = Storage;
