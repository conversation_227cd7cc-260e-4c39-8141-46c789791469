{extend name="layout/base" /}

{block name="title"}卡密管理 - 卡密兑换管理系统{/block}

{block name="style"}
<!-- 使用统一的CSS架构，移除内联样式 -->
<link rel="stylesheet" href="/static/css/main.css">
<link rel="stylesheet" href="/static/css/pages/cards.css">
{/block}

{block name="content"}

<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">卡密管理</h1>
        <p class="text-muted mb-0">管理和监控所有卡密的状态</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline">
            <i class="fas fa-filter"></i>
            筛选
        </button>
        <button class="modern-btn modern-btn-success">
            <i class="fas fa-download"></i>
            导出
        </button>
        <a href="/cards/generate" class="modern-btn modern-btn-primary">
            <i class="fas fa-plus"></i>
            生成卡密
        </a>
    </div>
</div>

<!-- 统计概览 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="fas fa-credit-card"></i>
        </div>
        <div class="modern-stats-value">{$stats.total|default=0}</div>
        <div class="modern-stats-label">总卡密数</div>
        <div class="modern-stats-trend positive">
            <i class="fas fa-arrow-up"></i>
            +12%
        </div>
    </div>
    
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="modern-stats-value">{$stats.used|default=0}</div>
        <div class="modern-stats-label">已使用</div>
        <div class="modern-stats-trend positive">
            <i class="fas fa-arrow-up"></i>
            +8%
        </div>
    </div>
    
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="fas fa-clock"></i>
        </div>
        <div class="modern-stats-value">{$stats.unused|default=0}</div>
        <div class="modern-stats-label">未使用</div>
        <div class="modern-stats-trend negative">
            <i class="fas fa-arrow-down"></i>
            -2%
        </div>
    </div>
    
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);">
            <i class="fas fa-ban"></i>
        </div>
        <div class="modern-stats-value">{$stats.disabled|default=0}</div>
        <div class="modern-stats-label">已禁用</div>
        <div class="modern-stats-trend positive">
            <i class="fas fa-arrow-up"></i>
            +1%
        </div>
    </div>
</div>

<!-- 筛选区域 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-search me-2"></i>
            搜索筛选
        </h5>
        <button class="modern-btn modern-btn-outline btn-sm" onclick="resetFilters()">
            <i class="fas fa-undo"></i>
            重置
        </button>
    </div>
    <div class="modern-card-body">
        <form method="get" action="/cards">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="modern-form-group">
                        <label class="modern-form-label">分类</label>
                        <select name="category_id" class="modern-form-control">
                            <option value="">全部分类</option>
                            {volist name="categories" id="category"}
                            <option value="{$category.id}" {$filters.category_id == $category.id ? 'selected' : ''}>{$category.name}</option>
                            {/volist}
                        </select>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="modern-form-group">
                        <label class="modern-form-label">状态</label>
                        <select name="status" class="modern-form-control">
                            <option value="">全部状态</option>
                            <option value="unused" {$filters.status == 'unused' ? 'selected' : ''}>未使用</option>
                            <option value="used" {$filters.status == 'used' ? 'selected' : ''}>已使用</option>
                            <option value="disabled" {$filters.status == 'disabled' ? 'selected' : ''}>已禁用</option>
                        </select>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="modern-form-group">
                        <label class="modern-form-label">搜索关键词</label>
                        <input type="text" name="keyword" class="modern-form-control" placeholder="搜索卡密编号或内容" value="{$filters.keyword}">
                    </div>
                </div>
                
                <div class="col-md-2">
                    <div class="modern-form-group">
                        <label class="modern-form-label">&nbsp;</label>
                        <button type="submit" class="modern-btn modern-btn-primary w-100">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 卡密列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-list me-2"></i>
            卡密列表
        </h5>
        <div class="d-flex gap-2">
            <button class="modern-btn modern-btn-outline btn-sm" onclick="selectAll()">
                <i class="fas fa-check-square"></i>
                全选
            </button>
            <button class="modern-btn modern-btn-success btn-sm" onclick="batchExport()">
                <i class="fas fa-download"></i>
                批量导出
            </button>
        </div>
    </div>
    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                        </th>
                        <th>卡密编号</th>
                        <th>分类</th>
                        <th>内容</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>使用时间</th>
                        <th width="120">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="cards" id="card"}
                    <tr>
                        <td class="checkbox-cell">
                            <input type="checkbox" name="card_ids[]" value="{$card.id}" class="card-checkbox">
                        </td>
                        <td>
                            <span class="card-code">{$card.card_code}</span>
                        </td>
                        <td>
                            <span class="modern-badge modern-badge-primary">{$card.category_name|default='未分类'}</span>
                        </td>
                        <td>
                            <div class="text-truncate" style="max-width: 200px;" title="{$card.content}">
                                {$card.content|default='无内容'}
                            </div>
                        </td>
                        <td>
                            {switch name="card.status"}
                                {case value="unused"}
                                    <span class="status-badge status-pending">
                                        <i class="fas fa-clock"></i>
                                        未使用
                                    </span>
                                {/case}
                                {case value="used"}
                                    <span class="status-badge status-active">
                                        <i class="fas fa-check-circle"></i>
                                        已使用
                                    </span>
                                {/case}
                                {case value="disabled"}
                                    <span class="status-badge status-inactive">
                                        <i class="fas fa-ban"></i>
                                        已禁用
                                    </span>
                                {/case}
                            {/switch}
                        </td>
                        <td class="date-cell">{$card.created_at}</td>
                        <td class="date-cell">{$card.used_at|default='-'}</td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn btn-edit" onclick="editCard({$card.id})" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="action-btn btn-delete" onclick="deleteCard({$card.id})" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {/volist}
                    {empty name="cards"}
                    <tr>
                        <td colspan="8" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                                <div>暂无卡密数据</div>
                                <div class="mt-2">
                                    <a href="/cards/generate" class="modern-btn modern-btn-primary">
                                        <i class="fas fa-plus"></i>
                                        立即生成
                                    </a>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {/empty}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- 分页 -->
    {if condition="$cards->hasPages()"}
    <div class="modern-card-footer">
        <div class="modern-pagination-wrapper">
            <div class="pagination-info">
                <div class="info-text">
                    <i class="fas fa-info-circle"></i>
                    <span>显示第 <span class="info-numbers">{$cards->currentPage()}</span> 页，共 <span class="info-numbers">{$cards->lastPage()}</span> 页，总计 <span class="info-numbers">{$cards->total()}</span> 条记录</span>
                </div>
            </div>
            <div>
                {$cards->render()|raw}
            </div>
        </div>
    </div>
    {/if}
</div>

<script>
// 全选功能
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.card-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

// 重置筛选
function resetFilters() {
    window.location.href = '/cards';
}

// 批量导出
function batchExport() {
    const selected = document.querySelectorAll('.card-checkbox:checked');
    if (selected.length === 0) {
        alert('请选择要导出的卡密');
        return;
    }
    
    const ids = Array.from(selected).map(cb => cb.value);
    window.location.href = `/cards/export?ids=${ids.join(',')}`;
}

// 编辑卡密
function editCard(id) {
    window.location.href = `/cards/edit/${id}`;
}

// 删除卡密
function deleteCard(id) {
    if (confirm('确定要删除这个卡密吗？')) {
        fetch(`/cards/delete/${id}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                location.reload();
            } else {
                alert(data.message);
            }
        });
    }
}
</script>

{/block}
