/**
 * 间距工具类
 * 提供统一的内外边距工具类
 */

/* 外边距 (margin) */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-1) !important; }
.m-2 { margin: var(--spacing-2) !important; }
.m-3 { margin: var(--spacing-3) !important; }
.m-4 { margin: var(--spacing-4) !important; }
.m-5 { margin: var(--spacing-5) !important; }
.m-6 { margin: var(--spacing-6) !important; }
.m-8 { margin: var(--spacing-8) !important; }
.m-10 { margin: var(--spacing-10) !important; }
.m-12 { margin: var(--spacing-12) !important; }
.m-16 { margin: var(--spacing-16) !important; }
.m-20 { margin: var(--spacing-20) !important; }
.m-auto { margin: auto !important; }

/* 外边距 - 顶部 */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-1) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mt-5 { margin-top: var(--spacing-5) !important; }
.mt-6 { margin-top: var(--spacing-6) !important; }
.mt-8 { margin-top: var(--spacing-8) !important; }
.mt-10 { margin-top: var(--spacing-10) !important; }
.mt-12 { margin-top: var(--spacing-12) !important; }
.mt-16 { margin-top: var(--spacing-16) !important; }
.mt-20 { margin-top: var(--spacing-20) !important; }
.mt-auto { margin-top: auto !important; }

/* 外边距 - 右侧 */
.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--spacing-1) !important; }
.mr-2 { margin-right: var(--spacing-2) !important; }
.mr-3 { margin-right: var(--spacing-3) !important; }
.mr-4 { margin-right: var(--spacing-4) !important; }
.mr-5 { margin-right: var(--spacing-5) !important; }
.mr-6 { margin-right: var(--spacing-6) !important; }
.mr-8 { margin-right: var(--spacing-8) !important; }
.mr-10 { margin-right: var(--spacing-10) !important; }
.mr-12 { margin-right: var(--spacing-12) !important; }
.mr-16 { margin-right: var(--spacing-16) !important; }
.mr-20 { margin-right: var(--spacing-20) !important; }
.mr-auto { margin-right: auto !important; }

/* 外边距 - 底部 */
.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-1) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.mb-5 { margin-bottom: var(--spacing-5) !important; }
.mb-6 { margin-bottom: var(--spacing-6) !important; }
.mb-8 { margin-bottom: var(--spacing-8) !important; }
.mb-10 { margin-bottom: var(--spacing-10) !important; }
.mb-12 { margin-bottom: var(--spacing-12) !important; }
.mb-16 { margin-bottom: var(--spacing-16) !important; }
.mb-20 { margin-bottom: var(--spacing-20) !important; }
.mb-auto { margin-bottom: auto !important; }

/* 外边距 - 左侧 */
.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--spacing-1) !important; }
.ml-2 { margin-left: var(--spacing-2) !important; }
.ml-3 { margin-left: var(--spacing-3) !important; }
.ml-4 { margin-left: var(--spacing-4) !important; }
.ml-5 { margin-left: var(--spacing-5) !important; }
.ml-6 { margin-left: var(--spacing-6) !important; }
.ml-8 { margin-left: var(--spacing-8) !important; }
.ml-10 { margin-left: var(--spacing-10) !important; }
.ml-12 { margin-left: var(--spacing-12) !important; }
.ml-16 { margin-left: var(--spacing-16) !important; }
.ml-20 { margin-left: var(--spacing-20) !important; }
.ml-auto { margin-left: auto !important; }

/* 外边距 - 水平 */
.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--spacing-1) !important; margin-right: var(--spacing-1) !important; }
.mx-2 { margin-left: var(--spacing-2) !important; margin-right: var(--spacing-2) !important; }
.mx-3 { margin-left: var(--spacing-3) !important; margin-right: var(--spacing-3) !important; }
.mx-4 { margin-left: var(--spacing-4) !important; margin-right: var(--spacing-4) !important; }
.mx-5 { margin-left: var(--spacing-5) !important; margin-right: var(--spacing-5) !important; }
.mx-6 { margin-left: var(--spacing-6) !important; margin-right: var(--spacing-6) !important; }
.mx-8 { margin-left: var(--spacing-8) !important; margin-right: var(--spacing-8) !important; }
.mx-10 { margin-left: var(--spacing-10) !important; margin-right: var(--spacing-10) !important; }
.mx-12 { margin-left: var(--spacing-12) !important; margin-right: var(--spacing-12) !important; }
.mx-16 { margin-left: var(--spacing-16) !important; margin-right: var(--spacing-16) !important; }
.mx-20 { margin-left: var(--spacing-20) !important; margin-right: var(--spacing-20) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

/* 外边距 - 垂直 */
.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--spacing-1) !important; margin-bottom: var(--spacing-1) !important; }
.my-2 { margin-top: var(--spacing-2) !important; margin-bottom: var(--spacing-2) !important; }
.my-3 { margin-top: var(--spacing-3) !important; margin-bottom: var(--spacing-3) !important; }
.my-4 { margin-top: var(--spacing-4) !important; margin-bottom: var(--spacing-4) !important; }
.my-5 { margin-top: var(--spacing-5) !important; margin-bottom: var(--spacing-5) !important; }
.my-6 { margin-top: var(--spacing-6) !important; margin-bottom: var(--spacing-6) !important; }
.my-8 { margin-top: var(--spacing-8) !important; margin-bottom: var(--spacing-8) !important; }
.my-10 { margin-top: var(--spacing-10) !important; margin-bottom: var(--spacing-10) !important; }
.my-12 { margin-top: var(--spacing-12) !important; margin-bottom: var(--spacing-12) !important; }
.my-16 { margin-top: var(--spacing-16) !important; margin-bottom: var(--spacing-16) !important; }
.my-20 { margin-top: var(--spacing-20) !important; margin-bottom: var(--spacing-20) !important; }
.my-auto { margin-top: auto !important; margin-bottom: auto !important; }

/* 内边距 (padding) */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }
.p-6 { padding: var(--spacing-6) !important; }
.p-8 { padding: var(--spacing-8) !important; }
.p-10 { padding: var(--spacing-10) !important; }
.p-12 { padding: var(--spacing-12) !important; }
.p-16 { padding: var(--spacing-16) !important; }
.p-20 { padding: var(--spacing-20) !important; }

/* 内边距 - 顶部 */
.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--spacing-1) !important; }
.pt-2 { padding-top: var(--spacing-2) !important; }
.pt-3 { padding-top: var(--spacing-3) !important; }
.pt-4 { padding-top: var(--spacing-4) !important; }
.pt-5 { padding-top: var(--spacing-5) !important; }
.pt-6 { padding-top: var(--spacing-6) !important; }
.pt-8 { padding-top: var(--spacing-8) !important; }
.pt-10 { padding-top: var(--spacing-10) !important; }
.pt-12 { padding-top: var(--spacing-12) !important; }
.pt-16 { padding-top: var(--spacing-16) !important; }
.pt-20 { padding-top: var(--spacing-20) !important; }

/* 内边距 - 右侧 */
.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: var(--spacing-1) !important; }
.pr-2 { padding-right: var(--spacing-2) !important; }
.pr-3 { padding-right: var(--spacing-3) !important; }
.pr-4 { padding-right: var(--spacing-4) !important; }
.pr-5 { padding-right: var(--spacing-5) !important; }
.pr-6 { padding-right: var(--spacing-6) !important; }
.pr-8 { padding-right: var(--spacing-8) !important; }
.pr-10 { padding-right: var(--spacing-10) !important; }
.pr-12 { padding-right: var(--spacing-12) !important; }
.pr-16 { padding-right: var(--spacing-16) !important; }
.pr-20 { padding-right: var(--spacing-20) !important; }

/* 内边距 - 底部 */
.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--spacing-1) !important; }
.pb-2 { padding-bottom: var(--spacing-2) !important; }
.pb-3 { padding-bottom: var(--spacing-3) !important; }
.pb-4 { padding-bottom: var(--spacing-4) !important; }
.pb-5 { padding-bottom: var(--spacing-5) !important; }
.pb-6 { padding-bottom: var(--spacing-6) !important; }
.pb-8 { padding-bottom: var(--spacing-8) !important; }
.pb-10 { padding-bottom: var(--spacing-10) !important; }
.pb-12 { padding-bottom: var(--spacing-12) !important; }
.pb-16 { padding-bottom: var(--spacing-16) !important; }
.pb-20 { padding-bottom: var(--spacing-20) !important; }

/* 内边距 - 左侧 */
.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: var(--spacing-1) !important; }
.pl-2 { padding-left: var(--spacing-2) !important; }
.pl-3 { padding-left: var(--spacing-3) !important; }
.pl-4 { padding-left: var(--spacing-4) !important; }
.pl-5 { padding-left: var(--spacing-5) !important; }
.pl-6 { padding-left: var(--spacing-6) !important; }
.pl-8 { padding-left: var(--spacing-8) !important; }
.pl-10 { padding-left: var(--spacing-10) !important; }
.pl-12 { padding-left: var(--spacing-12) !important; }
.pl-16 { padding-left: var(--spacing-16) !important; }
.pl-20 { padding-left: var(--spacing-20) !important; }

/* 内边距 - 水平 */
.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--spacing-1) !important; padding-right: var(--spacing-1) !important; }
.px-2 { padding-left: var(--spacing-2) !important; padding-right: var(--spacing-2) !important; }
.px-3 { padding-left: var(--spacing-3) !important; padding-right: var(--spacing-3) !important; }
.px-4 { padding-left: var(--spacing-4) !important; padding-right: var(--spacing-4) !important; }
.px-5 { padding-left: var(--spacing-5) !important; padding-right: var(--spacing-5) !important; }
.px-6 { padding-left: var(--spacing-6) !important; padding-right: var(--spacing-6) !important; }
.px-8 { padding-left: var(--spacing-8) !important; padding-right: var(--spacing-8) !important; }
.px-10 { padding-left: var(--spacing-10) !important; padding-right: var(--spacing-10) !important; }
.px-12 { padding-left: var(--spacing-12) !important; padding-right: var(--spacing-12) !important; }
.px-16 { padding-left: var(--spacing-16) !important; padding-right: var(--spacing-16) !important; }
.px-20 { padding-left: var(--spacing-20) !important; padding-right: var(--spacing-20) !important; }

/* 内边距 - 垂直 */
.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--spacing-1) !important; padding-bottom: var(--spacing-1) !important; }
.py-2 { padding-top: var(--spacing-2) !important; padding-bottom: var(--spacing-2) !important; }
.py-3 { padding-top: var(--spacing-3) !important; padding-bottom: var(--spacing-3) !important; }
.py-4 { padding-top: var(--spacing-4) !important; padding-bottom: var(--spacing-4) !important; }
.py-5 { padding-top: var(--spacing-5) !important; padding-bottom: var(--spacing-5) !important; }
.py-6 { padding-top: var(--spacing-6) !important; padding-bottom: var(--spacing-6) !important; }
.py-8 { padding-top: var(--spacing-8) !important; padding-bottom: var(--spacing-8) !important; }
.py-10 { padding-top: var(--spacing-10) !important; padding-bottom: var(--spacing-10) !important; }
.py-12 { padding-top: var(--spacing-12) !important; padding-bottom: var(--spacing-12) !important; }
.py-16 { padding-top: var(--spacing-16) !important; padding-bottom: var(--spacing-16) !important; }
.py-20 { padding-top: var(--spacing-20) !important; padding-bottom: var(--spacing-20) !important; }
