/**
 * 设置表单组件
 * 处理设置表单的验证、保存和重置功能
 */

const SettingsForm = {
    /**
     * 初始化表单功能
     */
    init() {
        this.bindEvents();
        this.initFormValidation();
        this.saveOriginalValues();
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 绑定保存按钮事件
        const saveBtn = document.querySelector('[onclick="saveSettings()"]');
        if (saveBtn) {
            saveBtn.removeAttribute('onclick');
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }
        
        // 绑定重置按钮事件
        const resetBtn = document.querySelector('[onclick="resetForm()"]');
        if (resetBtn) {
            resetBtn.removeAttribute('onclick');
            resetBtn.addEventListener('click', () => {
                this.resetForm();
            });
        }
        
        // 绑定键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+S 保存设置
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveSettings();
            }
            // Ctrl+R 重置表单
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.resetForm();
            }
        });
        
        // 监听表单字段变化
        this.bindFieldChangeEvents();
    },
    
    /**
     * 绑定字段变化事件
     */
    bindFieldChangeEvents() {
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        const fields = form.querySelectorAll('input, textarea, select');
        fields.forEach(field => {
            field.addEventListener('input', () => {
                this.markFieldChanged(field);
                this.validateField(field);
            });
            
            field.addEventListener('change', () => {
                this.markFieldChanged(field);
                this.validateField(field);
            });
        });
    },
    
    /**
     * 标记字段已更改
     * @param {HTMLElement} field 字段元素
     */
    markFieldChanged(field) {
        const originalValue = field.dataset.originalValue;
        if (originalValue !== undefined && field.value !== originalValue) {
            field.classList.add('field-changed');
        } else {
            field.classList.remove('field-changed');
        }
        
        // 更新保存按钮状态
        this.updateSaveButtonState();
    },
    
    /**
     * 更新保存按钮状态
     */
    updateSaveButtonState() {
        const saveBtn = document.querySelector('.modern-btn-primary');
        const hasChanges = document.querySelectorAll('.field-changed').length > 0;
        
        if (saveBtn) {
            if (hasChanges) {
                saveBtn.classList.add('has-changes');
                saveBtn.disabled = false;
            } else {
                saveBtn.classList.remove('has-changes');
            }
        }
    },
    
    /**
     * 初始化表单验证
     */
    initFormValidation() {
        // 网站名称验证
        const siteNameField = document.getElementById('site_name');
        if (siteNameField) {
            siteNameField.addEventListener('input', () => {
                this.validateSiteName(siteNameField);
            });
        }
        
        // 邮箱验证
        const emailField = document.getElementById('admin_email');
        if (emailField) {
            emailField.addEventListener('input', () => {
                this.validateEmail(emailField);
            });
        }
        
        // 数字字段验证
        const numberFields = document.querySelectorAll('input[type="number"]');
        numberFields.forEach(field => {
            field.addEventListener('input', () => {
                this.validateNumber(field);
            });
        });
    },
    
    /**
     * 验证网站名称
     * @param {HTMLElement} field 字段元素
     */
    validateSiteName(field) {
        const value = field.value.trim();
        const errorElement = this.getOrCreateErrorElement(field);
        
        if (!value) {
            this.showFieldError(field, errorElement, '网站名称不能为空');
            return false;
        }
        
        if (value.length > 100) {
            this.showFieldError(field, errorElement, '网站名称长度不能超过100个字符');
            return false;
        }
        
        this.hideFieldError(field, errorElement);
        return true;
    },
    
    /**
     * 验证邮箱
     * @param {HTMLElement} field 字段元素
     */
    validateEmail(field) {
        const value = field.value.trim();
        const errorElement = this.getOrCreateErrorElement(field);
        
        if (value && !this.isValidEmail(value)) {
            this.showFieldError(field, errorElement, '请输入有效的邮箱地址');
            return false;
        }
        
        this.hideFieldError(field, errorElement);
        return true;
    },
    
    /**
     * 验证数字字段
     * @param {HTMLElement} field 字段元素
     */
    validateNumber(field) {
        const value = field.value;
        const min = parseInt(field.min);
        const max = parseInt(field.max);
        const errorElement = this.getOrCreateErrorElement(field);
        
        if (value && isNaN(parseInt(value))) {
            this.showFieldError(field, errorElement, '请输入有效的数字');
            return false;
        }
        
        const numValue = parseInt(value);
        if (!isNaN(min) && numValue < min) {
            this.showFieldError(field, errorElement, `值不能小于 ${min}`);
            return false;
        }
        
        if (!isNaN(max) && numValue > max) {
            this.showFieldError(field, errorElement, `值不能大于 ${max}`);
            return false;
        }
        
        this.hideFieldError(field, errorElement);
        return true;
    },
    
    /**
     * 验证单个字段
     * @param {HTMLElement} field 字段元素
     */
    validateField(field) {
        switch (field.type) {
            case 'email':
                return this.validateEmail(field);
            case 'number':
                return this.validateNumber(field);
            default:
                if (field.id === 'site_name') {
                    return this.validateSiteName(field);
                }
                return true;
        }
    },
    
    /**
     * 验证整个表单
     * @returns {boolean}
     */
    validateForm() {
        const form = document.getElementById('settingsForm');
        if (!form) return false;
        
        let isValid = true;
        const fields = form.querySelectorAll('input, textarea, select');
        
        fields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
            }
        });
        
        return isValid;
    },
    
    /**
     * 保存设置
     */
    saveSettings() {
        // 验证表单
        if (!this.validateForm()) {
            Toast.error('请检查表单中的错误信息');
            return;
        }
        
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        const formData = new FormData(form);
        
        // 处理复选框
        const checkboxes = form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            if (!checkbox.checked) {
                formData.set(checkbox.name, '0');
            } else {
                formData.set(checkbox.name, '1');
            }
        });
        
        // 设置加载状态
        this.setButtonLoading('.modern-btn-primary', true);
        
        // 发送请求
        API.settings.save(formData)
            .then(data => {
                if (data.code === 200) {
                    Toast.success(data.message || '设置保存成功');
                    this.saveOriginalValues(); // 更新原始值
                    this.clearFieldChanges(); // 清除更改标记
                } else {
                    Toast.error(data.message || '保存失败');
                }
            })
            .catch(error => {
                console.error('保存设置失败:', error);
                Toast.error('保存失败，请稍后重试');
            })
            .finally(() => {
                this.setButtonLoading('.modern-btn-primary', false);
            });
    },
    
    /**
     * 重置表单
     */
    resetForm() {
        if (!Utils.confirm('确定要重置所有设置吗？这将丢失所有未保存的更改。')) {
            return;
        }
        
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        // 恢复原始值
        const fields = form.querySelectorAll('input, textarea, select');
        fields.forEach(field => {
            if (field.dataset.originalValue !== undefined) {
                if (field.type === 'checkbox') {
                    field.checked = field.dataset.originalValue === '1';
                } else {
                    field.value = field.dataset.originalValue;
                }
            }
        });
        
        // 清除更改标记和错误信息
        this.clearFieldChanges();
        this.clearFieldErrors();
        
        Toast.info('表单已重置');
    },
    
    /**
     * 保存原始值
     */
    saveOriginalValues() {
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        const fields = form.querySelectorAll('input, textarea, select');
        fields.forEach(field => {
            if (field.type === 'checkbox') {
                field.dataset.originalValue = field.checked ? '1' : '0';
            } else {
                field.dataset.originalValue = field.value;
            }
        });
    },
    
    /**
     * 清除字段更改标记
     */
    clearFieldChanges() {
        document.querySelectorAll('.field-changed').forEach(field => {
            field.classList.remove('field-changed');
        });
        this.updateSaveButtonState();
    },
    
    /**
     * 清除字段错误信息
     */
    clearFieldErrors() {
        document.querySelectorAll('.field-error').forEach(error => {
            error.remove();
        });
        document.querySelectorAll('.has-error').forEach(field => {
            field.classList.remove('has-error');
        });
    },
    
    /**
     * 获取或创建错误元素
     * @param {HTMLElement} field 字段元素
     * @returns {HTMLElement}
     */
    getOrCreateErrorElement(field) {
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error';
            field.parentNode.appendChild(errorElement);
        }
        return errorElement;
    },
    
    /**
     * 显示字段错误
     * @param {HTMLElement} field 字段元素
     * @param {HTMLElement} errorElement 错误元素
     * @param {string} message 错误消息
     */
    showFieldError(field, errorElement, message) {
        field.classList.add('has-error');
        errorElement.textContent = message;
        errorElement.style.display = 'block';
    },
    
    /**
     * 隐藏字段错误
     * @param {HTMLElement} field 字段元素
     * @param {HTMLElement} errorElement 错误元素
     */
    hideFieldError(field, errorElement) {
        field.classList.remove('has-error');
        errorElement.style.display = 'none';
    },
    
    /**
     * 设置按钮加载状态
     * @param {string} selector 按钮选择器
     * @param {boolean} loading 是否加载中
     */
    setButtonLoading(selector, loading) {
        const button = document.querySelector(selector);
        if (!button) return;
        
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
        } else {
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-save"></i> 保存设置';
        }
    },
    
    /**
     * 检查邮箱格式
     * @param {string} email 邮箱地址
     * @returns {boolean}
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
};

// 全局函数，供HTML调用
window.saveSettings = function() {
    SettingsForm.saveSettings();
};

window.resetForm = function() {
    SettingsForm.resetForm();
};

// 导出模块
window.SettingsForm = SettingsForm;
