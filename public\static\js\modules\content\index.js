/**
 * 内容管理主控制器
 * 统一管理内容相关的所有功能
 */

const ContentManager = {
    /**
     * 初始化内容管理功能
     */
    init() {
        console.log('初始化内容管理功能...');
        
        // 初始化各个子模块
        this.initSubModules();
        
        // 绑定页面事件
        this.bindEvents();
        
        // 初始化页面状态
        this.initPageState();
        
        console.log('内容管理功能初始化完成');
    },
    
    /**
     * 初始化子模块
     */
    initSubModules() {
        // 初始化编辑器功能
        if (typeof ContentEditor !== 'undefined') {
            ContentEditor.init();
        }
        
        // 初始化操作功能
        if (typeof ContentOperations !== 'undefined') {
            ContentOperations.init();
        }
    },
    
    /**
     * 绑定页面事件
     */
    bindEvents() {
        // 绑定搜索功能
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 500);
            });
        }
        
        // 绑定分类筛选
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.addEventListener('change', (e) => {
                this.filterByCategory(e.target.value);
            });
        }
        
        // 绑定状态筛选
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filterByStatus(e.target.value);
            });
        }
        
        // 绑定排序功能
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortContents(e.target.value);
            });
        }
        
        // 绑定键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl+N 新建内容
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                window.location.href = '/content/create';
            }
            // Ctrl+F 聚焦搜索框
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
        
        // 绑定页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // 页面重新可见时，刷新数据
                this.refreshData();
            }
        });
    },
    
    /**
     * 初始化页面状态
     */
    initPageState() {
        // 从URL参数恢复筛选状态
        this.restoreFilterState();
        
        // 初始化表格排序
        this.initTableSorting();
        
        // 显示统计信息
        this.updateStatistics();
    },
    
    /**
     * 执行搜索
     * @param {string} keyword 搜索关键词
     */
    performSearch(keyword) {
        const params = new URLSearchParams(window.location.search);
        
        if (keyword.trim()) {
            params.set('search', keyword);
        } else {
            params.delete('search');
        }
        
        // 重置页码
        params.delete('page');
        
        // 更新URL并刷新页面
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.location.href = newUrl;
    },
    
    /**
     * 按分类筛选
     * @param {string} categoryId 分类ID
     */
    filterByCategory(categoryId) {
        const params = new URLSearchParams(window.location.search);
        
        if (categoryId) {
            params.set('category_id', categoryId);
        } else {
            params.delete('category_id');
        }
        
        // 重置页码
        params.delete('page');
        
        // 更新URL并刷新页面
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.location.href = newUrl;
    },
    
    /**
     * 按状态筛选
     * @param {string} status 状态
     */
    filterByStatus(status) {
        const params = new URLSearchParams(window.location.search);
        
        if (status !== '') {
            params.set('status', status);
        } else {
            params.delete('status');
        }
        
        // 重置页码
        params.delete('page');
        
        // 更新URL并刷新页面
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.location.href = newUrl;
    },
    
    /**
     * 排序内容
     * @param {string} sortBy 排序字段
     */
    sortContents(sortBy) {
        const params = new URLSearchParams(window.location.search);
        
        if (sortBy) {
            params.set('sort', sortBy);
        } else {
            params.delete('sort');
        }
        
        // 重置页码
        params.delete('page');
        
        // 更新URL并刷新页面
        const newUrl = `${window.location.pathname}?${params.toString()}`;
        window.location.href = newUrl;
    },
    
    /**
     * 恢复筛选状态
     */
    restoreFilterState() {
        const params = new URLSearchParams(window.location.search);
        
        // 恢复搜索关键词
        const searchInput = document.getElementById('searchInput');
        if (searchInput && params.has('search')) {
            searchInput.value = params.get('search');
        }
        
        // 恢复分类筛选
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter && params.has('category_id')) {
            categoryFilter.value = params.get('category_id');
        }
        
        // 恢复状态筛选
        const statusFilter = document.getElementById('statusFilter');
        if (statusFilter && params.has('status')) {
            statusFilter.value = params.get('status');
        }
        
        // 恢复排序
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect && params.has('sort')) {
            sortSelect.value = params.get('sort');
        }
    },
    
    /**
     * 初始化表格排序
     */
    initTableSorting() {
        const sortableHeaders = document.querySelectorAll('.sortable-header');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const field = header.dataset.sort;
                if (field) {
                    this.sortContents(field);
                }
            });
        });
    },
    
    /**
     * 更新统计信息
     */
    updateStatistics() {
        const totalCount = document.querySelectorAll('.content-checkbox').length;
        const enabledCount = document.querySelectorAll('.status-badge.status-enabled').length;
        const disabledCount = totalCount - enabledCount;
        
        // 更新统计显示
        const statsElement = document.getElementById('contentStats');
        if (statsElement) {
            statsElement.innerHTML = `
                总计: ${totalCount} | 
                启用: ${enabledCount} | 
                禁用: ${disabledCount}
            `;
        }
    },
    
    /**
     * 刷新数据
     */
    refreshData() {
        // 检查是否有新的内容更新
        API.content.getUpdateTime()
            .then(data => {
                if (data.code === 200) {
                    const lastUpdate = localStorage.getItem('content_last_update');
                    if (lastUpdate && data.data.last_update > lastUpdate) {
                        // 有新的更新，询问是否刷新
                        if (Utils.confirm('检测到内容有更新，是否刷新页面？')) {
                            Utils.reload();
                        }
                    }
                    localStorage.setItem('content_last_update', data.data.last_update);
                }
            })
            .catch(error => {
                console.error('检查更新失败:', error);
            });
    },
    
    /**
     * 清空筛选条件
     */
    clearFilters() {
        window.location.href = window.location.pathname;
    },
    
    /**
     * 显示高级搜索
     */
    showAdvancedSearch() {
        const modal = document.getElementById('advancedSearchModal');
        if (modal) {
            Modal.show('advancedSearchModal');
        } else {
            // 如果没有高级搜索模态框，显示简单的提示
            Toast.info('高级搜索功能开发中...');
        }
    },
    
    /**
     * 获取内容统计
     */
    getContentStats() {
        return {
            total: document.querySelectorAll('.content-checkbox').length,
            enabled: document.querySelectorAll('.status-badge.status-enabled').length,
            disabled: document.querySelectorAll('.status-badge.status-disabled').length,
            selected: document.querySelectorAll('.content-checkbox:checked').length
        };
    },
    
    /**
     * 预览内容
     * @param {number} contentId 内容ID
     */
    previewContent(contentId) {
        API.content.get(contentId)
            .then(data => {
                if (data.code === 200) {
                    const content = data.data;
                    ContentOperations.showContentModal(content.id, content.title, content.content);
                } else {
                    Toast.error('获取内容失败');
                }
            })
            .catch(error => {
                console.error('预览内容错误:', error);
                Toast.error('预览失败，请稍后重试');
            });
    }
};

// 全局函数，供HTML调用
window.clearFilters = function() {
    ContentManager.clearFilters();
};

window.showAdvancedSearch = function() {
    ContentManager.showAdvancedSearch();
};

window.previewContent = function(contentId) {
    ContentManager.previewContent(contentId);
};

window.refreshData = function() {
    ContentManager.refreshData();
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保所有依赖都已加载
    if (typeof Utils !== 'undefined' && 
        typeof API !== 'undefined' && 
        typeof Toast !== 'undefined') {
        ContentManager.init();
    } else {
        console.error('内容管理功能初始化失败：缺少依赖模块');
    }
});

// 导出模块
window.ContentManager = ContentManager;
