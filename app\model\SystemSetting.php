<?php

namespace app\model;

use think\Model;

/**
 * 系统设置模型
 */
class SystemSetting extends Model
{
    // 设置表名
    protected $table = 'km_system_settings';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'setting_key'   => 'string',
        'setting_value' => 'string',
        'setting_type'  => 'string',
        'description'   => 'string',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 定义时间戳字段名
    protected $createTime = 'created_at';
    protected $updateTime = 'updated_at';
    
    // 设置类型常量
    const TYPE_STRING = 'string';
    const TYPE_INT = 'int';
    const TYPE_BOOL = 'bool';
    const TYPE_JSON = 'json';
    
    /**
     * 值获取器 - 根据类型转换
     */
    public function getSettingValueAttr($value, $data)
    {
        $type = $data['setting_type'] ?? self::TYPE_STRING;
        
        switch ($type) {
            case self::TYPE_INT:
                return (int) $value;
            case self::TYPE_BOOL:
                return (bool) $value;
            case self::TYPE_JSON:
                return json_decode($value, true) ?: [];
            default:
                return $value;
        }
    }
    
    /**
     * 值修改器 - 根据类型转换
     */
    public function setSettingValueAttr($value, $data)
    {
        $type = $data['setting_type'] ?? self::TYPE_STRING;
        
        switch ($type) {
            case self::TYPE_JSON:
                return json_encode($value);
            case self::TYPE_BOOL:
                return $value ? '1' : '0';
            default:
                return (string) $value;
        }
    }
    
    /**
     * 获取设置值
     */
    public static function getValue($key, $default = null)
    {
        $setting = self::where('setting_key', $key)->find();
        return $setting ? $setting->setting_value : $default;
    }
    
    /**
     * 设置值
     */
    public static function setValue($key, $value, $type = self::TYPE_STRING, $description = null)
    {
        $setting = self::where('setting_key', $key)->find();
        
        if ($setting) {
            $setting->setting_value = $value;
            $setting->setting_type = $type;
            if ($description !== null) {
                $setting->description = $description;
            }
            return $setting->save();
        } else {
            return self::create([
                'setting_key' => $key,
                'setting_value' => $value,
                'setting_type' => $type,
                'description' => $description
            ]);
        }
    }
    
    /**
     * 批量获取设置
     */
    public static function getSettings($keys = [])
    {
        $query = self::field(['setting_key', 'setting_value', 'setting_type']);
        
        if (!empty($keys)) {
            $query->whereIn('setting_key', $keys);
        }
        
        $settings = $query->select();
        $result = [];
        
        foreach ($settings as $setting) {
            $result[$setting->setting_key] = $setting->setting_value;
        }
        
        return $result;
    }
    
    /**
     * 获取系统状态
     */
    public static function getSystemStatus()
    {
        // 模拟系统状态检查
        return [
            'server_status' => [
                'status' => 'normal',
                'text' => '正常运行',
                'color' => 'success'
            ],
            'database_status' => [
                'status' => 'normal', 
                'text' => '已连接',
                'color' => 'success'
            ],
            'storage_status' => [
                'status' => 'normal',
                'text' => '85% 已使用',
                'color' => 'warning'
            ]
        ];
    }
}
