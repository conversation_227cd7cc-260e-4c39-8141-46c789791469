<?php /*a:2:{s:46:"F:\linshi\thphp\kmxt\view\dashboard\index.html";i:1754114473;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1754115353;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- 主样式文件 -->
    <link rel="stylesheet" href="/static/css/main.css">

    <!-- 自定义样式 -->
    <style>
        /* 基础模板中只保留必要的CSS变量，其他变量由外部CSS文件提供 */

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            overflow-x: hidden; /* 防止水平滚动条闪烁 */
        }
        
        /* 优化动画性能 */
        .sidebar,
        .main-content {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        /* 防止初始化时的闪烁 */
        .sidebar-loading .sidebar,
        .sidebar-loading .main-content {
            transition: none !important;
        }

        /* 防止导航激活状态闪烁 */
        .nav-loading .nav-link {
            transition: none !important;
        }














        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: var(--card-bg);
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
            border-bottom: 1px solid var(--border-color);
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: var(--text-secondary);
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: var(--content-bg);
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: var(--text-primary);
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: var(--content-bg);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: var(--text-primary);
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: var(--content-bg);
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: var(--text-secondary);
            margin-right: 1rem;
        }

        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: var(--text-secondary);
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
<!-- 使用统一的CSS架构，移除内联样式 -->
<link rel="stylesheet" href="/static/css/main.css">
<link rel="stylesheet" href="/static/css/pages/dashboard.css">





</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">控制台</h1>
        <p class="text-muted mb-0">欢迎回来，这里是您的数据概览</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline" onclick="location.reload()">
            <i class="fas fa-refresh"></i>
            刷新数据
        </button>
        <a href="/cards/generate" class="modern-btn modern-btn-primary">
            <i class="fas fa-plus"></i>
            生成卡密
        </a>
    </div>
</div>

<!-- 数据概览 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="<?php echo htmlentities((string) $cardStats['total_cards']['icon']); ?>"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: <?php echo htmlentities((string) $cardStats['total_cards']['raw_value']); ?>"><?php echo htmlentities((string) $cardStats['total_cards']['value']); ?></div>
        <div class="modern-stats-label">总卡密数</div>
        <div class="modern-stats-trend <?php echo $cardStats['total_cards']['growth']>=0 ? 'positive'  :  'negative'; ?>">
            <i class="fas fa-arrow-<?php echo $cardStats['total_cards']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
            <?php echo htmlentities((string) $cardStats['total_cards']['growth']); ?>%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="<?php echo htmlentities((string) $cardStats['used_cards']['icon']); ?>"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: <?php echo htmlentities((string) $cardStats['used_cards']['raw_value']); ?>"><?php echo htmlentities((string) $cardStats['used_cards']['value']); ?></div>
        <div class="modern-stats-label">已使用</div>
        <div class="modern-stats-trend <?php echo $cardStats['used_cards']['growth']>=0 ? 'positive'  :  'negative'; ?>">
            <i class="fas fa-arrow-<?php echo $cardStats['used_cards']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
            <?php echo htmlentities((string) $cardStats['used_cards']['growth']); ?>%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="<?php echo htmlentities((string) $cardStats['unused_cards']['icon']); ?>"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: <?php echo htmlentities((string) $cardStats['unused_cards']['raw_value']); ?>"><?php echo htmlentities((string) $cardStats['unused_cards']['value']); ?></div>
        <div class="modern-stats-label">未使用</div>
        <div class="modern-stats-trend <?php echo $cardStats['unused_cards']['growth']>=0 ? 'positive'  :  'negative'; ?>">
            <i class="fas fa-arrow-<?php echo $cardStats['unused_cards']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
            <?php echo htmlentities((string) $cardStats['unused_cards']['growth']); ?>%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);">
            <i class="<?php echo htmlentities((string) $cardStats['disabled_cards']['icon']); ?>"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: <?php echo htmlentities((string) $cardStats['disabled_cards']['raw_value']); ?>"><?php echo htmlentities((string) $cardStats['disabled_cards']['value']); ?></div>
        <div class="modern-stats-label">已禁用</div>
        <div class="modern-stats-trend <?php echo $cardStats['disabled_cards']['growth']>=0 ? 'positive'  :  'negative'; ?>">
            <i class="fas fa-arrow-<?php echo $cardStats['disabled_cards']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
            <?php echo htmlentities((string) $cardStats['disabled_cards']['growth']); ?>%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--purple-color) 0%, var(--purple-light) 100%);">
            <i class="<?php echo htmlentities((string) $cardStats['category_count']['icon']); ?>"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: <?php echo htmlentities((string) $cardStats['category_count']['raw_value']); ?>"><?php echo htmlentities((string) $cardStats['category_count']['value']); ?></div>
        <div class="modern-stats-label">分类总数</div>
        <div class="modern-stats-trend <?php echo $cardStats['category_count']['growth']>=0 ? 'positive'  :  'negative'; ?>">
            <i class="fas fa-arrow-<?php echo $cardStats['category_count']['growth']>=0 ? 'up'  :  'down'; ?>"></i>
            <?php echo htmlentities((string) $cardStats['category_count']['growth']); ?>%
        </div>
    </div>
</div>

<!-- 快速操作中心 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-bolt me-2"></i>
            快速操作
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="row g-3">
            <div class="col-md-2">
                <a href="/cards/generate" class="modern-btn modern-btn-primary w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                    <span>生成卡密</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/cards" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-credit-card fa-2x mb-2"></i>
                    <span>卡密管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/categories" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-tags fa-2x mb-2"></i>
                    <span>分类管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/content" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <span>内容管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="#" class="modern-btn modern-btn-success w-100 d-flex flex-column align-items-center py-3" onclick="showExportModal()">
                    <i class="fas fa-download fa-2x mb-2"></i>
                    <span>导出数据</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/settings" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-cog fa-2x mb-2"></i>
                    <span>系统设置</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row g-4 mb-4">
    <!-- 使用趋势图表 -->
    <div class="col-lg-8">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-chart-line me-2"></i>
                    卡密使用趋势
                </h5>
                <div class="d-flex gap-1">
                    <button class="modern-btn modern-btn-primary btn-sm active" data-period="day">日</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="week">周</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="month">月</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="year">年</button>
                </div>
            </div>
            <div class="modern-card-body">
                <div style="height: 300px;">
                    <canvas id="usageTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类占比图表 -->
    <div class="col-lg-4">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-chart-pie me-2"></i>
                    分类占比
                </h5>
            </div>
            <div class="modern-card-body">
                <div style="height: 300px;">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 最近使用记录 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-history me-2"></i>
            最近使用记录
        </h5>
        <a href="/usage-records" class="modern-btn modern-btn-outline btn-sm">查看全部</a>
    </div>
    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>卡密编号</th>
                        <th>分类</th>
                        <th>使用时间</th>
                        <th>使用IP</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if(is_array($recentRecords) || $recentRecords instanceof \think\Collection || $recentRecords instanceof \think\Paginator): $i = 0; $__LIST__ = $recentRecords;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$record): $mod = ($i % 2 );++$i;?>
                    <tr>
                        <td><code class="text-primary"><?php echo htmlentities((string) $record['masked_card_code']); ?></code></td>
                        <td><span class="modern-badge modern-badge-primary"><?php echo htmlentities((string) $record['category_name']); ?></span></td>
                        <td class="text-muted"><?php echo htmlentities((string) $record['used_at']); ?></td>
                        <td class="text-muted"><?php echo htmlentities((string) (isset($record['used_ip']) && ($record['used_ip'] !== '')?$record['used_ip']:'未知')); ?></td>
                        <td>
                            <span class="modern-badge modern-badge-success">
                                <i class="fas fa-check-circle"></i>
                                已使用
                            </span>
                        </td>
                    </tr>
                    <?php endforeach; endif; else: echo "" ;endif; if(empty($recentRecords) || (($recentRecords instanceof \think\Collection || $recentRecords instanceof \think\Paginator ) && $recentRecords->isEmpty())): ?>
                    <tr>
                        <td colspan="5" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                                <div>暂无使用记录</div>
                            </div>
                        </td>
                    </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 系统状态监控 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-server me-2"></i>
            系统状态
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="row g-4">
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-<?php echo htmlentities((string) $systemStatus['server_status']['color']); ?> me-3"></div>
                    <div>
                        <div class="fw-bold">服务器状态</div>
                        <small class="text-muted"><?php echo htmlentities((string) $systemStatus['server_status']['text']); ?></small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-<?php echo htmlentities((string) $systemStatus['database_status']['color']); ?> me-3"></div>
                    <div>
                        <div class="fw-bold">数据库连接</div>
                        <small class="text-muted"><?php echo htmlentities((string) $systemStatus['database_status']['text']); ?></small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-<?php echo htmlentities((string) $systemStatus['storage_status']['color']); ?> me-3"></div>
                    <div>
                        <div class="fw-bold">存储空间</div>
                        <small class="text-muted"><?php echo htmlentities((string) $systemStatus['storage_status']['text']); ?></small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导出数据模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportType" class="form-label">导出类型</label>
                        <select class="form-select" id="exportType">
                            <option value="all">全部数据</option>
                            <option value="cards">卡密数据</option>
                            <option value="usage">使用记录</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportData()">导出</button>
            </div>
        </div>
    </div>
</div>

        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.isAnimating = false;
                this.resizeTimeout = null;

                this.init();
            }

            init() {
                // 绑定事件
                this.sidebarToggle.addEventListener('click', (e) => this.handleToggle(e));
                document.addEventListener('click', (e) => this.handleOutsideClick(e));
                window.addEventListener('resize', () => this.handleResize());

                // 初始化状态
                this.updateLayout();
            }

            handleToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.isAnimating) return;

                this.isAnimating = true;

                if (window.innerWidth <= 768) {
                    this.toggleMobile();
                } else {
                    this.toggleDesktop();
                }

                // 动画完成后重置标志
                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }

            toggleMobile() {
                this.sidebar.classList.toggle('show');
            }

            toggleDesktop() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    this.sidebar.classList.add('collapsed');
                    this.mainContent.classList.add('expanded');
                }
            }

            handleOutsideClick(e) {
                if (window.innerWidth <= 768) {
                    if (!this.sidebar.contains(e.target) && !this.sidebarToggle.contains(e.target)) {
                        this.sidebar.classList.remove('show');
                    }
                }
            }

            handleResize() {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateLayout();
                }, 150);
            }

            updateLayout() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    // 移动端：移除桌面端的类
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    // 桌面端：移除移动端的类
                    this.sidebar.classList.remove('show');
                }
            }
        }

        // 初始化侧边栏管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载类防止初始化闪烁
            document.body.classList.add('sidebar-loading');

            // 初始化管理器
            new SidebarManager();

            // 移除加载类，启用过渡效果
            setTimeout(() => {
                document.body.classList.remove('sidebar-loading');
            }, 100);
        });

        // 导航激活状态管理器
        class NavigationManager {
            constructor() {
                this.currentPath = window.location.pathname;
                this.navLinks = document.querySelectorAll('.nav-link');
                this.init();
            }

            init() {
                // 添加导航加载类，暂时禁用过渡效果
                document.body.classList.add('nav-loading');

                // 立即设置激活状态，避免闪烁
                this.setActiveState();

                // 移除加载类，启用过渡效果
                setTimeout(() => {
                    document.body.classList.remove('nav-loading');
                }, 50);

                // 监听页面变化（如果使用了PJAX或类似技术）
                window.addEventListener('popstate', () => {
                    this.currentPath = window.location.pathname;
                    this.setActiveState();
                });
            }

            setActiveState() {
                // 使用requestAnimationFrame确保在下一帧执行，避免闪烁
                requestAnimationFrame(() => {
                    this.navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        const isActive = this.isLinkActive(href);

                        // 只在状态真正改变时才操作DOM
                        if (isActive && !link.classList.contains('active')) {
                            link.classList.add('active');
                        } else if (!isActive && link.classList.contains('active')) {
                            link.classList.remove('active');
                        }
                    });
                });
            }

            isLinkActive(href) {
                if (!href) return false;

                // 精确匹配路径
                if (this.currentPath === href) {
                    return true;
                }

                // 处理子路径匹配
                if (href !== '/' && this.currentPath.startsWith(href + '/')) {
                    return true;
                }

                // 特殊处理：根路径只在完全匹配时激活
                if (href === '/' && this.currentPath === '/') {
                    return true;
                }

                return false;
            }
        }

        // 初始化导航管理器
        document.addEventListener('DOMContentLoaded', function() {
            new NavigationManager();
        });
    </script>
    
    
<!-- 引入JavaScript模块 -->
<script src="/static/js/common.js"></script>
<script src="/static/js/api.js"></script>
<script src="/static/js/components/modal.js"></script>
<script src="/static/js/components/toast.js"></script>
<script src="/static/js/modules/dashboard/index.js"></script>
<script>
// 页面初始化数据
window.dashboardData = {
    usageTrend: <?php echo json_encode($usageTrend); ?>,
    categoryStats: <?php echo json_encode($categoryStats); ?>
};






</script>

</body>
</html>
