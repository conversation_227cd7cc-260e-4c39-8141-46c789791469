/**
 * 分类管理主控制器
 * 统一管理分类相关的所有功能
 */

const CategoryManager = {
    /**
     * 初始化分类管理功能
     */
    init() {
        console.log('初始化分类管理功能...');
        
        // 初始化各个子模块
        this.initSubModules();
        
        // 绑定页面事件
        this.bindEvents();
        
        // 初始化树形选择器
        this.initTreeSelector();
        
        console.log('分类管理功能初始化完成');
    },
    
    /**
     * 初始化子模块
     */
    initSubModules() {
        // 初始化分类树功能
        if (typeof CategoryTree !== 'undefined') {
            CategoryTree.init();
        }
        
        // 初始化操作功能
        if (typeof CategoryOperations !== 'undefined') {
            CategoryOperations.init();
        }
    },
    
    /**
     * 绑定页面事件
     */
    bindEvents() {
        // 绑定树形选择器切换事件
        const treeSelectorInput = document.querySelector('.tree-selector-input');
        if (treeSelectorInput) {
            treeSelectorInput.addEventListener('click', () => {
                this.toggleTreeSelector();
            });
        }
        
        // 绑定模态框关闭事件
        const closeButtons = document.querySelectorAll('#categoryModal .modal-close, #categoryModal .modern-btn-outline');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => CategoryOperations.closeModal());
        });
        
        // 绑定表单提交按钮事件
        const submitButton = document.querySelector('#categoryModal .modern-btn-primary');
        if (submitButton) {
            submitButton.addEventListener('click', () => CategoryOperations.submitForm());
        }
        
        // 绑定ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const modal = document.getElementById('categoryModal');
                if (modal && modal.style.display === 'block') {
                    CategoryOperations.closeModal();
                }
            }
        });
        
        // 绑定模态框背景点击关闭
        const modal = document.getElementById('categoryModal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    CategoryOperations.closeModal();
                }
            });
        }
        
        // 绑定点击外部关闭树形选择器
        document.addEventListener('click', (e) => {
            const treeSelector = document.querySelector('.category-tree-selector');
            if (treeSelector && !treeSelector.contains(e.target)) {
                CategoryTree.closeTreeSelector();
            }
        });
    },
    
    /**
     * 初始化树形选择器
     */
    initTreeSelector() {
        // 设置初始状态
        CategoryTree.closeTreeSelector();
    },
    
    /**
     * 切换树形选择器
     */
    toggleTreeSelector() {
        const dropdown = document.getElementById('treeSelectorDropdown');
        if (!dropdown) return;
        
        if (dropdown.style.display === 'none' || dropdown.style.display === '') {
            CategoryTree.openTreeSelector();
        } else {
            CategoryTree.closeTreeSelector();
        }
    },
    
    /**
     * 刷新页面数据
     */
    refresh() {
        Utils.reload();
    },
    
    /**
     * 获取选中的分类信息
     * @returns {object|null}
     */
    getSelectedCategoryInfo() {
        const selectedId = CategoryTree.getSelectedCategoryId();
        if (!selectedId) return null;
        
        const selectedRow = document.querySelector(`[data-id="${selectedId}"]`);
        if (!selectedRow) return null;
        
        return {
            id: selectedId,
            level: selectedRow.dataset.level,
            parent: selectedRow.dataset.parent,
            sort: selectedRow.dataset.sort
        };
    },
    
    /**
     * 展开到指定分类
     * @param {number} categoryId 分类ID
     */
    expandToCategory(categoryId) {
        CategoryTree.expandToCategory(categoryId);
    },
    
    /**
     * 展开所有分类
     */
    expandAll() {
        // 展开所有树形节点
        document.querySelectorAll('.tree-node').forEach(node => {
            const expandBtn = node.querySelector('.tree-expand-btn');
            const childrenContainer = node.querySelector('.tree-children');

            if (childrenContainer && !expandBtn.classList.contains('no-children')) {
                childrenContainer.style.display = 'block';
                childrenContainer.classList.add('expanded');
                expandBtn.classList.add('expanded');
            }
        });
    },

    /**
     * 收起所有分类
     */
    collapseAll() {
        // 收起所有树形节点
        document.querySelectorAll('.tree-node').forEach(node => {
            const expandBtn = node.querySelector('.tree-expand-btn');
            const childrenContainer = node.querySelector('.tree-children');

            if (childrenContainer && !expandBtn.classList.contains('no-children')) {
                childrenContainer.style.display = 'none';
                childrenContainer.classList.remove('expanded');
                expandBtn.classList.remove('expanded');
            }
        });
    },
    
    /**
     * 显示分类统计信息
     */
    showStats() {
        const totalRows = document.querySelectorAll('.category-row').length;
        const level1Count = document.querySelectorAll('.category-row[data-level="1"]').length;
        const level2Count = document.querySelectorAll('.category-row[data-level="2"]').length;
        const level3Count = document.querySelectorAll('.category-row[data-level="3"]').length;
        
        console.log(`分类统计: 总计 ${totalRows} 个分类`);
        console.log(`一级分类: ${level1Count} 个`);
        console.log(`二级分类: ${level2Count} 个`);
        console.log(`三级分类: ${level3Count} 个`);
        
        return {
            total: totalRows,
            level1: level1Count,
            level2: level2Count,
            level3: level3Count
        };
    },
    
    /**
     * 导出分类数据
     */
    exportCategories() {
        // 创建导出表单
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/categories/export';
        form.style.display = 'none';
        
        // 添加到页面并提交
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
        
        Toast.info('正在导出分类数据...');
    },
    
    /**
     * 导入分类数据
     */
    importCategories() {
        // 创建文件输入框
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.xlsx,.xls,.csv';
        fileInput.style.display = 'none';
        
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            const formData = new FormData();
            formData.append('file', file);
            
            // 发送导入请求
            fetch('/categories/import', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    Toast.success(data.message || '导入成功');
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '导入失败');
                }
            })
            .catch(error => {
                console.error('导入错误:', error);
                Toast.error('导入失败，请稍后重试');
            });
        });
        
        // 触发文件选择
        document.body.appendChild(fileInput);
        fileInput.click();
        document.body.removeChild(fileInput);
    }
};

// 全局函数，供HTML调用
window.toggleTreeSelector = function() {
    CategoryManager.toggleTreeSelector();
};

window.exportCategories = function() {
    CategoryManager.exportCategories();
};

window.importCategories = function() {
    CategoryManager.importCategories();
};

window.expandAll = function() {
    CategoryManager.expandAll();
};

window.collapseAll = function() {
    CategoryManager.collapseAll();
};

window.showHierarchyHelp = function() {
    const modal = document.getElementById('hierarchyModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
};

window.closeHierarchyModal = function() {
    const modal = document.getElementById('hierarchyModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保所有依赖都已加载
    if (typeof Utils !== 'undefined' &&
        typeof API !== 'undefined' &&
        typeof Modal !== 'undefined') {
        CategoryManager.init();
    } else {
        // 即使某些依赖不存在，也尝试初始化基本功能
        try {
            CategoryManager.init();
        } catch (error) {
            console.error('分类管理初始化失败:', error);
        }
    }
});

// 导出模块
window.CategoryManager = CategoryManager;
