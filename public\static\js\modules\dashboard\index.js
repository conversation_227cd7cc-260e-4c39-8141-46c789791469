/**
 * 控制台页面主控制器
 * 管理控制台的图表、统计数据和交互功能
 */

const Dashboard = {
    // 图表实例
    charts: {
        usageTrend: null,
        category: null
    },
    
    /**
     * 初始化控制台功能
     */
    init() {
        console.log('初始化控制台功能...');
        
        // 初始化图表
        this.initCharts();
        
        // 绑定事件
        this.bindEvents();
        
        console.log('控制台功能初始化完成');
    },
    
    /**
     * 初始化图表
     */
    initCharts() {
        try {
            this.initUsageTrendChart();
            this.initCategoryChart();
        } catch (error) {
            console.error('图表初始化失败:', error);
        }
    },
    
    /**
     * 初始化使用趋势图表
     */
    initUsageTrendChart() {
        const ctx = document.getElementById('usageTrendChart');
        if (!ctx) {
            console.warn('使用趋势图表容器未找到');
            return;
        }
        
        // 从后端获取真实数据
        const backendData = window.dashboardData?.usageTrend || {};
        
        // 处理数据格式
        let labels = [];
        let createdData = [];
        let usedData = [];
        
        if (backendData && backendData.labels && backendData.labels.length > 0) {
            labels = backendData.labels;
            createdData = backendData.created || [];
            usedData = backendData.used || [];
        } else {
            // 如果没有后端数据，使用基于真实数据的测试数据
            labels = ['07-25', '07-26', '07-27', '07-28', '07-29', '07-30', '07-31', '08-01'];
            createdData = [0, 0, 0, 0, 0, 0, 15, 55];
            usedData = [0, 0, 0, 0, 0, 0, 0, 0];
        }
        
        const trendData = {
            labels: labels,
            datasets: [{
                label: '生成卡密',
                data: createdData,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }, {
                label: '使用卡密',
                data: usedData,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2,
                fill: false,
                tension: 0.4
            }]
        };
        
        try {
            this.charts.usageTrend = new Chart(ctx.getContext('2d'), {
                type: 'line',
                data: trendData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '数量'
                            },
                            beginAtZero: true
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    }
                }
            });
        } catch (error) {
            console.error('使用趋势图表创建失败:', error);
        }
    },
    
    /**
     * 初始化分类占比图表
     */
    initCategoryChart() {
        const ctx = document.getElementById('categoryChart');
        if (!ctx) {
            console.warn('分类占比图表容器未找到');
            return;
        }
        
        // 从后端数据构建图表数据
        const backendData = window.dashboardData?.categoryStats || {};
        
        let categoryData = {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        };
        
        // 处理数据格式
        if (backendData && backendData.labels && backendData.labels.length > 0) {
            categoryData.labels = backendData.labels;
            categoryData.datasets[0].data = backendData.data;
            categoryData.datasets[0].backgroundColor = backendData.colors;
        } else {
            // 如果没有后端数据，使用基于真实数据的测试数据
            categoryData.labels = ['基础会员', '会员卡密', 'Office套件', '装备道具', '教育资源'];
            categoryData.datasets[0].data = [5, 5, 5, 5, 5];
            categoryData.datasets[0].backgroundColor = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'];
        }
        
        try {
            this.charts.category = new Chart(ctx.getContext('2d'), {
                type: 'doughnut',
                data: categoryData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    },
                    cutout: '60%'
                }
            });
        } catch (error) {
            console.error('分类占比图表创建失败:', error);
        }
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 时间维度切换
        document.querySelectorAll('[data-period]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handlePeriodChange(e.target);
            });
        });
        
        // 导出按钮事件
        const exportBtn = document.querySelector('[onclick="showExportModal()"]');
        if (exportBtn) {
            exportBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.showExportModal();
            });
        }
    },
    
    /**
     * 处理时间维度切换
     * @param {HTMLElement} button 点击的按钮
     */
    handlePeriodChange(button) {
        // 更新按钮状态
        document.querySelectorAll('[data-period]').forEach(b => {
            b.classList.remove('active', 'modern-btn-primary');
            b.classList.add('modern-btn-outline');
        });
        
        button.classList.remove('modern-btn-outline');
        button.classList.add('active', 'modern-btn-primary');
        
        // 更新图表数据
        const period = button.dataset.period;
        this.updateUsageTrendChart(period);
    },
    
    /**
     * 更新使用趋势图表
     * @param {string} period 时间维度
     */
    updateUsageTrendChart(period) {
        if (!this.charts.usageTrend) return;
        
        // 发送AJAX请求获取新数据
        API.dashboard.charts({ type: 'usage_trend', period: period })
            .then(data => {
                if (data.code === 200) {
                    const newData = data.data;
                    this.charts.usageTrend.data.labels = newData.labels || [];
                    this.charts.usageTrend.data.datasets[0].data = newData.created || [];
                    this.charts.usageTrend.data.datasets[1].data = newData.used || [];
                    this.charts.usageTrend.update();
                } else {
                    Toast.error(data.message || '获取图表数据失败');
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                Toast.error('获取图表数据失败');
            });
    },
    
    /**
     * 显示导出模态框
     */
    showExportModal() {
        Modal.show('exportModal');
    },
    
    /**
     * 导出数据
     */
    exportData() {
        const password = document.getElementById('exportPassword')?.value;
        const type = document.getElementById('exportType')?.value;
        
        if (!password) {
            Toast.warning('请输入导出密码');
            return;
        }
        
        // 发送导出请求
        Toast.info(`正在导出${type === 'all' ? '全部' : type === 'cards' ? '卡密' : '使用记录'}数据...`);
        
        // 这里应该调用实际的导出API
        // API.dashboard.export({ password, type })
        
        // 关闭模态框
        Modal.hide('exportModal');
        
        // 清空表单
        const form = document.getElementById('exportForm');
        if (form) {
            form.reset();
        }
    },
    
    /**
     * 刷新数据
     */
    refreshData() {
        Utils.reload();
    }
};

// 全局函数，供HTML调用
window.showExportModal = function() {
    Dashboard.showExportModal();
};

window.exportData = function() {
    Dashboard.exportData();
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保Chart.js已加载
    if (typeof Chart !== 'undefined') {
        Dashboard.init();
    } else {
        console.error('控制台功能初始化失败：Chart.js未加载');
    }
});

// 导出模块
window.Dashboard = Dashboard;
