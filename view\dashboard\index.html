{extend name="layout/base" /}

{block name="title"}控制台 - 卡密兑换管理系统{/block}

{block name="style"}
<!-- 使用统一的CSS架构，移除内联样式 -->
<link rel="stylesheet" href="/static/css/main.css">
<link rel="stylesheet" href="/static/css/pages/dashboard.css">




{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">控制台</h1>
        <p class="text-muted mb-0">欢迎回来，这里是您的数据概览</p>
    </div>
    <div class="d-flex gap-2">
        <button class="modern-btn modern-btn-outline" onclick="location.reload()">
            <i class="fas fa-refresh"></i>
            刷新数据
        </button>
        <a href="/cards/generate" class="modern-btn modern-btn-primary">
            <i class="fas fa-plus"></i>
            生成卡密
        </a>
    </div>
</div>

<!-- 数据概览 -->
<div class="modern-stats-grid">
    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);">
            <i class="{$cardStats.total_cards.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.total_cards.raw_value}">{$cardStats.total_cards.value}</div>
        <div class="modern-stats-label">总卡密数</div>
        <div class="modern-stats-trend {$cardStats.total_cards.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.total_cards.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.total_cards.growth}%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--success-color) 0%, var(--success-light) 100%);">
            <i class="{$cardStats.used_cards.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.used_cards.raw_value}">{$cardStats.used_cards.value}</div>
        <div class="modern-stats-label">已使用</div>
        <div class="modern-stats-trend {$cardStats.used_cards.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.used_cards.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.used_cards.growth}%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-light) 100%);">
            <i class="{$cardStats.unused_cards.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.unused_cards.raw_value}">{$cardStats.unused_cards.value}</div>
        <div class="modern-stats-label">未使用</div>
        <div class="modern-stats-trend {$cardStats.unused_cards.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.unused_cards.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.unused_cards.growth}%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--error-color) 0%, var(--error-light) 100%);">
            <i class="{$cardStats.disabled_cards.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.disabled_cards.raw_value}">{$cardStats.disabled_cards.value}</div>
        <div class="modern-stats-label">已禁用</div>
        <div class="modern-stats-trend {$cardStats.disabled_cards.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.disabled_cards.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.disabled_cards.growth}%
        </div>
    </div>

    <div class="modern-stats-card">
        <div class="modern-stats-icon" style="background: linear-gradient(135deg, var(--purple-color) 0%, var(--purple-light) 100%);">
            <i class="{$cardStats.category_count.icon}"></i>
        </div>
        <div class="modern-stats-value" title="精确数值: {$cardStats.category_count.raw_value}">{$cardStats.category_count.value}</div>
        <div class="modern-stats-label">分类总数</div>
        <div class="modern-stats-trend {$cardStats.category_count.growth >= 0 ? 'positive' : 'negative'}">
            <i class="fas fa-arrow-{$cardStats.category_count.growth >= 0 ? 'up' : 'down'}"></i>
            {$cardStats.category_count.growth}%
        </div>
    </div>
</div>

<!-- 快速操作中心 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-bolt me-2"></i>
            快速操作
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="row g-3">
            <div class="col-md-2">
                <a href="/cards/generate" class="modern-btn modern-btn-primary w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-plus-circle fa-2x mb-2"></i>
                    <span>生成卡密</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/cards" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-credit-card fa-2x mb-2"></i>
                    <span>卡密管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/categories" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-tags fa-2x mb-2"></i>
                    <span>分类管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/content" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-file-alt fa-2x mb-2"></i>
                    <span>内容管理</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="#" class="modern-btn modern-btn-success w-100 d-flex flex-column align-items-center py-3" onclick="showExportModal()">
                    <i class="fas fa-download fa-2x mb-2"></i>
                    <span>导出数据</span>
                </a>
            </div>
            <div class="col-md-2">
                <a href="/settings" class="modern-btn modern-btn-outline w-100 d-flex flex-column align-items-center py-3">
                    <i class="fas fa-cog fa-2x mb-2"></i>
                    <span>系统设置</span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row g-4 mb-4">
    <!-- 使用趋势图表 -->
    <div class="col-lg-8">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-chart-line me-2"></i>
                    卡密使用趋势
                </h5>
                <div class="d-flex gap-1">
                    <button class="modern-btn modern-btn-primary btn-sm active" data-period="day">日</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="week">周</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="month">月</button>
                    <button class="modern-btn modern-btn-outline btn-sm" data-period="year">年</button>
                </div>
            </div>
            <div class="modern-card-body">
                <div style="height: 300px;">
                    <canvas id="usageTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 分类占比图表 -->
    <div class="col-lg-4">
        <div class="modern-card">
            <div class="modern-card-header">
                <h5 class="modern-card-title">
                    <i class="fas fa-chart-pie me-2"></i>
                    分类占比
                </h5>
            </div>
            <div class="modern-card-body">
                <div style="height: 300px;">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 最近使用记录 -->
<div class="modern-card mb-4">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-history me-2"></i>
            最近使用记录
        </h5>
        <a href="/usage-records" class="modern-btn modern-btn-outline btn-sm">查看全部</a>
    </div>
    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
                <thead>
                    <tr>
                        <th>卡密编号</th>
                        <th>分类</th>
                        <th>使用时间</th>
                        <th>使用IP</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    {volist name="recentRecords" id="record"}
                    <tr>
                        <td><code class="text-primary">{$record.masked_card_code}</code></td>
                        <td><span class="modern-badge modern-badge-primary">{$record.category_name}</span></td>
                        <td class="text-muted">{$record.used_at}</td>
                        <td class="text-muted">{$record.used_ip|default='未知'}</td>
                        <td>
                            <span class="modern-badge modern-badge-success">
                                <i class="fas fa-check-circle"></i>
                                已使用
                            </span>
                        </td>
                    </tr>
                    {/volist}
                    {empty name="recentRecords"}
                    <tr>
                        <td colspan="5" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                                <div>暂无使用记录</div>
                            </div>
                        </td>
                    </tr>
                    {/empty}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 系统状态监控 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-server me-2"></i>
            系统状态
        </h5>
    </div>
    <div class="modern-card-body">
        <div class="row g-4">
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-{$systemStatus.server_status.color} me-3"></div>
                    <div>
                        <div class="fw-bold">服务器状态</div>
                        <small class="text-muted">{$systemStatus.server_status.text}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-{$systemStatus.database_status.color} me-3"></div>
                    <div>
                        <div class="fw-bold">数据库连接</div>
                        <small class="text-muted">{$systemStatus.database_status.text}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center">
                    <div class="status-indicator status-{$systemStatus.storage_status.color} me-3"></div>
                    <div>
                        <div class="fw-bold">存储空间</div>
                        <small class="text-muted">{$systemStatus.storage_status.text}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导出数据模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出数据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportType" class="form-label">导出类型</label>
                        <select class="form-select" id="exportType">
                            <option value="all">全部数据</option>
                            <option value="cards">卡密数据</option>
                            <option value="usage">使用记录</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportData()">导出</button>
            </div>
        </div>
    </div>
</div>
{/block}

{block name="script"}
<!-- 引入JavaScript模块 -->
<script src="/static/js/common.js"></script>
<script src="/static/js/api.js"></script>
<script src="/static/js/components/modal.js"></script>
<script src="/static/js/components/toast.js"></script>
<script src="/static/js/modules/dashboard/index.js"></script>
<script>
// 页面初始化数据
window.dashboardData = {
    usageTrend: {$usageTrend|json_encode|raw},
    categoryStats: {$categoryStats|json_encode|raw}
};






</script>
{/block}
