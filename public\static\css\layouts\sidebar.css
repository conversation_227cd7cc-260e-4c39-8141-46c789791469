/**
 * 侧边栏布局样式
 * 统一的侧边栏导航和布局样式
 */

/* 侧边栏容器 */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: var(--sidebar-width);
  height: 100vh;
  background: var(--sidebar-bg);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow-y: auto;
  overflow-x: hidden;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.collapsed {
  transform: translateX(-100%);
}

.sidebar.show {
  transform: translateX(0);
}

/* 侧边栏头部 */
.sidebar-header {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
}

.sidebar-brand {
  color: var(--sidebar-text);
  text-decoration: none;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  transition: all var(--transition-fast);
}

.sidebar-brand:hover {
  color: var(--sidebar-text);
  text-decoration: none;
  opacity: 0.8;
}

.sidebar-brand i {
  margin-right: 8px;
  font-size: 20px;
  color: var(--primary-color);
}

.sidebar-brand-icon {
  width: 32px;
  height: 32px;
  margin-right: var(--spacing-3);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  font-size: var(--font-size-lg);
}

.sidebar-brand-text {
  transition: opacity var(--transition-normal);
}

.sidebar.collapsed .sidebar-brand-text {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* 侧边栏导航 */
.sidebar-nav {
  padding: 16px 0;
}

.nav-section {
  margin-bottom: 24px;
}

.nav-section-title {
  color: var(--sidebar-text-secondary);
  font-size: 12px;
  text-transform: uppercase;
  font-weight: 600;
  padding: 0 24px;
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

.sidebar.collapsed .sidebar-nav-title {
  opacity: 0;
  height: 0;
  margin: 0;
  overflow: hidden;
}

.sidebar-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.sidebar-nav-item {
  margin-bottom: var(--spacing-1);
}

/* 导航链接 */
.nav-link {
  color: var(--sidebar-text-secondary);
  padding: 12px 24px;
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  position: relative;
  border-radius: 0 20px 20px 0;
  margin-right: 8px;
}

.nav-link:hover {
  color: var(--sidebar-text);
  background-color: var(--sidebar-hover);
  text-decoration: none;
}

.nav-link.active {
  color: var(--sidebar-text);
  background-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.nav-link i {
  width: 16px;
  margin-right: 12px;
  text-align: center;
  font-size: 16px;
}

.sidebar-nav-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-base);
  flex-shrink: 0;
}

.sidebar-nav-text {
  flex: 1;
  transition: opacity var(--transition-normal);
  white-space: nowrap;
  overflow: hidden;
}

.sidebar.collapsed .sidebar-nav-text {
  opacity: 0;
  width: 0;
}

.sidebar-nav-badge {
  margin-left: auto;
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--danger-color);
  color: var(--white);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  min-width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity var(--transition-normal);
}

.sidebar.collapsed .sidebar-nav-badge {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* 子菜单 */
.sidebar-nav-submenu {
  list-style: none;
  margin: 0;
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height var(--transition-normal);
}

.sidebar-nav-item.expanded .sidebar-nav-submenu {
  max-height: 500px;
}

.sidebar-nav-submenu .sidebar-nav-link {
  padding-left: var(--spacing-12);
  font-size: var(--font-size-sm);
}

.sidebar-nav-submenu .sidebar-nav-icon {
  width: 16px;
  height: 16px;
  font-size: var(--font-size-sm);
}

.sidebar-nav-toggle {
  margin-left: auto;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--transition-fast);
}

.sidebar-nav-item.expanded .sidebar-nav-toggle {
  transform: rotate(90deg);
}

.sidebar.collapsed .sidebar-nav-toggle {
  opacity: 0;
  width: 0;
}

/* 侧边栏底部 */
.sidebar-footer {
  position: sticky;
  bottom: 0;
  padding: var(--spacing-4);
  border-top: var(--border-width) solid var(--border-color);
  background: var(--white);
}

.sidebar-user {
  display: flex;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--gray-100);
  border-radius: var(--border-radius-lg);
  transition: all var(--transition-fast);
}

.sidebar-user:hover {
  background: var(--gray-200);
}

.sidebar-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius-full);
  margin-right: var(--spacing-3);
  object-fit: cover;
  flex-shrink: 0;
}

.sidebar-user-info {
  flex: 1;
  min-width: 0;
  transition: opacity var(--transition-normal);
}

.sidebar.collapsed .sidebar-user-info {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

.sidebar-user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-800);
  margin-bottom: var(--spacing-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-user-role {
  font-size: var(--font-size-xs);
  color: var(--gray-600);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 折叠按钮 */
.sidebar-toggle {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  z-index: 10;
}

.sidebar-toggle:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.sidebar-toggle-icon {
  font-size: var(--font-size-sm);
  transition: transform var(--transition-fast);
}

.sidebar.collapsed .sidebar-toggle-icon {
  transform: rotate(180deg);
}

/* 工具提示（折叠状态下显示） */
.sidebar-tooltip {
  position: absolute;
  left: calc(100% + var(--spacing-2));
  top: 50%;
  transform: translateY(-50%);
  background: var(--gray-800);
  color: var(--white);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-fast);
  z-index: var(--z-tooltip);
}

.sidebar-tooltip::before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 4px solid transparent;
  border-right-color: var(--gray-800);
}

.sidebar.collapsed .sidebar-nav-link:hover .sidebar-tooltip {
  opacity: 1;
  visibility: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    z-index: var(--z-modal);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .sidebar-toggle {
    display: none;
  }
}

/* 滚动条样式 */
.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}
