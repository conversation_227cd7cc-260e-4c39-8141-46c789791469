/**
 * 颜色工具类
 * 提供统一的颜色工具类
 */

/* 文本颜色 */
.text-primary { color: var(--primary-color) !important; }
.text-primary-hover:hover { color: var(--primary-hover) !important; }
.text-primary-light { color: var(--primary-light) !important; }
.text-primary-dark { color: var(--primary-dark) !important; }

.text-secondary { color: var(--secondary-color) !important; }
.text-secondary-hover:hover { color: var(--secondary-hover) !important; }
.text-secondary-light { color: var(--secondary-light) !important; }

.text-success { color: var(--success-color) !important; }
.text-success-hover:hover { color: var(--success-hover) !important; }
.text-success-light { color: var(--success-light) !important; }

.text-warning { color: var(--warning-color) !important; }
.text-warning-hover:hover { color: var(--warning-hover) !important; }
.text-warning-light { color: var(--warning-light) !important; }

.text-danger { color: var(--danger-color) !important; }
.text-danger-hover:hover { color: var(--danger-hover) !important; }
.text-danger-light { color: var(--danger-light) !important; }

.text-info { color: var(--info-color) !important; }
.text-info-hover:hover { color: var(--info-hover) !important; }
.text-info-light { color: var(--info-light) !important; }

/* 中性色文本 */
.text-white { color: var(--white) !important; }
.text-black { color: var(--black) !important; }

.text-gray-100 { color: var(--gray-100) !important; }
.text-gray-200 { color: var(--gray-200) !important; }
.text-gray-300 { color: var(--gray-300) !important; }
.text-gray-400 { color: var(--gray-400) !important; }
.text-gray-500 { color: var(--gray-500) !important; }
.text-gray-600 { color: var(--gray-600) !important; }
.text-gray-700 { color: var(--gray-700) !important; }
.text-gray-800 { color: var(--gray-800) !important; }
.text-gray-900 { color: var(--gray-900) !important; }

/* 语义化文本颜色 */
.text-muted { color: var(--gray-500) !important; }
.text-light { color: var(--gray-400) !important; }
.text-dark { color: var(--gray-800) !important; }

/* 背景颜色 */
.bg-primary { background-color: var(--primary-color) !important; }
.bg-primary-hover:hover { background-color: var(--primary-hover) !important; }
.bg-primary-light { background-color: var(--primary-light) !important; }
.bg-primary-dark { background-color: var(--primary-dark) !important; }

.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-secondary-hover:hover { background-color: var(--secondary-hover) !important; }
.bg-secondary-light { background-color: var(--secondary-light) !important; }

.bg-success { background-color: var(--success-color) !important; }
.bg-success-hover:hover { background-color: var(--success-hover) !important; }
.bg-success-light { background-color: var(--success-light) !important; }

.bg-warning { background-color: var(--warning-color) !important; }
.bg-warning-hover:hover { background-color: var(--warning-hover) !important; }
.bg-warning-light { background-color: var(--warning-light) !important; }

.bg-danger { background-color: var(--danger-color) !important; }
.bg-danger-hover:hover { background-color: var(--danger-hover) !important; }
.bg-danger-light { background-color: var(--danger-light) !important; }

.bg-info { background-color: var(--info-color) !important; }
.bg-info-hover:hover { background-color: var(--info-hover) !important; }
.bg-info-light { background-color: var(--info-light) !important; }

/* 中性色背景 */
.bg-white { background-color: var(--white) !important; }
.bg-black { background-color: var(--black) !important; }

.bg-gray-100 { background-color: var(--gray-100) !important; }
.bg-gray-200 { background-color: var(--gray-200) !important; }
.bg-gray-300 { background-color: var(--gray-300) !important; }
.bg-gray-400 { background-color: var(--gray-400) !important; }
.bg-gray-500 { background-color: var(--gray-500) !important; }
.bg-gray-600 { background-color: var(--gray-600) !important; }
.bg-gray-700 { background-color: var(--gray-700) !important; }
.bg-gray-800 { background-color: var(--gray-800) !important; }
.bg-gray-900 { background-color: var(--gray-900) !important; }

/* 语义化背景颜色 */
.bg-light { background-color: var(--gray-100) !important; }
.bg-dark { background-color: var(--gray-800) !important; }
.bg-transparent { background-color: transparent !important; }

/* 边框颜色 */
.border-primary { border-color: var(--primary-color) !important; }
.border-secondary { border-color: var(--secondary-color) !important; }
.border-success { border-color: var(--success-color) !important; }
.border-warning { border-color: var(--warning-color) !important; }
.border-danger { border-color: var(--danger-color) !important; }
.border-info { border-color: var(--info-color) !important; }

.border-white { border-color: var(--white) !important; }
.border-black { border-color: var(--black) !important; }

.border-gray-100 { border-color: var(--gray-100) !important; }
.border-gray-200 { border-color: var(--gray-200) !important; }
.border-gray-300 { border-color: var(--gray-300) !important; }
.border-gray-400 { border-color: var(--gray-400) !important; }
.border-gray-500 { border-color: var(--gray-500) !important; }
.border-gray-600 { border-color: var(--gray-600) !important; }
.border-gray-700 { border-color: var(--gray-700) !important; }
.border-gray-800 { border-color: var(--gray-800) !important; }
.border-gray-900 { border-color: var(--gray-900) !important; }

.border-light { border-color: var(--gray-300) !important; }
.border-dark { border-color: var(--gray-700) !important; }
.border-transparent { border-color: transparent !important; }

/* 渐变背景 */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-hover) 100%) !important;
}

.bg-gradient-success {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-hover) 100%) !important;
}

.bg-gradient-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-hover) 100%) !important;
}

.bg-gradient-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-hover) 100%) !important;
}

.bg-gradient-info {
  background: linear-gradient(135deg, var(--info-color) 0%, var(--info-hover) 100%) !important;
}

/* 特殊渐变 */
.bg-gradient-light {
  background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%) !important;
}

.bg-gradient-dark {
  background: linear-gradient(135deg, var(--gray-700) 0%, var(--gray-900) 100%) !important;
}

.bg-gradient-rainbow {
  background: linear-gradient(135deg, 
    #ff6b6b 0%, 
    #4ecdc4 25%, 
    #45b7d1 50%, 
    #96ceb4 75%, 
    #ffeaa7 100%) !important;
}

/* 透明度 */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* 悬停透明度 */
.hover-opacity-0:hover { opacity: 0 !important; }
.hover-opacity-25:hover { opacity: 0.25 !important; }
.hover-opacity-50:hover { opacity: 0.5 !important; }
.hover-opacity-75:hover { opacity: 0.75 !important; }
.hover-opacity-100:hover { opacity: 1 !important; }

/* 填充和描边（用于SVG图标） */
.fill-current { fill: currentColor !important; }
.stroke-current { stroke: currentColor !important; }

.fill-primary { fill: var(--primary-color) !important; }
.fill-secondary { fill: var(--secondary-color) !important; }
.fill-success { fill: var(--success-color) !important; }
.fill-warning { fill: var(--warning-color) !important; }
.fill-danger { fill: var(--danger-color) !important; }
.fill-info { fill: var(--info-color) !important; }

.stroke-primary { stroke: var(--primary-color) !important; }
.stroke-secondary { stroke: var(--secondary-color) !important; }
.stroke-success { stroke: var(--success-color) !important; }
.stroke-warning { stroke: var(--warning-color) !important; }
.stroke-danger { stroke: var(--danger-color) !important; }
.stroke-info { stroke: var(--info-color) !important; }

/* 颜色过渡 */
.transition-colors {
  transition: color var(--transition-fast), 
              background-color var(--transition-fast), 
              border-color var(--transition-fast) !important;
}

.transition-colors-slow {
  transition: color var(--transition-slow), 
              background-color var(--transition-slow), 
              border-color var(--transition-slow) !important;
}

/* 状态颜色组合 */
.text-on-primary { color: var(--white) !important; }
.text-on-secondary { color: var(--white) !important; }
.text-on-success { color: var(--white) !important; }
.text-on-warning { color: var(--gray-800) !important; }
.text-on-danger { color: var(--white) !important; }
.text-on-info { color: var(--white) !important; }
.text-on-light { color: var(--gray-800) !important; }
.text-on-dark { color: var(--white) !important; }

/* 链接颜色 */
.link-primary { color: var(--primary-color) !important; }
.link-primary:hover { color: var(--primary-hover) !important; }

.link-secondary { color: var(--secondary-color) !important; }
.link-secondary:hover { color: var(--secondary-hover) !important; }

.link-success { color: var(--success-color) !important; }
.link-success:hover { color: var(--success-hover) !important; }

.link-warning { color: var(--warning-color) !important; }
.link-warning:hover { color: var(--warning-hover) !important; }

.link-danger { color: var(--danger-color) !important; }
.link-danger:hover { color: var(--danger-hover) !important; }

.link-info { color: var(--info-color) !important; }
.link-info:hover { color: var(--info-hover) !important; }

.link-muted { color: var(--gray-500) !important; }
.link-muted:hover { color: var(--gray-700) !important; }

.link-dark { color: var(--gray-800) !important; }
.link-dark:hover { color: var(--gray-900) !important; }
