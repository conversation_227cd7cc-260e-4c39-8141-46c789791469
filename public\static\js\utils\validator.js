/**
 * 表单验证工具
 * 提供各种常用的验证规则和方法
 */

const Validator = {
    // 常用正则表达式
    patterns: {
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        phone: /^1[3-9]\d{9}$/,
        idCard: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        url: /^https?:\/\/.+/,
        number: /^\d+$/,
        decimal: /^\d+(\.\d+)?$/,
        chinese: /^[\u4e00-\u9fa5]+$/,
        username: /^[a-zA-Z0-9_]{3,20}$/,
        password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
    },
    
    /**
     * 验证是否为空
     * @param {any} value 值
     * @returns {boolean}
     */
    isEmpty(value) {
        return value === null || value === undefined || value === '' || 
               (Array.isArray(value) && value.length === 0) ||
               (typeof value === 'object' && Object.keys(value).length === 0);
    },
    
    /**
     * 验证是否必填
     * @param {any} value 值
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    required(value, message = '此字段为必填项') {
        return !this.isEmpty(value) || message;
    },
    
    /**
     * 验证最小长度
     * @param {string} value 值
     * @param {number} min 最小长度
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    minLength(value, min, message) {
        if (this.isEmpty(value)) return true;
        return value.length >= min || (message || `长度不能少于${min}个字符`);
    },
    
    /**
     * 验证最大长度
     * @param {string} value 值
     * @param {number} max 最大长度
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    maxLength(value, max, message) {
        if (this.isEmpty(value)) return true;
        return value.length <= max || (message || `长度不能超过${max}个字符`);
    },
    
    /**
     * 验证邮箱格式
     * @param {string} value 值
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    email(value, message = '邮箱格式不正确') {
        if (this.isEmpty(value)) return true;
        return this.patterns.email.test(value) || message;
    },
    
    /**
     * 验证手机号格式
     * @param {string} value 值
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    phone(value, message = '手机号格式不正确') {
        if (this.isEmpty(value)) return true;
        return this.patterns.phone.test(value) || message;
    },
    
    /**
     * 验证URL格式
     * @param {string} value 值
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    url(value, message = 'URL格式不正确') {
        if (this.isEmpty(value)) return true;
        return this.patterns.url.test(value) || message;
    },
    
    /**
     * 验证数字
     * @param {string} value 值
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    number(value, message = '必须是数字') {
        if (this.isEmpty(value)) return true;
        return this.patterns.number.test(value) || message;
    },
    
    /**
     * 验证小数
     * @param {string} value 值
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    decimal(value, message = '必须是数字') {
        if (this.isEmpty(value)) return true;
        return this.patterns.decimal.test(value) || message;
    },
    
    /**
     * 验证范围
     * @param {number} value 值
     * @param {number} min 最小值
     * @param {number} max 最大值
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    range(value, min, max, message) {
        if (this.isEmpty(value)) return true;
        const num = parseFloat(value);
        return (num >= min && num <= max) || (message || `值必须在${min}到${max}之间`);
    },
    
    /**
     * 验证正则表达式
     * @param {string} value 值
     * @param {RegExp} pattern 正则表达式
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    pattern(value, pattern, message = '格式不正确') {
        if (this.isEmpty(value)) return true;
        return pattern.test(value) || message;
    },
    
    /**
     * 验证两个字段是否相等
     * @param {string} value 值
     * @param {string} compareValue 比较值
     * @param {string} message 错误消息
     * @returns {boolean|string}
     */
    equals(value, compareValue, message = '两次输入不一致') {
        return value === compareValue || message;
    },
    
    /**
     * 批量验证
     * @param {object} data 数据对象
     * @param {object} rules 验证规则
     * @returns {object}
     */
    validate(data, rules) {
        const errors = {};
        
        Object.keys(rules).forEach(field => {
            const fieldRules = rules[field];
            const value = data[field];
            
            // 如果是数组，表示多个验证规则
            if (Array.isArray(fieldRules)) {
                for (let rule of fieldRules) {
                    const result = this.validateField(value, rule, data);
                    if (result !== true) {
                        errors[field] = result;
                        break;
                    }
                }
            } else {
                const result = this.validateField(value, fieldRules, data);
                if (result !== true) {
                    errors[field] = result;
                }
            }
        });
        
        return {
            valid: Object.keys(errors).length === 0,
            errors: errors
        };
    },
    
    /**
     * 验证单个字段
     * @param {any} value 值
     * @param {object} rule 验证规则
     * @param {object} data 完整数据对象
     * @returns {boolean|string}
     */
    validateField(value, rule, data = {}) {
        // 自定义验证函数
        if (rule.validator && typeof rule.validator === 'function') {
            return rule.validator(value, data);
        }
        
        // 必填验证
        if (rule.required) {
            const result = this.required(value, rule.message);
            if (result !== true) return result;
        }
        
        // 如果值为空且不是必填，跳过其他验证
        if (this.isEmpty(value) && !rule.required) return true;
        
        // 长度验证
        if (rule.minLength !== undefined) {
            const result = this.minLength(value, rule.minLength, rule.message);
            if (result !== true) return result;
        }
        
        if (rule.maxLength !== undefined) {
            const result = this.maxLength(value, rule.maxLength, rule.message);
            if (result !== true) return result;
        }
        
        // 类型验证
        if (rule.type) {
            switch (rule.type) {
                case 'email':
                    return this.email(value, rule.message);
                case 'phone':
                    return this.phone(value, rule.message);
                case 'url':
                    return this.url(value, rule.message);
                case 'number':
                    return this.number(value, rule.message);
                case 'decimal':
                    return this.decimal(value, rule.message);
            }
        }
        
        // 正则验证
        if (rule.pattern) {
            return this.pattern(value, rule.pattern, rule.message);
        }
        
        // 范围验证
        if (rule.min !== undefined && rule.max !== undefined) {
            return this.range(value, rule.min, rule.max, rule.message);
        }
        
        return true;
    }
};

// 导出到全局
window.Validator = Validator;
