# 卡密兑换管理系统

基于 ThinkPHP 8.0 开发的卡密兑换管理系统，支持前后端分离架构。

## 功能特性

- **管理后台**: 数据统计、卡密管理、分类管理
- **用户端**: 卡密兑换、记录查询
- **API接口**: RESTful风格的API接口
- **响应式设计**: 支持桌面端和移动端

## 环境要求

- PHP 8.0+
- MySQL 5.7+
- Composer

## 快速开始

1. 安装依赖
```bash
composer install
```

2. 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件配置数据库连接
```

3. 初始化数据库
```bash
# 创建数据库并导入表结构
mysql -u root -p < database/migrations/create_card_system_tables.sql
```

4. 启动服务
```bash
php think run
```

## 访问地址

- **管理后台**: http://localhost:8000/dashboard
- **用户兑换**: http://localhost:8000/exchange.html

## 默认账户

- **管理员**: admin / admin
- **导出密码**: 123456

## 技术栈

- **后端**: ThinkPHP 8.0 + MySQL
- **前端**: Bootstrap 5 + Chart.js
- **API**: RESTful接口设计

## 许可证

Apache 2.0