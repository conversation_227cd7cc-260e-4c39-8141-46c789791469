/**
 * 数据格式化工具
 * 提供各种数据格式化功能
 */

const Formatter = {
    /**
     * 格式化日期时间
     * @param {string|Date} datetime 日期时间
     * @param {string} format 格式化模板
     * @returns {string}
     */
    dateTime(datetime, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!datetime) return '-';
        
        const date = typeof datetime === 'string' ? new Date(datetime) : datetime;
        if (isNaN(date.getTime())) return '-';
        
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 格式化日期
     * @param {string|Date} date 日期
     * @returns {string}
     */
    date(date) {
        return this.dateTime(date, 'YYYY-MM-DD');
    },
    
    /**
     * 格式化时间
     * @param {string|Date} time 时间
     * @returns {string}
     */
    time(time) {
        return this.dateTime(time, 'HH:mm:ss');
    },
    
    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @param {number} decimals 小数位数
     * @returns {string}
     */
    fileSize(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    },
    
    /**
     * 格式化数字
     * @param {number} number 数字
     * @param {number} decimals 小数位数
     * @returns {string}
     */
    number(number, decimals = 0) {
        if (isNaN(number)) return '-';
        return Number(number).toFixed(decimals);
    },
    
    /**
     * 格式化百分比
     * @param {number} number 数字
     * @param {number} decimals 小数位数
     * @returns {string}
     */
    percentage(number, decimals = 2) {
        if (isNaN(number)) return '-';
        return (Number(number) * 100).toFixed(decimals) + '%';
    },
    
    /**
     * 格式化货币
     * @param {number} amount 金额
     * @param {string} currency 货币符号
     * @param {number} decimals 小数位数
     * @returns {string}
     */
    currency(amount, currency = '¥', decimals = 2) {
        if (isNaN(amount)) return '-';
        return currency + Number(amount).toFixed(decimals);
    },
    
    /**
     * 格式化手机号
     * @param {string} phone 手机号
     * @returns {string}
     */
    phone(phone) {
        if (!phone) return '-';
        const phoneStr = String(phone);
        if (phoneStr.length === 11) {
            return phoneStr.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3');
        }
        return phoneStr;
    },
    
    /**
     * 格式化身份证号
     * @param {string} idCard 身份证号
     * @returns {string}
     */
    idCard(idCard) {
        if (!idCard) return '-';
        const idStr = String(idCard);
        if (idStr.length === 18) {
            return idStr.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
        }
        return idStr;
    },
    
    /**
     * 格式化银行卡号
     * @param {string} cardNumber 银行卡号
     * @returns {string}
     */
    bankCard(cardNumber) {
        if (!cardNumber) return '-';
        const cardStr = String(cardNumber);
        if (cardStr.length >= 16) {
            return cardStr.replace(/(\d{4})\d*(\d{4})/, '$1****$2');
        }
        return cardStr;
    },
    
    /**
     * 截断文本
     * @param {string} text 文本
     * @param {number} length 最大长度
     * @param {string} suffix 后缀
     * @returns {string}
     */
    truncate(text, length = 50, suffix = '...') {
        if (!text) return '-';
        const textStr = String(text);
        if (textStr.length <= length) return textStr;
        return textStr.substring(0, length) + suffix;
    },
    
    /**
     * 格式化状态
     * @param {number|string} status 状态值
     * @param {object} statusMap 状态映射
     * @returns {string}
     */
    status(status, statusMap = {}) {
        return statusMap[status] || '未知';
    },
    
    /**
     * 格式化布尔值
     * @param {boolean} value 布尔值
     * @param {string} trueText 真值文本
     * @param {string} falseText 假值文本
     * @returns {string}
     */
    boolean(value, trueText = '是', falseText = '否') {
        return value ? trueText : falseText;
    },
    
    /**
     * 格式化数组为字符串
     * @param {Array} array 数组
     * @param {string} separator 分隔符
     * @returns {string}
     */
    array(array, separator = ', ') {
        if (!Array.isArray(array)) return '-';
        return array.join(separator);
    },
    
    /**
     * 高亮关键词
     * @param {string} text 文本
     * @param {string} keyword 关键词
     * @param {string} className 高亮样式类名
     * @returns {string}
     */
    highlight(text, keyword, className = 'highlight') {
        if (!text || !keyword) return text;
        const regex = new RegExp(`(${keyword})`, 'gi');
        return text.replace(regex, `<span class="${className}">$1</span>`);
    },
    
    /**
     * 转义HTML
     * @param {string} html HTML字符串
     * @returns {string}
     */
    escapeHtml(html) {
        if (!html) return '';
        const div = document.createElement('div');
        div.textContent = html;
        return div.innerHTML;
    },
    
    /**
     * 反转义HTML
     * @param {string} html HTML字符串
     * @returns {string}
     */
    unescapeHtml(html) {
        if (!html) return '';
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }
};

// 导出到全局
window.Formatter = Formatter;
