<?php /*a:2:{s:41:"F:\linshi\thphp\kmxt\view\card\index.html";i:1754112188;s:42:"F:\linshi\thphp\kmxt\view\layout\base.html";i:1754108817;}*/ ?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>卡密管理 - 卡密兑换管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- 主样式文件 -->
    <link rel="stylesheet" href="/static/css/main.css">

    <!-- 自定义样式 -->
    <style>
        /* 基础模板中只保留必要的CSS变量，其他变量由外部CSS文件提供 */

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--content-bg);
            color: var(--text-primary);
            font-size: 14px;
            line-height: 1.5715;
            overflow-x: hidden; /* 防止水平滚动条闪烁 */
        }
        
        /* 优化动画性能 */
        .sidebar,
        .main-content {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform-style: preserve-3d;
            -webkit-transform-style: preserve-3d;
        }

        /* 防止初始化时的闪烁 */
        .sidebar-loading .sidebar,
        .sidebar-loading .main-content {
            transition: none !important;
        }

        /* 防止导航激活状态闪烁 */
        .nav-loading .nav-link {
            transition: none !important;
        }














        
        /* 顶部导航栏 */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 999;
        }

        .navbar-left {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        /* 通知按钮 */
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            padding: 0.5rem;
            border-radius: 50%;
            color: #6c757d;
            transition: all 0.2s ease;
        }

        .notification-btn:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: #dc3545;
            color: white;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 10px;
            min-width: 16px;
            text-align: center;
        }

        /* 用户下拉菜单 */
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            color: #495057;
        }

        .user-dropdown .dropdown-toggle:hover {
            background-color: #f8f9fa;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), #40a9ff);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .user-dropdown .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            border-radius: 8px;
            padding: 0.5rem 0;
            min-width: 200px;
        }

        .user-dropdown .dropdown-item {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            color: #495057;
            transition: all 0.2s ease;
        }

        .user-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
            color: var(--primary-color);
        }

        .user-dropdown .dropdown-item i {
            width: 16px;
            text-align: center;
        }
        
        .sidebar-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: #6c757d;
            margin-right: 1rem;
        }
        
        .notification-btn {
            position: relative;
            background: none;
            border: none;
            font-size: 1.1rem;
            color: #6c757d;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-dropdown .dropdown-toggle {
            background: none;
            border: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.9rem;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
        }
        
        /* 内容区域样式 */
        .content-wrapper {
            padding: 2rem;
            max-width: 100%;
            margin: 0;
            width: 100%;
        }
        
        .page-title {
            margin-bottom: 24px;
            background: var(--card-bg);
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border: 1px solid var(--border-color);
        }

        .page-title h1 {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0 0 8px 0;
            line-height: 1.2;
        }

        .page-title .breadcrumb {
            background: none;
            padding: 0;
            margin: 0;
            font-size: 14px;
        }

        .breadcrumb-item a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
        }

        .breadcrumb-item.active {
            color: var(--text-primary);
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
            padding: 8px 16px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: #40a9ff;
            border-color: #40a9ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-success:hover {
            background-color: #73d13d;
            border-color: #73d13d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-warning:hover {
            background-color: #ffc53d;
            border-color: #ffc53d;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(250, 173, 20, 0.3);
        }

        .btn-danger {
            background-color: var(--error-color);
            border-color: var(--error-color);
        }

        .btn-danger:hover {
            background-color: #ff7875;
            border-color: #ff7875;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
        }

        .btn-outline-secondary {
            color: var(--text-secondary);
            border-color: var(--border-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--content-bg);
            border-color: var(--text-secondary);
            color: var(--text-primary);
        }
    </style>
    
    
<!-- 使用统一的CSS架构，移除内联样式 -->
<link rel="stylesheet" href="/static/css/main.css">
<link rel="stylesheet" href="/static/css/pages/cards.css">





</head>
<body>
    <!-- 侧边栏 -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="/dashboard" class="sidebar-brand">
                <i class="fas fa-shield-alt me-2"></i>
                卡密管理系统
            </a>
        </div>
        
        <div class="sidebar-nav">
            <div class="nav-section">
                <div class="nav-section-title">主要功能</div>
                <a href="/dashboard" class="nav-link">
                    <i class="fas fa-tachometer-alt"></i>
                    控制台
                </a>
                <a href="/cards" class="nav-link" id="nav-cards">
                    <i class="fas fa-credit-card"></i>
                    卡密管理
                </a>
                <a href="/categories" class="nav-link">
                    <i class="fas fa-tags"></i>
                    分类管理
                </a>
                <a href="/content" class="nav-link">
                    <i class="fas fa-file-alt"></i>
                    内容管理
                </a>
                <a href="/generate" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    生成卡密
                </a>
            </div>
            
            <div class="nav-section">
                <div class="nav-section-title">系统管理</div>
                <a href="/settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容区域 -->
    <div class="main-content" id="mainContent">
        <!-- 顶部导航栏 -->
        <div class="top-navbar">
            <div class="navbar-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <span class="fw-semibold">欢迎回来，管理员</span>
            </div>
            
            <div class="navbar-right">
                <button class="notification-btn" title="通知">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge" style="display: none;">0</span>
                </button>
                
                <div class="dropdown user-dropdown">
                    <button class="dropdown-toggle" data-bs-toggle="dropdown">
                        <div class="user-avatar"><?php echo htmlentities((string) strtoupper(substr((isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'A'),0,1))); ?></div>
                        <span><?php echo htmlentities((string) (isset($currentAdmin['name']) && ($currentAdmin['name'] !== '')?$currentAdmin['name']:'Admin')); ?></span>
                        <i class="fas fa-chevron-down ms-1"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/account"><i class="fas fa-cog me-2"></i>账户设置</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout"><i class="fas fa-sign-out-alt me-2"></i>退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="content-wrapper">
            
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-1 fw-bold text-dark">卡密管理</h1>
        <p class="text-muted mb-0">管理和监控所有卡密的状态</p>
    </div>

</div>

<!-- 筛选条件 -->
<div class="modern-card mb-3 compact-filter">
    <div class="modern-card-header">
        <h6 class="modern-card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            筛选条件
        </h6>
    </div>
    <div class="modern-card-body">
        <form method="get">
            <div class="row g-2 align-items-center">
                <div class="col-md-2">
                    <select name="status" class="modern-form-control">
                        <option value="">全部状态</option>
                        <option value="0" <?php echo $filters['status']=='0' ? 'selected'  :  ''; ?>>未使用</option>
                        <option value="1" <?php echo $filters['status']=='1' ? 'selected'  :  ''; ?>>已使用</option>
                        <option value="2" <?php echo $filters['status']=='2' ? 'selected'  :  ''; ?>>已禁用</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <div class="category-tree-selector">
                        <div class="tree-selector-input" onclick="toggleCategoryDropdown()">
                            <span class="selected-text" id="selectedCategoryText">
                                <?php if($filters['category_id']): 
                                        function findCategoryById($categories, $id) {
                                            foreach ($categories as $category) {
                                                if ($category['id'] == $id) {
                                                    return $category;
                                                }
                                                if (!empty($category['children'])) {
                                                    $found = findCategoryById($category['children'], $id);
                                                    if ($found) return $found;
                                                }
                                            }
                                            return null;
                                        }
                                        $selectedCategory = findCategoryById($categories, $filters['category_id']);
                                        echo $selectedCategory ? $selectedCategory['name'] : '全部分类';
                                     else: ?>
                                    全部分类
                                <?php endif; ?>
                            </span>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="tree-selector-dropdown" id="categoryTreeDropdown" style="display: none;">
                            <div class="tree-item" data-id="" onclick="selectCategory('', '全部分类')">
                                <div class="tree-item-content">
                                    <span class="expand-icon-placeholder"></span>
                                    <i class="fas fa-list text-primary"></i>
                                    <span>全部分类</span>
                                </div>
                            </div>
                            <?php if(is_array($categories) || $categories instanceof \think\Collection || $categories instanceof \think\Paginator): $i = 0; $__LIST__ = $categories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                                <!-- 一级分类 -->
                                <div class="tree-item level-1" data-id="<?php echo htmlentities((string) $category['id']); ?>">
                                    <div class="tree-item-content" onclick="selectCategory('<?php echo htmlentities((string) $category['id']); ?>', '<?php echo htmlentities((string) $category['name']); ?>')">
                                        <i class="fas fa-chevron-right expand-icon" onclick="event.stopPropagation(); toggleTreeNode(this)"></i>
                                        <div class="category-icon level-1-icon">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                        <span><?php echo htmlentities((string) $category['name']); ?></span>
                                    </div>
                                    <div class="tree-children" style="display: none;">
                                        <?php if(is_array($category['children']) || $category['children'] instanceof \think\Collection || $category['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $category['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$child): $mod = ($i % 2 );++$i;?>
                                            <!-- 二级分类 -->
                                            <div class="tree-item level-2" data-id="<?php echo htmlentities((string) $child['id']); ?>">
                                                <div class="tree-item-content" onclick="selectCategory('<?php echo htmlentities((string) $child['id']); ?>', '<?php echo htmlentities((string) $child['name']); ?>')">
                                                    <i class="fas fa-chevron-right expand-icon" onclick="event.stopPropagation(); toggleTreeNode(this)"></i>
                                                    <div class="category-icon level-2-icon">
                                                        <i class="fas fa-square"></i>
                                                    </div>
                                                    <span><?php echo htmlentities((string) $child['name']); ?></span>
                                                </div>
                                                <div class="tree-children" style="display: none;">
                                                    <?php if(is_array($child['children']) || $child['children'] instanceof \think\Collection || $child['children'] instanceof \think\Paginator): $i = 0; $__LIST__ = $child['children'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$grandchild): $mod = ($i % 2 );++$i;?>
                                                        <!-- 三级分类 -->
                                                        <div class="tree-item level-3" data-id="<?php echo htmlentities((string) $grandchild['id']); ?>">
                                                            <div class="tree-item-content" onclick="selectCategory('<?php echo htmlentities((string) $grandchild['id']); ?>', '<?php echo htmlentities((string) $grandchild['name']); ?>')">
                                                                <span class="expand-icon-placeholder"></span>
                                                                <div class="category-icon level-3-icon">
                                                                    <i class="fas fa-circle"></i>
                                                                </div>
                                                                <span><?php echo htmlentities((string) $grandchild['name']); ?></span>
                                                            </div>
                                                        </div>
                                                    <?php endforeach; endif; else: echo "" ;endif; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; endif; else: echo "" ;endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </div>
                        <input type="hidden" name="category_id" id="categoryIdInput" value="<?php echo htmlentities((string) $filters['category_id']); ?>">
                    </div>
                </div>

                <div class="col-md-4">
                    <input type="text" name="keyword" class="modern-form-control" placeholder="搜索卡密编号" value="<?php echo htmlentities((string) $filters['keyword']); ?>">
                </div>

                <div class="col-md-3">
                    <div class="d-flex gap-2">
                        <button type="submit" class="modern-btn modern-btn-primary">
                            <i class="fas fa-search"></i>
                            搜索
                        </button>
                        <a href="/cards" class="modern-btn modern-btn-outline">
                            <i class="fas fa-undo"></i>
                            重置
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 卡密列表 -->
<div class="modern-card">
    <div class="modern-card-header">
        <h5 class="modern-card-title">
            <i class="fas fa-list me-2"></i>
            卡密列表
        </h5>
        <div class="d-flex gap-2">
            <button type="button" id="selectAllBtn" class="modern-btn modern-btn-outline btn-sm" onclick="toggleSelectAllBtn()">
                <i class="fas fa-check-square"></i>
                全选
            </button>
            <button type="button" class="modern-btn modern-btn-success btn-sm" onclick="showGenerateModal()">
                <i class="fas fa-plus"></i>
                生成卡密
            </button>
            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="batchAction('disable')">
                <i class="fas fa-ban"></i>
                批量禁用
            </button>
            <button type="button" class="modern-btn modern-btn-outline btn-sm" onclick="showExportModal()">
                <i class="fas fa-download"></i>
                导出
            </button>
        </div>
    </div>

    <div class="modern-card-body p-0">
        <div class="modern-table-container">
            <table class="modern-table">
            <thead>
                <tr>
                    <th width="50">
                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                    </th>
                    <th>卡密编号</th>
                    <th>分类</th>
                    <th>状态</th>
                    <th>内容</th>
                    <th>创建时间</th>
                    <th>使用时间</th>
                    <th>过期时间</th>
                    <th width="120">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if(is_array($cards) || $cards instanceof \think\Collection || $cards instanceof \think\Paginator): $i = 0; $__LIST__ = $cards;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$card): $mod = ($i % 2 );++$i;?>
                <tr>
                    <td class="checkbox-cell">
                        <input type="checkbox" class="card-checkbox" value="<?php echo htmlentities((string) $card['id']); ?>">
                    </td>
                    <td>
                        <span class="card-code"><?php echo htmlentities((string) $card['card_code']); ?></span>
                    </td>
                    <td>
                        <span class="modern-badge modern-badge-primary" title="<?php echo htmlentities((string) $card['category_path']); ?>"><?php echo htmlentities((string) $card['category_path']); ?></span>
                    </td>
                    <td>
                        <?php switch($card['status']): case "0": ?>
                                <span class="status-badge status-pending">
                                    <i class="fas fa-clock"></i>
                                    未使用
                                </span>
                            <?php break; case "1": ?>
                                <span class="status-badge status-active">
                                    <i class="fas fa-check-circle"></i>
                                    已使用
                                </span>
                            <?php break; case "2": ?>
                                <span class="status-badge status-inactive">
                                    <i class="fas fa-ban"></i>
                                    已禁用
                                </span>
                            <?php break; ?>
                        <?php endswitch; ?>
                    </td>
                    <td>
                        <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlentities((string) $card['content']); ?>">
                            <?php echo htmlentities((string) $card['content']); ?>
                        </div>
                    </td>
                    <td class="date-cell"><?php echo htmlentities((string) $card['created_at']); ?></td>
                    <td class="date-cell"><?php echo !empty($card['used_at']) ? htmlentities((string) $card['used_at']) : '-'; ?></td>
                    <td class="date-cell"><?php echo !empty($card['expire_at']) ? htmlentities((string) $card['expire_at']) : '-'; ?></td>
                    <td>
                        <div class="action-buttons">
                            <button type="button" class="action-btn btn-view" onclick="viewCardDetail(<?php echo htmlentities((string) $card['id']); ?>)" title="查看详情">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button type="button" class="action-btn btn-edit" onclick="toggleCardStatus(<?php echo htmlentities((string) $card['id']); ?>, <?php echo htmlentities((string) $card['status']); ?>)" title="切换状态">
                                <i class="fas fa-<?php echo $card['status']==0 ? 'ban'  :  'check'; ?>"></i>
                            </button>
                            <button type="button" class="action-btn btn-delete" onclick="deleteCard(<?php echo htmlentities((string) $card['id']); ?>)" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                <?php endforeach; endif; else: echo "" ;endif; if(empty($cards) || (($cards instanceof \think\Collection || $cards instanceof \think\Paginator ) && $cards->isEmpty())): ?>
                <tr>
                    <td colspan="9" class="text-center py-5">
                        <div class="text-muted">
                            <i class="fas fa-inbox fa-3x mb-3 opacity-25"></i>
                            <div>暂无卡密数据</div>
                            <div class="mt-2">
                                <button class="modern-btn modern-btn-primary" onclick="showGenerateModal()">
                                    <i class="fas fa-plus"></i>
                                    立即生成
                                </button>
                            </div>
                        </div>
                    </td>
                </tr>
                <?php endif; ?>
            </tbody>
        </table>
        </div>
    </div>

    <!-- 分页 -->
    <div class="modern-card-footer">
        <div class="modern-pagination-wrapper">
            <div class="pagination-info">
                <div class="info-text">
                    <i class="fas fa-info-circle"></i>
                    <span>显示第 <span class="info-numbers"><?php echo htmlentities((string) $cards->currentPage()); ?></span> 页，共 <span class="info-numbers"><?php echo htmlentities((string) $cards->lastPage()); ?></span> 页，总计 <span class="info-numbers"><?php echo htmlentities((string) $cards->total()); ?></span> 条记录</span>
                </div>
                <div class="d-flex align-items-center gap-2 mt-2">
                    <span class="text-muted small">每页显示:</span>
                    <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
                        <option value="5" <?php echo $filters['limit']==5 ? 'selected'  :  ''; ?>>5条</option>
                        <option value="10" <?php echo $filters['limit']==10 ? 'selected'  :  ''; ?>>10条</option>
                        <option value="20" <?php echo $filters['limit']==20 ? 'selected'  :  ''; ?>>20条</option>
                        <option value="50" <?php echo $filters['limit']==50 ? 'selected'  :  ''; ?>>50条</option>
                        <option value="100" <?php echo $filters['limit']==100 ? 'selected'  :  ''; ?>>100条</option>
                    </select>
                </div>
            </div>
            <div>
                <?php if($cards->hasPages()): ?>
                    <?php echo $cards->appends(request()->param())->render(); ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 导出模态框 -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">导出卡密</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="exportForm">
                    <div class="mb-3">
                        <label for="exportPassword" class="form-label">导出密码</label>
                        <input type="password" class="form-control" id="exportPassword" required>
                        <div class="form-text">请输入导出密码以确认身份</div>
                    </div>
                    <div class="mb-3">
                        <label for="exportStatus" class="form-label">状态筛选</label>
                        <select class="form-select" id="exportStatus">
                            <option value="">全部状态</option>
                            <option value="0">未使用</option>
                            <option value="1">已使用</option>
                            <option value="2">已禁用</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="exportCategory" class="form-label">分类筛选</label>
                        <select class="form-select" id="exportCategory">
                            <option value="">全部分类</option>
                            <?php if(is_array($categories) || $categories instanceof \think\Collection || $categories instanceof \think\Paginator): $i = 0; $__LIST__ = $categories;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$category): $mod = ($i % 2 );++$i;?>
                            <option value="<?php echo htmlentities((string) $category['id']); ?>"><?php echo htmlentities((string) $category['name']); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportCards()">导出</button>
            </div>
        </div>
    </div>
</div>

        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 侧边栏管理器
        class SidebarManager {
            constructor() {
                this.sidebar = document.getElementById('sidebar');
                this.mainContent = document.getElementById('mainContent');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.isAnimating = false;
                this.resizeTimeout = null;

                this.init();
            }

            init() {
                // 绑定事件
                this.sidebarToggle.addEventListener('click', (e) => this.handleToggle(e));
                document.addEventListener('click', (e) => this.handleOutsideClick(e));
                window.addEventListener('resize', () => this.handleResize());

                // 初始化状态
                this.updateLayout();
            }

            handleToggle(e) {
                e.preventDefault();
                e.stopPropagation();

                if (this.isAnimating) return;

                this.isAnimating = true;

                if (window.innerWidth <= 768) {
                    this.toggleMobile();
                } else {
                    this.toggleDesktop();
                }

                // 动画完成后重置标志
                setTimeout(() => {
                    this.isAnimating = false;
                }, 300);
            }

            toggleMobile() {
                this.sidebar.classList.toggle('show');
            }

            toggleDesktop() {
                const isCollapsed = this.sidebar.classList.contains('collapsed');

                if (isCollapsed) {
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    this.sidebar.classList.add('collapsed');
                    this.mainContent.classList.add('expanded');
                }
            }

            handleOutsideClick(e) {
                if (window.innerWidth <= 768) {
                    if (!this.sidebar.contains(e.target) && !this.sidebarToggle.contains(e.target)) {
                        this.sidebar.classList.remove('show');
                    }
                }
            }

            handleResize() {
                // 防抖处理
                clearTimeout(this.resizeTimeout);
                this.resizeTimeout = setTimeout(() => {
                    this.updateLayout();
                }, 150);
            }

            updateLayout() {
                const isMobile = window.innerWidth <= 768;

                if (isMobile) {
                    // 移动端：移除桌面端的类
                    this.sidebar.classList.remove('collapsed');
                    this.mainContent.classList.remove('expanded');
                } else {
                    // 桌面端：移除移动端的类
                    this.sidebar.classList.remove('show');
                }
            }
        }

        // 初始化侧边栏管理器
        document.addEventListener('DOMContentLoaded', function() {
            // 添加加载类防止初始化闪烁
            document.body.classList.add('sidebar-loading');

            // 初始化管理器
            new SidebarManager();

            // 移除加载类，启用过渡效果
            setTimeout(() => {
                document.body.classList.remove('sidebar-loading');
            }, 100);
        });

        // 导航激活状态管理器
        class NavigationManager {
            constructor() {
                this.currentPath = window.location.pathname;
                this.navLinks = document.querySelectorAll('.nav-link');
                this.init();
            }

            init() {
                // 添加导航加载类，暂时禁用过渡效果
                document.body.classList.add('nav-loading');

                // 立即设置激活状态，避免闪烁
                this.setActiveState();

                // 移除加载类，启用过渡效果
                setTimeout(() => {
                    document.body.classList.remove('nav-loading');
                }, 50);

                // 监听页面变化（如果使用了PJAX或类似技术）
                window.addEventListener('popstate', () => {
                    this.currentPath = window.location.pathname;
                    this.setActiveState();
                });
            }

            setActiveState() {
                // 使用requestAnimationFrame确保在下一帧执行，避免闪烁
                requestAnimationFrame(() => {
                    this.navLinks.forEach(link => {
                        const href = link.getAttribute('href');
                        const isActive = this.isLinkActive(href);

                        // 只在状态真正改变时才操作DOM
                        if (isActive && !link.classList.contains('active')) {
                            link.classList.add('active');
                        } else if (!isActive && link.classList.contains('active')) {
                            link.classList.remove('active');
                        }
                    });
                });
            }

            isLinkActive(href) {
                if (!href) return false;

                // 精确匹配路径
                if (this.currentPath === href) {
                    return true;
                }

                // 处理子路径匹配
                if (href !== '/' && this.currentPath.startsWith(href + '/')) {
                    return true;
                }

                // 特殊处理：根路径只在完全匹配时激活
                if (href === '/' && this.currentPath === '/') {
                    return true;
                }

                return false;
            }
        }

        // 初始化导航管理器
        document.addEventListener('DOMContentLoaded', function() {
            new NavigationManager();
        });
    </script>
    
    
<!-- 引入JavaScript模块 -->
<script src="/static/js/common.js"></script>
<script src="/static/js/api.js"></script>
<script src="/static/js/components/modal.js"></script>
<script src="/static/js/components/toast.js"></script>
<script src="/static/js/components/table.js"></script>
<script src="/static/js/components/form.js"></script>
<script src="/static/js/components/pagination.js"></script>
<script src="/static/js/utils/validator.js"></script>
<script src="/static/js/utils/formatter.js"></script>
<script src="/static/js/utils/storage.js"></script>
<script src="/static/js/modules/card/detail.js"></script>
<script src="/static/js/modules/card/operations.js"></script>
<script src="/static/js/modules/card/generate.js"></script>
<script src="/static/js/modules/card/index.js"></script>
<script>
// 页面初始化数据
window.pageData = {
    currentPage: <?php echo htmlentities((string) $cards->currentPage()); ?>,
    pageSize: <?php echo htmlentities((string) $filters['limit']); ?>,
    totalPages: <?php echo htmlentities((string) $cards->lastPage()); ?>,
    totalItems: <?php echo htmlentities((string) $cards->total()); ?>
};








    





</script>

<!-- 生成卡密模态框 -->
<div id="generateModal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h2><i class="fas fa-magic"></i> 生成卡密</h2>
            <span class="close" onclick="closeGenerateModal()">&times;</span>
        </div>
        <div class="modal-body">
            <!-- 生成表单 -->
            <form id="generateForm">
                <div class="form-group">
                    <label for="generate_category_selector">选择分类 <span style="color: #ff4d4f;">*</span></label>
                    <div class="category-tree-selector">
                        <div class="tree-selector-input" onclick="toggleGenerateCategoryDropdown()">
                            <span class="selected-text" id="generateSelectedCategoryText">请选择分类</span>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </div>
                        <div class="tree-selector-dropdown" id="generateCategoryDropdown" style="display: none;">
                            <!-- 分类树将通过JavaScript动态加载 -->
                        </div>
                    </div>
                    <input type="hidden" id="generate_category_id" name="category_id" required>
                </div>

                <div class="form-group">
                    <label for="generate_content_id">选择内容 <span style="color: #ff4d4f;">*</span></label>
                    <select id="generate_content_id" name="content_id" class="form-control" required>
                        <option value="">请先选择分类</option>
                    </select>
                    <small class="form-text">选择该分类下的内容作为卡密兑换内容</small>
                </div>

                <div class="form-group">
                    <label for="generate_count">生成数量 <span style="color: #ff4d4f;">*</span></label>
                    <input type="number" id="generate_count" name="count" class="form-control"
                           min="1" max="1000" value="1" required>
                    <small class="form-text">请输入1-1000之间的数字</small>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn-secondary" onclick="closeGenerateModal()">
                <i class="fas fa-times"></i> 取消
            </button>
            <button type="button" id="generateSubmitBtn" class="btn-primary" onclick="submitGenerate()">
                <span class="btn-text">
                    <i class="fas fa-magic"></i> 开始生成
                </span>
                <span class="loading-spinner" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i> 生成中...
                </span>
            </button>
        </div>
    </div>
</div>

<!-- 卡密详情模态框 -->
<div id="detailModal" class="modal-overlay" style="display: none;">
    <div class="modal-container" style="max-width: 600px;">
        <div class="modal-header">
            <h3>卡密详情</h3>
            <button type="button" class="modal-close" onclick="closeDetailModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="detail-info">
                <div class="info-row">
                    <label>卡密编号：</label>
                    <span id="detail_card_code" class="text-primary font-weight-bold"></span>
                </div>
                <div class="info-row">
                    <label>所属分类：</label>
                    <span id="detail_category_path"></span>
                </div>
                <div class="info-row">
                    <label>关联内容：</label>
                    <span id="detail_content_title"></span>
                </div>
                <div class="info-row">
                    <label>卡密状态：</label>
                    <span id="detail_status"></span>
                </div>
                <div class="info-row">
                    <label>创建时间：</label>
                    <span id="detail_created_at"></span>
                </div>
                <div class="info-row">
                    <label>过期时间：</label>
                    <span id="detail_expire_time"></span>
                </div>
                <div class="info-row">
                    <label>是否已兑换：</label>
                    <span id="detail_is_used"></span>
                </div>
                <div class="info-row" id="detail_used_time_row" style="display: none;">
                    <label>兑换时间：</label>
                    <span id="detail_used_time"></span>
                </div>
                <div class="info-row" id="detail_used_ip_row" style="display: none;">
                    <label>兑换IP：</label>
                    <span id="detail_used_ip"></span>
                </div>
                <div class="info-row">
                    <label>备注信息：</label>
                    <span id="detail_remark"></span>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button type="button" class="modern-btn modern-btn-outline" onclick="closeDetailModal()">关闭</button>
        </div>
    </div>
</div>


</body>
</html>
