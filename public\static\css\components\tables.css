/**
 * 表格组件样式
 * 统一的表格样式系统
 */

/* 基础表格样式 */
.table {
  width: 100%;
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--spacing-3) var(--spacing-4);
  vertical-align: middle;
  border-bottom: var(--border-width) solid var(--border-color);
}

.table th {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  background-color: var(--content-bg);
  border-bottom: 2px solid var(--border-color);
  text-align: left;
  font-size: var(--font-size-sm);
}

.table tbody tr {
  transition: background-color var(--transition-fast);
}

.table tbody tr:hover {
  background-color: var(--gray-50);
}

/* 表格变体 */
.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--gray-50);
}

.table-striped tbody tr:nth-of-type(odd):hover {
  background-color: var(--gray-100);
}

.table-bordered {
  border: var(--border-width) solid var(--border-color);
}

.table-bordered th,
.table-bordered td {
  border: var(--border-width) solid var(--border-color);
}

.table-borderless th,
.table-borderless td,
.table-borderless thead th,
.table-borderless tbody + tbody {
  border: 0;
}

/* 表格尺寸 */
.table-sm th,
.table-sm td {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.table-lg th,
.table-lg td {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-base);
}

/* 现代表格样式 */
.modern-table-container {
  background: var(--white);
  border-radius: var(--border-radius-xl);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1px solid rgba(229, 231, 235, 0.3);
  position: relative;
}

.modern-table-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-hover) 100%);
}

.modern-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
  font-size: 0.875rem;
}

.modern-table th {
  background: linear-gradient(135deg, var(--content-bg) 0%, #e8eaed 100%);
  padding: 1.25rem 1.5rem;
  text-align: left;
  font-weight: 600;
  font-size: 0.8125rem;
  color: var(--text-primary);
  border-bottom: 2px solid rgba(229, 231, 235, 0.6);
  position: sticky;
  top: 0;
  z-index: 10;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  white-space: nowrap;
}

.modern-table th:first-child {
  border-top-left-radius: var(--border-radius-xl);
}

.modern-table th:last-child {
  border-top-right-radius: var(--border-radius-xl);
}

.modern-table td {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.4);
  font-size: 0.875rem;
  color: var(--text-primary);
  vertical-align: middle;
  line-height: 1.5;
}

.modern-table tbody tr {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.modern-table tbody tr:hover {
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.04) 0%, rgba(64, 169, 255, 0.04) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.modern-table tbody tr:last-child td {
  border-bottom: none;
}

.modern-table tbody tr:last-child td:first-child {
  border-bottom-left-radius: var(--border-radius-xl);
}

.modern-table tbody tr:last-child td:last-child {
  border-bottom-right-radius: var(--border-radius-xl);
}

/* 表格内容样式增强 */
.modern-table .status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.modern-table .status-badge.status-active {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(21, 128, 61, 0.1) 100%);
  color: var(--success-color);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.modern-table .status-badge.status-inactive {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.modern-table .status-badge.status-pending {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.modern-table .action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.modern-table .action-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  font-size: 0.875rem;
}

.modern-table .action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  text-decoration: none;
}

.modern-table .action-btn.btn-edit {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.modern-table .action-btn.btn-delete {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.modern-table .action-btn.btn-view {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.modern-table .content-title-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.modern-table .content-title {
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.4;
}

.modern-table .content-category {
  font-size: 0.75rem;
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.modern-table .content-category i {
  font-size: 0.625rem;
}

.modern-table .card-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.8125rem;
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.modern-table .date-cell {
  color: var(--gray-600);
  font-size: 0.8125rem;
}

.modern-table .checkbox-cell {
  width: 50px;
  text-align: center;
}

.modern-table .checkbox-cell input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

/* 分类管理特殊样式 */
.category-table .category-row {
  position: relative;
}

.category-table .category-row.level-1 {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.02) 0%, rgba(168, 85, 247, 0.02) 100%);
  border-left: 4px solid var(--primary-color);
}

.category-table .category-row.level-2 {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, rgba(21, 128, 61, 0.02) 100%);
  border-left: 4px solid var(--success-color);
}

.category-table .category-row.level-2 td:first-child {
  padding-left: 2.5rem;
  position: relative;
}

.category-table .category-row.level-2 td:first-child::before {
  content: '└─';
  position: absolute;
  left: 1.5rem;
  color: var(--gray-400);
  font-family: monospace;
}

.category-table .category-row.level-3 {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.02) 0%, rgba(217, 119, 6, 0.02) 100%);
  border-left: 4px solid var(--warning-color);
}

.category-table .category-row.level-3 td:first-child {
  padding-left: 3.5rem;
  position: relative;
}

.category-table .category-row.level-3 td:first-child::before {
  content: '　└─';
  position: absolute;
  left: 1.5rem;
  color: var(--gray-400);
  font-family: monospace;
}

.category-table .category-name {
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.category-table .category-name i {
  color: var(--primary-color);
  font-size: 0.875rem;
}

.category-table .category-description {
  color: var(--gray-600);
  font-size: 0.8125rem;
  line-height: 1.4;
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.category-table .sort-order {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
  color: var(--primary-color);
  border-radius: 50%;
  font-weight: 600;
  font-size: 0.8125rem;
  border: 1px solid rgba(99, 102, 241, 0.2);
}

.category-table .children-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(21, 128, 61, 0.1) 100%);
  color: var(--success-color);
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.75rem;
  padding: 0 0.5rem;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

/* 响应式表格样式 */
@media (max-width: 768px) {
  .modern-table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .modern-table {
    min-width: 600px;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.875rem 1rem;
    font-size: 0.8125rem;
  }

  .action-buttons {
    flex-direction: column;
    gap: 0.25rem;
  }

  .action-btn {
    width: 28px;
    height: 28px;
    font-size: 0.75rem;
  }

  .status-badge {
    font-size: 0.6875rem;
    padding: 0.25rem 0.5rem;
  }

  .card-code {
    font-size: 0.75rem;
    padding: 0.1875rem 0.375rem;
  }
}

@media (max-width: 576px) {
  .modern-table th,
  .modern-table td {
    padding: 0.75rem 0.75rem;
  }

  .content-title-cell {
    max-width: 200px;
  }

  .category-description {
    max-width: 150px;
  }
}

/* 表格加载状态 */
.modern-table-loading {
  position: relative;
  pointer-events: none;
  opacity: 0.6;
}

.modern-table-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid rgba(99, 102, 241, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 表格空状态 */
.modern-table-empty {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--gray-500);
}

.modern-table-empty i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}

.modern-table-empty h4 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--gray-700);
}

.modern-table-empty p {
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

/* 表格行选中状态 */
.modern-table tbody tr.selected {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(168, 85, 247, 0.08) 100%);
  border-left: 3px solid var(--primary-color);
}

.modern-table tbody tr.selected:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.12) 0%, rgba(168, 85, 247, 0.12) 100%);
}

/* 表格排序指示器 */
.modern-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.modern-table th.sortable:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

.modern-table th.sortable::after {
  content: '\f0dc';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: 0.75rem;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.modern-table th.sortable:hover::after {
  opacity: 0.6;
}

.modern-table th.sortable.sort-asc::after {
  content: '\f0de';
  opacity: 1;
  color: var(--primary-color);
}

.modern-table th.sortable.sort-desc::after {
  content: '\f0dd';
  opacity: 1;
  color: var(--primary-color);
}

/* 表格筛选器 */
.table-filters {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--gray-50);
  border-bottom: 1px solid rgba(229, 231, 235, 0.4);
  flex-wrap: wrap;
}

.table-filters .filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.table-filters .filter-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  margin: 0;
}

.table-filters .filter-group select,
.table-filters .filter-group input {
  font-size: 0.875rem;
  border: 1px solid rgba(229, 231, 235, 0.6);
  border-radius: var(--border-radius);
  padding: 0.375rem 0.75rem;
  background: var(--white);
}

/* 表格工具栏 */
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: var(--white);
  border-bottom: 1px solid rgba(229, 231, 235, 0.4);
  flex-wrap: wrap;
  gap: 1rem;
}

.table-toolbar .toolbar-left,
.table-toolbar .toolbar-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.table-toolbar .selected-count {
  font-size: 0.875rem;
  color: var(--gray-600);
  background: rgba(99, 102, 241, 0.1);
  padding: 0.375rem 0.75rem;
  border-radius: var(--border-radius);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

/* 表格响应式容器 */
.table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-responsive > .table {
  margin-bottom: 0;
}

/* 表格操作列 */
.table-actions {
  white-space: nowrap;
  width: 1%;
}

.table-actions .btn {
  margin-right: var(--spacing-1);
}

.table-actions .btn:last-child {
  margin-right: 0;
}

/* 状态徽章 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.status-active,
.status-badge.status-enabled,
.status-badge.status-used {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-badge.status-inactive,
.status-badge.status-disabled,
.status-badge.status-unused {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.status-badge.status-expired,
.status-badge.status-deleted {
  background-color: var(--danger-light);
  color: var(--danger-color);
}

.status-badge.status-pending {
  background-color: var(--info-light);
  color: var(--info-color);
}

/* 表格排序 */
.sortable-header {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: color var(--transition-fast);
}

.sortable-header:hover {
  color: var(--primary-color);
}

.sortable-header::after {
  content: '';
  position: absolute;
  right: var(--spacing-2);
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid var(--gray-400);
  opacity: 0.5;
  transition: all var(--transition-fast);
}

.sortable-header.sort-asc::after {
  border-bottom: 4px solid var(--primary-color);
  border-top: none;
  opacity: 1;
}

.sortable-header.sort-desc::after {
  border-top: 4px solid var(--primary-color);
  border-bottom: none;
  opacity: 1;
}

/* 表格筛选 */
.table-filters {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-4);
  background: var(--gray-100);
  border-radius: var(--border-radius);
}

.table-search {
  flex: 1;
  max-width: 300px;
}

.table-filter-select {
  min-width: 150px;
}

/* 表格分页 */
.table-pagination {
  display: flex;
  justify-content: between;
  align-items: center;
  padding: var(--spacing-4);
  border-top: var(--border-width) solid var(--border-color);
  background: var(--gray-50);
}

.table-info {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

/* 表格选择 */
.table-select {
  width: 1%;
  text-align: center;
}

.table-select input[type="checkbox"] {
  margin: 0;
}

/* 批量操作栏 */
.table-bulk-actions {
  display: none;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  background: var(--primary-light);
  border-bottom: var(--border-width) solid var(--primary-color);
}

.table-bulk-actions.show {
  display: flex;
}

.table-bulk-info {
  font-size: var(--font-size-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.table-bulk-buttons {
  display: flex;
  gap: var(--spacing-2);
  margin-left: auto;
}

/* 空状态 */
.table-empty {
  text-align: center;
  padding: var(--spacing-12) var(--spacing-6);
  color: var(--gray-500);
}

.table-empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
  opacity: 0.5;
}

.table-empty-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
}

.table-empty-description {
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-4);
}

/* 加载状态 */
.table-loading {
  position: relative;
}

.table-loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.table-loading::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 32px;
  height: 32px;
  border: 3px solid var(--gray-300);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 11;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 响应式表格 */
@media (max-width: 768px) {
  .table-responsive-stack {
    display: block;
  }
  
  .table-responsive-stack thead {
    display: none;
  }
  
  .table-responsive-stack tbody,
  .table-responsive-stack tr,
  .table-responsive-stack td {
    display: block;
    width: 100%;
  }
  
  .table-responsive-stack tr {
    border: var(--border-width) solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-4);
    background: var(--white);
  }
  
  .table-responsive-stack td {
    border: none;
    padding: var(--spacing-2) 0;
    text-align: right;
    position: relative;
    padding-left: 50%;
  }
  
  .table-responsive-stack td::before {
    content: attr(data-label);
    position: absolute;
    left: 0;
    width: 45%;
    text-align: left;
    font-weight: var(--font-weight-semibold);
    color: var(--gray-700);
  }
}
