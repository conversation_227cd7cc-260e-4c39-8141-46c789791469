/**
 * 系统设置主控制器
 * 统一管理系统设置相关的所有功能
 */

const SettingsManager = {
    /**
     * 初始化系统设置功能
     */
    init() {
        console.log('初始化系统设置功能...');
        
        // 初始化各个子模块
        this.initSubModules();
        
        // 绑定页面事件
        this.bindEvents();
        
        // 初始化页面状态
        this.initPageState();
        
        console.log('系统设置功能初始化完成');
    },
    
    /**
     * 初始化子模块
     */
    initSubModules() {
        // 初始化导航功能
        if (typeof SettingsNavigation !== 'undefined') {
            SettingsNavigation.init();
        }
        
        // 初始化表单功能
        if (typeof SettingsForm !== 'undefined') {
            SettingsForm.init();
        }
        
        // 初始化系统信息功能
        if (typeof SystemInfo !== 'undefined') {
            SystemInfo.init();
        }
    },
    
    /**
     * 绑定页面事件
     */
    bindEvents() {
        // 绑定窗口关闭前确认
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = '您有未保存的更改，确定要离开此页面吗？';
                return e.returnValue;
            }
        });
        
        // 绑定页面可见性变化事件
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // 页面重新可见时，刷新系统信息
                if (SettingsNavigation.getCurrentPanel() === 'system') {
                    SystemInfo.loadSystemInfo();
                }
            }
        });
        
        // 绑定在线状态变化事件
        window.addEventListener('online', () => {
            Toast.success('网络连接已恢复');
        });
        
        window.addEventListener('offline', () => {
            Toast.warning('网络连接已断开，请检查网络设置');
        });
    },
    
    /**
     * 初始化页面状态
     */
    initPageState() {
        // 检查是否有保存的草稿
        this.loadDraft();
        
        // 设置自动保存草稿
        this.setupAutoSave();
        
        // 初始化主题
        this.initTheme();
    },
    
    /**
     * 检查是否有未保存的更改
     * @returns {boolean}
     */
    hasUnsavedChanges() {
        return document.querySelectorAll('.field-changed').length > 0;
    },
    
    /**
     * 加载草稿
     */
    loadDraft() {
        try {
            const draft = localStorage.getItem('settings_draft');
            if (draft) {
                const draftData = JSON.parse(draft);
                const draftTime = new Date(draftData.timestamp);
                const now = new Date();
                
                // 如果草稿是30分钟内的，询问是否恢复
                if (now - draftTime < 30 * 60 * 1000) {
                    if (confirm('发现未保存的设置草稿，是否恢复？')) {
                        this.restoreDraft(draftData.data);
                    }
                }
            }
        } catch (error) {
            console.error('加载草稿失败:', error);
        }
    },
    
    /**
     * 恢复草稿
     * @param {object} draftData 草稿数据
     */
    restoreDraft(draftData) {
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        Object.keys(draftData).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = draftData[key] === '1';
                } else {
                    field.value = draftData[key];
                }
                SettingsForm.markFieldChanged(field);
            }
        });
        
        Toast.info('草稿已恢复');
    },
    
    /**
     * 保存草稿
     */
    saveDraft() {
        try {
            const form = document.getElementById('settingsForm');
            if (!form) return;
            
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            const draft = {
                timestamp: new Date().toISOString(),
                data: data
            };
            
            localStorage.setItem('settings_draft', JSON.stringify(draft));
        } catch (error) {
            console.error('保存草稿失败:', error);
        }
    },
    
    /**
     * 清除草稿
     */
    clearDraft() {
        try {
            localStorage.removeItem('settings_draft');
        } catch (error) {
            console.error('清除草稿失败:', error);
        }
    },
    
    /**
     * 设置自动保存草稿
     */
    setupAutoSave() {
        // 每30秒自动保存草稿
        setInterval(() => {
            if (this.hasUnsavedChanges()) {
                this.saveDraft();
            }
        }, 30000);
        
        // 监听表单变化，延迟保存草稿
        let saveTimeout;
        document.addEventListener('input', () => {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                if (this.hasUnsavedChanges()) {
                    this.saveDraft();
                }
            }, 5000);
        });
    },
    
    /**
     * 初始化主题
     */
    initTheme() {
        const themeColorField = document.getElementById('theme_color');
        if (themeColorField) {
            // 监听主题颜色变化
            themeColorField.addEventListener('input', (e) => {
                this.previewThemeColor(e.target.value);
            });
            
            // 初始化颜色预览
            this.previewThemeColor(themeColorField.value);
        }
    },
    
    /**
     * 预览主题颜色
     * @param {string} color 颜色值
     */
    previewThemeColor(color) {
        if (!color) return;
        
        // 更新CSS变量
        document.documentElement.style.setProperty('--primary-color', color);
        
        // 更新相关元素的颜色
        const elements = document.querySelectorAll('.modern-btn-primary, .nav-item.active');
        elements.forEach(element => {
            element.style.backgroundColor = color;
        });
    },
    
    /**
     * 导出设置
     */
    exportSettings() {
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        const formData = new FormData(form);
        const settings = {};
        
        for (let [key, value] of formData.entries()) {
            settings[key] = value;
        }
        
        // 创建下载链接
        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `settings_${new Date().toISOString().split('T')[0]}.json`;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(url);
        Toast.success('设置已导出');
    },
    
    /**
     * 导入设置
     */
    importSettings() {
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.json';
        fileInput.style.display = 'none';
        
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = (e) => {
                try {
                    const settings = JSON.parse(e.target.result);
                    this.applyImportedSettings(settings);
                } catch (error) {
                    Toast.error('导入失败：文件格式不正确');
                }
            };
            reader.readAsText(file);
        });
        
        document.body.appendChild(fileInput);
        fileInput.click();
        document.body.removeChild(fileInput);
    },
    
    /**
     * 应用导入的设置
     * @param {object} settings 设置数据
     */
    applyImportedSettings(settings) {
        if (!confirm('确定要导入这些设置吗？这将覆盖当前的设置。')) {
            return;
        }
        
        const form = document.getElementById('settingsForm');
        if (!form) return;
        
        let importedCount = 0;
        
        Object.keys(settings).forEach(key => {
            const field = form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'checkbox') {
                    field.checked = settings[key] === '1' || settings[key] === true;
                } else {
                    field.value = settings[key];
                }
                SettingsForm.markFieldChanged(field);
                importedCount++;
            }
        });
        
        Toast.success(`成功导入 ${importedCount} 项设置`);
    },
    
    /**
     * 重置为默认设置
     */
    resetToDefaults() {
        if (!confirm('确定要重置为默认设置吗？这将丢失所有自定义配置。')) {
            return;
        }
        
        API.settings.resetToDefaults()
            .then(data => {
                if (data.code === 200) {
                    Toast.success('已重置为默认设置');
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '重置失败');
                }
            })
            .catch(error => {
                console.error('重置设置失败:', error);
                Toast.error('重置失败，请稍后重试');
            });
    },
    
    /**
     * 清理缓存
     */
    clearCache() {
        if (!confirm('确定要清理系统缓存吗？')) {
            return;
        }
        
        API.settings.clearCache()
            .then(data => {
                if (data.code === 200) {
                    Toast.success('缓存清理成功');
                } else {
                    Toast.error(data.message || '清理失败');
                }
            })
            .catch(error => {
                console.error('清理缓存失败:', error);
                Toast.error('清理失败，请稍后重试');
            });
    }
};

// 全局函数，供HTML调用
window.exportSettings = function() {
    SettingsManager.exportSettings();
};

window.importSettings = function() {
    SettingsManager.importSettings();
};

window.resetToDefaults = function() {
    SettingsManager.resetToDefaults();
};

window.clearCache = function() {
    SettingsManager.clearCache();
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 确保所有依赖都已加载
    if (typeof Utils !== 'undefined' && 
        typeof API !== 'undefined' && 
        typeof Toast !== 'undefined') {
        SettingsManager.init();
    } else {
        console.error('系统设置功能初始化失败：缺少依赖模块');
    }
});

// 导出模块
window.SettingsManager = SettingsManager;
