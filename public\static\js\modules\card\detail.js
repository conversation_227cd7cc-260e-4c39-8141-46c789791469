/**
 * 卡密详情功能模块
 * 处理卡密详情的查看和显示
 */

const CardDetail = {
    /**
     * 查看卡密详情
     * @param {number} cardId 卡密ID
     */
    view(cardId) {
        if (!cardId) {
            Toast.error('卡密ID不能为空');
            return;
        }
        
        API.card.get(cardId)
            .then(data => {
                if (data.code === 200) {
                    this.showDetailModal(data.data);
                } else {
                    Toast.error(data.message || '获取卡密详情失败');
                }
            })
            .catch(error => {
                console.error('获取卡密详情错误:', error);
                Toast.error('获取卡密详情失败');
            });
    },
    
    /**
     * 显示详情模态框
     * @param {object} card 卡密数据
     */
    showDetailModal(card) {
        // 填充基本信息
        this.fillBasicInfo(card);
        
        // 填充状态信息
        this.fillStatusInfo(card);
        
        // 填充兑换信息
        this.fillUsageInfo(card);
        
        // 显示模态框
        Modal.show('detailModal');
    },
    
    /**
     * 填充基本信息
     * @param {object} card 卡密数据
     */
    fillBasicInfo(card) {
        const fields = {
            'detail_card_code': card.card_code,
            'detail_category_path': card.category_name || '未分类',
            'detail_content_title': card.content_title || '无关联内容',
            'detail_created_at': Formatter.dateTime(card.created_at),
            'detail_expire_time': card.expire_time ? Formatter.dateTime(card.expire_time) : '永不过期',
            'detail_remark': card.remark || '无'
        };
        
        Object.keys(fields).forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = fields[id];
            }
        });
    },
    
    /**
     * 填充状态信息
     * @param {object} card 卡密数据
     */
    fillStatusInfo(card) {
        const statusElement = document.getElementById('detail_status');
        if (statusElement) {
            const statusText = Utils.getStatusText(card.status);
            const statusClass = Utils.getStatusClass(card.status);
            statusElement.innerHTML = `<span class="modern-badge ${statusClass}">${statusText}</span>`;
        }
    },
    
    /**
     * 填充兑换信息
     * @param {object} card 卡密数据
     */
    fillUsageInfo(card) {
        const isUsedElement = document.getElementById('detail_is_used');
        const usedTimeRow = document.getElementById('detail_used_time_row');
        const usedIpRow = document.getElementById('detail_used_ip_row');
        const usedTimeElement = document.getElementById('detail_used_time');
        const usedIpElement = document.getElementById('detail_used_ip');
        
        if (card.status === Config.cardStatus.USED) {
            // 已兑换
            if (isUsedElement) {
                isUsedElement.innerHTML = '<span class="text-success">已兑换</span>';
            }
            
            if (usedTimeElement) {
                usedTimeElement.textContent = Formatter.dateTime(card.used_at) || '未知';
            }
            
            if (usedIpElement) {
                usedIpElement.textContent = card.used_ip || '未知';
            }
            
            if (usedTimeRow) usedTimeRow.style.display = 'flex';
            if (usedIpRow) usedIpRow.style.display = 'flex';
        } else {
            // 未兑换
            if (isUsedElement) {
                isUsedElement.innerHTML = '<span class="text-muted">未兑换</span>';
            }
            
            if (usedTimeRow) usedTimeRow.style.display = 'none';
            if (usedIpRow) usedIpRow.style.display = 'none';
        }
    },
    
    /**
     * 关闭详情模态框
     */
    close() {
        Modal.hide('detailModal');
    },
    
    /**
     * 初始化详情功能
     */
    init() {
        // 绑定关闭按钮事件
        const closeButtons = document.querySelectorAll('#detailModal .modal-close, #detailModal .modern-btn-outline');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => this.close());
        });
        
        // 绑定ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const modal = document.getElementById('detailModal');
                if (modal && modal.style.display === 'block') {
                    this.close();
                }
            }
        });
        
        // 绑定模态框背景点击关闭
        const modal = document.getElementById('detailModal');
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.close();
                }
            });
        }
    }
};

// 全局函数，供HTML调用
window.viewCardDetail = function(cardId) {
    CardDetail.view(cardId);
};

window.closeDetailModal = function() {
    CardDetail.close();
};

// 导出模块
window.CardDetail = CardDetail;
