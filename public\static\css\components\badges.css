/**
 * 徽章组件样式
 * 统一的徽章和状态标签样式系统
 */

/* 基础徽章样式 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  color: var(--white);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--border-radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge:empty {
  display: none;
}

/* 徽章颜色变体 */
.badge-primary {
  background-color: var(--primary-color);
}

.badge-secondary {
  background-color: var(--secondary-color);
}

.badge-success {
  background-color: var(--success-color);
}

.badge-warning {
  background-color: var(--warning-color);
}

.badge-danger {
  background-color: var(--danger-color);
}

.badge-info {
  background-color: var(--info-color);
}

.badge-light {
  background-color: var(--gray-200);
  color: var(--gray-800);
}

.badge-dark {
  background-color: var(--gray-800);
}

/* 轮廓徽章 */
.badge-outline-primary {
  color: var(--primary-color);
  background-color: transparent;
  border: var(--border-width) solid var(--primary-color);
}

.badge-outline-secondary {
  color: var(--secondary-color);
  background-color: transparent;
  border: var(--border-width) solid var(--secondary-color);
}

.badge-outline-success {
  color: var(--success-color);
  background-color: transparent;
  border: var(--border-width) solid var(--success-color);
}

.badge-outline-warning {
  color: var(--warning-color);
  background-color: transparent;
  border: var(--border-width) solid var(--warning-color);
}

.badge-outline-danger {
  color: var(--danger-color);
  background-color: transparent;
  border: var(--border-width) solid var(--danger-color);
}

.badge-outline-info {
  color: var(--info-color);
  background-color: transparent;
  border: var(--border-width) solid var(--info-color);
}

/* 现代化状态标签 */
.modern-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.modern-badge-success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
  color: var(--success-color);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.modern-badge-warning {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);
  color: var(--warning-color);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.modern-badge-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
  color: var(--danger-color);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.modern-badge-primary {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);
  color: var(--primary-color);
  border: 1px solid rgba(99, 102, 241, 0.2);
}

/* 徽章尺寸 */
.badge-sm {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: 0.625rem;
}

.badge-lg {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-sm);
}

/* 带图标的徽章 */
.badge-icon {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
}

.badge-icon i {
  font-size: 0.75em;
}

/* 可关闭的徽章 */
.badge-dismissible {
  padding-right: var(--spacing-2);
}

.badge-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 0.875em;
  margin-left: var(--spacing-1);
  cursor: pointer;
  opacity: 0.7;
  transition: opacity var(--transition-fast);
}

.badge-close:hover {
  opacity: 1;
}

/* 数字徽章 */
.badge-number {
  min-width: 1.5rem;
  height: 1.5rem;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  font-size: 0.625rem;
  font-weight: var(--font-weight-bold);
}

/* 点状徽章 */
.badge-dot {
  width: 0.5rem;
  height: 0.5rem;
  padding: 0;
  border-radius: 50%;
  position: relative;
}

.badge-dot.badge-pulse::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: inherit;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* 位置徽章 */
.position-relative {
  position: relative;
}

.badge-positioned {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  transform: translate(50%, -50%);
}

.badge-positioned.badge-top-left {
  top: -0.25rem;
  left: -0.25rem;
  right: auto;
  transform: translate(-50%, -50%);
}

.badge-positioned.badge-bottom-right {
  top: auto;
  bottom: -0.25rem;
  transform: translate(50%, 50%);
}

.badge-positioned.badge-bottom-left {
  top: auto;
  bottom: -0.25rem;
  left: -0.25rem;
  right: auto;
  transform: translate(-50%, 50%);
}
