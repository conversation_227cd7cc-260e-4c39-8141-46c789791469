/**
 * 分类操作功能模块
 * 处理分类的增删改查操作
 */

const CategoryOperations = {
    /**
     * 初始化操作功能
     */
    init() {
        this.bindEvents();
    },
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 绑定表单提交事件
        const form = document.getElementById('categoryModalForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitForm();
            });
        }
        
        // 绑定排序输入框事件
        document.querySelectorAll('.sort-input').forEach(input => {
            input.addEventListener('blur', (e) => {
                const categoryId = e.target.dataset.categoryId;
                const newSort = e.target.value;
                this.updateSort(categoryId, newSort);
            });
            
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.target.blur();
                }
            });
        });
        
        // 绑定状态切换事件
        document.querySelectorAll('.status-toggle').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                const categoryId = e.target.dataset.categoryId;
                const newStatus = e.target.checked ? 1 : 0;
                this.updateStatus(categoryId, newStatus);
            });
        });
    },
    
    /**
     * 显示添加分类模态框
     * @param {number} parentId 父分类ID
     */
    showAddModal(parentId = 0) {
        const form = document.getElementById('categoryModalForm');
        const title = document.getElementById('categoryModalTitle');
        
        // 重置表单
        form.reset();
        document.getElementById('modal_category_id').value = '';
        document.getElementById('modal_parent_id').value = parentId;
        document.getElementById('modal_sort_order').value = '0';
        
        // 设置标题
        title.innerHTML = parentId > 0 ? '<i class="fas fa-plus"></i> 添加子分类' : '<i class="fas fa-plus"></i> 添加分类';
        
        // 重置树形选择器显示
        const selectedText = document.querySelector('.selected-text');
        if (parentId > 0) {
            // 如果有父分类ID，需要获取父分类的完整路径
            this.getFullCategoryPath(parentId).then(fullPath => {
                selectedText.textContent = fullPath;
            });
        } else {
            selectedText.textContent = '顶级分类';
        }
        
        // 加载父分类选项
        this.loadParentOptions();
        
        // 显示模态框
        Modal.show('categoryModal');
    },
    
    /**
     * 显示编辑分类模态框
     * @param {number} categoryId 分类ID
     */
    showEditModal(categoryId) {
        const form = document.getElementById('categoryModalForm');
        const title = document.getElementById('categoryModalTitle');
        
        // 设置标题
        title.innerHTML = '<i class="fas fa-edit"></i> 编辑分类';
        
        // 获取分类信息
        API.category.get(categoryId)
            .then(data => {
                if (data.code === 200) {
                    const category = data.data;
                    
                    // 填充表单
                    document.getElementById('modal_category_id').value = category.id;
                    document.getElementById('modal_name').value = category.name;
                    document.getElementById('modal_description').value = category.description || '';
                    document.getElementById('modal_parent_id').value = category.parent_id || 0;
                    document.getElementById('modal_sort_order').value = category.sort_order || 0;
                    document.getElementById('modal_status').checked = category.status == 1;
                    
                    // 设置树形选择器显示文本
                    const selectedText = document.querySelector('.selected-text');
                    if (category.parent_id && category.parent_id > 0) {
                        // 获取父分类的完整路径
                        this.getFullCategoryPath(category.parent_id).then(fullPath => {
                            selectedText.textContent = fullPath;
                        });
                    } else {
                        selectedText.textContent = '顶级分类';
                    }
                    
                    // 加载父分类选项
                    this.loadParentOptions(categoryId);
                    
                    // 显示模态框
                    Modal.show('categoryModal');
                } else {
                    Toast.error(data.message || '获取分类信息失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Toast.error('获取分类信息失败');
            });
    },
    
    /**
     * 关闭分类模态框
     */
    closeModal() {
        Modal.hide('categoryModal');
        Modal.resetForm('categoryModalForm');
    },
    
    /**
     * 提交表单
     */
    submitForm() {
        const form = document.getElementById('categoryModalForm');
        const validation = this.validateForm(form);
        
        if (!validation.valid) {
            Toast.error(Object.values(validation.errors)[0]);
            return;
        }
        
        const data = validation.data;
        const isEdit = data.category_id && data.category_id !== '';
        
        // 设置加载状态
        Modal.setButtonLoading('#categoryModal .modern-btn-primary', true);
        
        const apiCall = isEdit ? 
            API.category.update(data.category_id, data) : 
            API.category.create(data);
        
        apiCall
            .then(result => {
                if (result.code === 200) {
                    Toast.success(result.message || (isEdit ? '更新成功' : '添加成功'));
                    this.closeModal();
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(result.message || (isEdit ? '更新失败' : '添加失败'));
                }
            })
            .catch(error => {
                console.error('提交错误:', error);
                Toast.error(isEdit ? '更新失败，请稍后重试' : '添加失败，请稍后重试');
            })
            .finally(() => {
                Modal.setButtonLoading('#categoryModal .modern-btn-primary', false);
            });
    },
    
    /**
     * 验证表单
     * @param {HTMLFormElement} form 表单元素
     * @returns {object}
     */
    validateForm(form) {
        const data = Form.serialize(form);

        const rules = {
            name: {
                required: true,
                minLength: 1,
                maxLength: 50,
                message: '分类名称不能为空且长度不能超过50个字符'
            },
            description: {
                maxLength: 200,
                message: '描述长度不能超过200个字符'
            },
            sort_order: {
                type: 'number',
                min: 0,
                max: 9999,
                message: '排序值必须是0-9999之间的数字'
            }
        };

        const validation = Validator.validate(data, rules);

        // 返回验证结果和数据
        return {
            valid: validation.valid,
            errors: validation.errors,
            data: data
        };
    },
    
    /**
     * 删除分类
     * @param {number} categoryId 分类ID
     */
    delete(categoryId) {
        if (!Utils.confirm('确定要删除此分类吗？删除后无法恢复！')) {
            return;
        }
        
        API.category.delete(categoryId)
            .then(data => {
                if (data.code === 200) {
                    Toast.success('删除成功');
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '删除失败');
                }
            })
            .catch(error => {
                console.error('删除错误:', error);
                Toast.error('删除失败，请稍后重试');
            });
    },
    
    /**
     * 更新排序
     * @param {number} categoryId 分类ID
     * @param {number} newSort 新排序值
     */
    updateSort(categoryId, newSort) {
        const sortValue = parseInt(newSort);
        if (isNaN(sortValue) || sortValue < 0 || sortValue > 9999) {
            Toast.warning('排序值必须是0-9999之间的数字');
            return;
        }
        
        API.category.updateSort({ id: categoryId, sort_order: sortValue })
            .then(data => {
                if (data.code === 200) {
                    Toast.success('排序更新成功');
                    setTimeout(() => {
                        Utils.reload();
                    }, 1000);
                } else {
                    Toast.error(data.message || '排序更新失败');
                }
            })
            .catch(error => {
                console.error('排序更新错误:', error);
                Toast.error('排序更新失败，请稍后重试');
            });
    },
    
    /**
     * 更新状态
     * @param {number} categoryId 分类ID
     * @param {number} newStatus 新状态
     */
    updateStatus(categoryId, newStatus) {
        API.category.updateStatus({ id: categoryId, status: newStatus })
            .then(data => {
                if (data.code === 200) {
                    Toast.success('状态更新成功');
                } else {
                    Toast.error(data.message || '状态更新失败');
                    // 恢复开关状态
                    const toggle = document.querySelector(`[data-category-id="${categoryId}"]`);
                    if (toggle) {
                        toggle.checked = !toggle.checked;
                    }
                }
            })
            .catch(error => {
                console.error('状态更新错误:', error);
                Toast.error('状态更新失败，请稍后重试');
                // 恢复开关状态
                const toggle = document.querySelector(`[data-category-id="${categoryId}"]`);
                if (toggle) {
                    toggle.checked = !toggle.checked;
                }
            });
    },
    
    /**
     * 添加子分类
     * @param {number} parentId 父分类ID
     */
    addSubCategory(parentId = null) {
        const id = parentId || CategoryTree.getSelectedCategoryId();
        this.showAddModal(id);
    },
    
    /**
     * 编辑分类
     * @param {number} id 分类ID
     */
    editCategory(id = null) {
        const categoryId = id || CategoryTree.getSelectedCategoryId();
        if (categoryId) {
            this.showEditModal(categoryId);
        }
    },
    
    /**
     * 加载父分类选项
     * @param {number} excludeId 排除的分类ID
     */
    loadParentOptions(excludeId = null) {
        const currentValue = document.getElementById('modal_parent_id').value;
        
        API.category.getParentOptions({ exclude: excludeId })
            .then(data => {
                if (data.code === 200) {
                    CategoryTree.renderTreeSelector(data.data, 'treeSelectorDropdown', currentValue);
                }
            })
            .catch(error => {
                console.error('Error loading parent options:', error);
            });
    },
    
    /**
     * 获取分类完整路径
     * @param {number} categoryId 分类ID
     * @returns {Promise<string>}
     */
    async getFullCategoryPath(categoryId) {
        try {
            const data = await API.category.getPath(categoryId);
            if (data.code === 200) {
                return data.data.path || '未知分类';
            }
        } catch (error) {
            console.error('获取分类路径错误:', error);
        }
        return '未知分类';
    }
};

// 全局函数，供HTML调用
window.showAddModal = function(parentId = 0) {
    CategoryOperations.showAddModal(parentId);
};

window.showEditModal = function(categoryId) {
    CategoryOperations.showEditModal(categoryId);
};

window.closeCategoryModal = function() {
    CategoryOperations.closeModal();
};

window.deleteCategory = function(categoryId) {
    CategoryOperations.delete(categoryId);
};

window.addSubCategory = function(parentId = null) {
    CategoryOperations.addSubCategory(parentId);
};

window.editCategory = function(id = null) {
    CategoryOperations.editCategory(id);
};

window.updateCategorySort = function(categoryId, newSort) {
    CategoryOperations.updateSort(categoryId, newSort);
};

// 导出模块
window.CategoryOperations = CategoryOperations;
