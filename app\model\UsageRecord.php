<?php

namespace app\model;

use think\Model;

/**
 * 卡密使用记录模型
 */
class UsageRecord extends Model
{
    // 设置表名
    protected $table = 'km_usage_records';
    
    // 设置主键
    protected $pk = 'id';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'card_id'     => 'int',
        'card_code'   => 'string',
        'category_id' => 'int',
        'used_ip'     => 'string',
        'user_agent'  => 'string',
        'used_at'     => 'datetime',
    ];
    
    // 自动时间戳
    protected $autoWriteTimestamp = 'datetime';
    
    // 定义时间戳字段名
    protected $createTime = 'used_at';
    protected $updateTime = false;
    
    /**
     * 关联卡密
     */
    public function card()
    {
        return $this->belongsTo(Card::class, 'card_id', 'id');
    }
    
    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }
    
    /**
     * 卡密编号获取器（部分隐藏）
     */
    public function getMaskedCardCodeAttr($value, $data)
    {
        $cardCode = $data['card_code'] ?? '';
        if (strlen($cardCode) <= 8) {
            return $cardCode;
        }
        return substr($cardCode, 0, 4) . '****' . substr($cardCode, -4);
    }
    
    /**
     * 获取最近使用记录
     */
    public static function getRecentRecords($limit = 5)
    {
        return self::alias('ur')
                   ->leftJoin('km_categories c', 'ur.category_id = c.id')
                   ->field([
                       'ur.*',
                       'c.name as category_name'
                   ])
                   ->order('ur.used_at', 'desc')
                   ->limit($limit)
                   ->select();
    }
    
    /**
     * 创建使用记录
     */
    public static function createRecord($cardId, $cardCode, $categoryId, $ip = null, $userAgent = null)
    {
        return self::create([
            'card_id' => $cardId,
            'card_code' => $cardCode,
            'category_id' => $categoryId,
            'used_ip' => $ip ?: request()->ip(),
            'user_agent' => $userAgent ?: request()->header('user-agent'),
            'used_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 获取使用统计
     */
    public static function getUsageStats($period = 'today')
    {
        $where = [];
        
        switch ($period) {
            case 'today':
                $where[] = ['used_at', '>=', date('Y-m-d 00:00:00')];
                break;
            case 'yesterday':
                $where[] = ['used_at', '>=', date('Y-m-d 00:00:00', strtotime('-1 day'))];
                $where[] = ['used_at', '<', date('Y-m-d 00:00:00')];
                break;
            case 'week':
                $where[] = ['used_at', '>=', date('Y-m-d 00:00:00', strtotime('-7 days'))];
                break;
            case 'month':
                $where[] = ['used_at', '>=', date('Y-m-01 00:00:00')];
                break;
        }
        
        return self::where($where)->count();
    }
}
