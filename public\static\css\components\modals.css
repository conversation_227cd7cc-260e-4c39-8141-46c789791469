/**
 * 模态框组件样式
 * 统一的模态框样式系统
 */

/* 模态框遮罩 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--z-modal-backdrop);
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.modal-backdrop.show {
  opacity: 1;
}

/* 模态框容器 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
  background-color: rgba(0, 0, 0, 0.5);
  align-items: center;
  justify-content: center;
}

.modal.show {
  display: flex;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--spacing-4);
  pointer-events: none;
  transform: scale(0.9);
  transition: transform var(--transition-normal);
}

.modal.show .modal-dialog {
  transform: scale(1);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  margin: auto;
  pointer-events: auto;
  background-color: var(--white);
  background-clip: padding-box;
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  outline: 0;
  overflow: hidden;
}

/* 支持内联样式的最大宽度 */
.modal-content[style*="max-width"] {
  width: auto;
}

/* 模态框头部 */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-6);
  border-bottom: var(--border-width) solid var(--border-color);
  border-top-left-radius: var(--border-radius-lg);
  border-top-right-radius: var(--border-radius-lg);
}

.modal-title {
  margin-bottom: 0;
  line-height: var(--line-height-normal);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--gray-800);
}

.modal-close {
  padding: var(--spacing-2);
  margin: calc(var(--spacing-2) * -1) calc(var(--spacing-2) * -1) calc(var(--spacing-2) * -1) auto;
  background-color: transparent;
  border: 0;
  border-radius: var(--border-radius);
  opacity: 0.5;
  cursor: pointer;
  transition: opacity var(--transition-fast);
}

.modal-close:hover {
  opacity: 0.75;
}

.modal-close:focus {
  opacity: 1;
  outline: 0;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* 兼容性：支持 .close 类名 */
.close {
  padding: var(--spacing-2);
  margin: calc(var(--spacing-2) * -1) calc(var(--spacing-2) * -1) calc(var(--spacing-2) * -1) auto;
  background-color: transparent;
  border: 0;
  border-radius: var(--border-radius);
  opacity: 0.5;
  cursor: pointer;
  transition: opacity var(--transition-fast);
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
}

.close:hover {
  opacity: 0.75;
}

.close:focus {
  opacity: 1;
  outline: 0;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

/* 模态框主体 */
.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--spacing-6);
  overflow-y: auto;
  max-height: calc(90vh - 120px);
}

/* 模态框底部 */
.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: var(--spacing-4) var(--spacing-6);
  border-top: var(--border-width) solid var(--border-color);
  border-bottom-right-radius: var(--border-radius-lg);
  border-bottom-left-radius: var(--border-radius-lg);
  gap: var(--spacing-3);
}

.modal-footer > * {
  margin: 0;
}

/* 模态框尺寸 */
.modal-sm .modal-dialog {
  max-width: 300px;
}

.modal-lg .modal-dialog {
  max-width: 800px;
}

.modal-xl .modal-dialog {
  max-width: 1140px;
}

.modal-fullscreen .modal-dialog {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}

.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}

/* 响应式模态框 */
@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: var(--spacing-8) auto;
  }
  
  .modal-sm .modal-dialog {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .modal-lg .modal-dialog {
    max-width: 800px;
  }
  
  .modal-xl .modal-dialog {
    max-width: 1140px;
  }
}

/* 垂直居中模态框 */
.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - var(--spacing-8));
}

@media (min-width: 576px) {
  .modal-dialog-centered {
    min-height: calc(100% - var(--spacing-16));
  }
}

/* 可滚动模态框 */
.modal-dialog-scrollable {
  height: calc(100% - var(--spacing-8));
}

.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}

.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

@media (min-width: 576px) {
  .modal-dialog-scrollable {
    height: calc(100% - var(--spacing-16));
  }
}

/* 模态框动画 */
.modal.fade .modal-dialog {
  transition: transform var(--transition-normal);
  transform: translate(0, -50px);
}

.modal.fade.show .modal-dialog {
  transform: none;
}

/* 模态框主题变体 */
.modal-primary .modal-header {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.modal-primary .modal-title {
  color: var(--white);
}

.modal-primary .modal-close {
  color: var(--white);
}

.modal-success .modal-header {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.modal-success .modal-title {
  color: var(--white);
}

.modal-success .modal-close {
  color: var(--white);
}

.modal-warning .modal-header {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
}

.modal-warning .modal-title {
  color: var(--gray-800);
}

.modal-warning .modal-close {
  color: var(--gray-800);
}

.modal-danger .modal-header {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.modal-danger .modal-title {
  color: var(--white);
}

.modal-danger .modal-close {
  color: var(--white);
}

/* 确认对话框样式 */
.modal-confirm .modal-body {
  text-align: center;
  padding: var(--spacing-8) var(--spacing-6);
}

.modal-confirm .modal-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-4);
}

.modal-confirm .modal-icon.text-warning {
  color: var(--warning-color);
}

.modal-confirm .modal-icon.text-danger {
  color: var(--danger-color);
}

.modal-confirm .modal-icon.text-info {
  color: var(--info-color);
}

.modal-confirm .modal-message {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-6);
  color: var(--gray-700);
}

/* 加载模态框 */
.modal-loading .modal-body {
  text-align: center;
  padding: var(--spacing-8) var(--spacing-6);
}

.modal-loading .loading-spinner {
  font-size: 2rem;
  margin-bottom: var(--spacing-4);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modal-loading .loading-text {
  font-size: var(--font-size-base);
  color: var(--gray-600);
}
