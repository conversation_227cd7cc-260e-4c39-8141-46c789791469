<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{$settings.site_name|default='红嘴鸥教育 - 电子资料兑换系统'}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }


        .exchange-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .exchange-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .brand-title {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .system-title {
            font-size: 1.5rem;
            color: #666;
            margin-bottom: 40px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .form-control {
            border-radius: 50px;
            padding: 15px 20px;
            font-size: 16px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-redeem {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            border: none;
            border-radius: 50px;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: bold;
            color: white;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
        }

        .btn-redeem:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
            color: white;
        }

        .btn-redeem:disabled {
            background: #ccc;
            transform: none;
            box-shadow: none;
        }

        .btn-records {
            background: linear-gradient(45deg, #20bf6b, #26d0ce);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-size: 14px;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-records:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(32, 191, 107, 0.3);
            color: white;
        }

        .description {
            font-size: 14px;
            color: #888;
            line-height: 1.6;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .loading {
            display: none;
        }

        .loading.show {
            display: inline-block;
        }

        .alert {
            border-radius: 10px;
            margin-top: 20px;
        }

        .records-modal .modal-content {
            border-radius: 15px;
        }

        .records-table {
            font-size: 14px;
        }

        .records-table th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
        }

        .records-table td {
            border: none;
            border-bottom: 1px solid #eee;
            vertical-align: middle;
        }

        .success-animation {
            animation: bounceIn 0.6s ease-out;
        }

        @keyframes bounceIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
            70% {
                transform: scale(0.9);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 576px) {
            .exchange-card {
                padding: 30px 20px;
                margin: 10px;
            }

            .brand-title {
                font-size: 1.5rem;
            }

            .system-title {
                font-size: 1.2rem;
            }
        }

    </style>
</head>
<body>
    <div class="exchange-container">
        <div class="exchange-card fade-in">
            <h1 class="brand-title">{$settings.site_name|default='红嘴鸥教育'}</h1>
            <h2 class="system-title">电子资料兑换系统</h2>

            <form id="exchangeForm">
                <div class="input-group">
                    <input type="text"
                           class="form-control"
                           id="cardCode"
                           placeholder="请输入卡密"
                           maxlength="50"
                           autocomplete="off">
                </div>

                <button type="submit" class="btn btn-redeem" id="redeemBtn">
                    <span class="btn-text">立即兑换</span>
                    <span class="loading">
                        <i class="fas fa-spinner fa-spin"></i> 兑换中...
                    </span>
                </button>
            </form>

            <button type="button" class="btn btn-records" data-bs-toggle="modal" data-bs-target="#recordsModal">
                兑换记录查询
            </button>

            <div id="alertContainer"></div>

            <div class="description">
                <strong>说明：</strong>此为电子版资料兑换下载系统，兑换后不支持任何理由的退换货，
                点击兑换及代表同意此说明，有问题可以联系客服或微信 <strong>hzoedu888</strong>
            </div>
        </div>
    </div>

    <!-- 兑换记录模态框 -->
    <div class="modal fade records-modal" id="recordsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">兑换记录查询</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="recordsForm" class="mb-3">
                        <div class="input-group">
                            <input type="text"
                                   class="form-control"
                                   id="recordCardCode"
                                   placeholder="请输入要查询的卡密"
                                   maxlength="50">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 查询
                            </button>
                        </div>
                    </form>

                    <div id="recordsContainer">
                        <div class="text-center text-muted">
                            <i class="fas fa-search fa-2x mb-3"></i>
                            <p>请输入卡密查询兑换记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>


    <script>
        // API基础URL
        const API_BASE = '/api';

        // DOM元素
        const exchangeForm = document.getElementById('exchangeForm');
        const cardCodeInput = document.getElementById('cardCode');
        const redeemBtn = document.getElementById('redeemBtn');
        const alertContainer = document.getElementById('alertContainer');
        const recordsForm = document.getElementById('recordsForm');
        const recordCardCodeInput = document.getElementById('recordCardCode');
        const recordsContainer = document.getElementById('recordsContainer');

        // 兑换表单提交
        exchangeForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const cardCode = cardCodeInput.value.trim();
            if (!cardCode) {
                showAlert('请输入卡密', 'warning');
                return;
            }

            setLoading(true);

            try {
                const response = await fetch(`${API_BASE}/exchange/redeem`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ card_code: cardCode })
                });

                const result = await response.json();

                if (result.code === 200) {
                    showAlert(`兑换成功！<br>分类：${result.data.category}<br>内容：${result.data.content}`, 'success', true);
                    cardCodeInput.value = '';
                } else {
                    showAlert(result.message, 'danger');
                }
            } catch (error) {
                showAlert('网络错误，请稍后重试', 'danger');
            } finally {
                setLoading(false);
            }
        });

        // 记录查询表单提交
        recordsForm.addEventListener('submit', async (e) => {
            e.preventDefault();

            const cardCode = recordCardCodeInput.value.trim();
            if (!cardCode) {
                showRecordsResult('<div class="alert alert-warning">请输入卡密</div>');
                return;
            }

            showRecordsResult('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 查询中...</div>');

            try {
                const response = await fetch(`${API_BASE}/exchange/records?card_code=${encodeURIComponent(cardCode)}`);
                const result = await response.json();

                if (result.code === 200) {
                    displayRecords(result.data);
                } else {
                    showRecordsResult(`<div class="alert alert-warning">${result.message}</div>`);
                }
            } catch (error) {
                showRecordsResult('<div class="alert alert-danger">查询失败，请稍后重试</div>');
            }
        });

        // 设置加载状态
        function setLoading(loading) {
            const btnText = redeemBtn.querySelector('.btn-text');
            const loadingSpan = redeemBtn.querySelector('.loading');

            if (loading) {
                btnText.style.display = 'none';
                loadingSpan.classList.add('show');
                redeemBtn.disabled = true;
            } else {
                btnText.style.display = 'inline';
                loadingSpan.classList.remove('show');
                redeemBtn.disabled = false;
            }
        }

        // 显示提示信息
        function showAlert(message, type, isSuccess = false) {
            const alertClass = isSuccess ? 'success-animation' : 'fade-in';
            const alertHtml = `
                <div class="alert alert-${type} ${alertClass}" role="alert">
                    ${message}
                </div>
            `;

            alertContainer.innerHTML = alertHtml;

            // 3秒后自动隐藏
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.style.opacity = '0';
                    setTimeout(() => {
                        alertContainer.innerHTML = '';
                    }, 300);
                }
            }, 3000);
        }

        // 显示记录查询结果
        function showRecordsResult(html) {
            recordsContainer.innerHTML = html;
        }

        // 显示兑换记录
        function displayRecords(records) {
            if (!records || records.length === 0) {
                showRecordsResult('<div class="alert alert-info">暂无兑换记录</div>');
                return;
            }

            let tableHtml = `
                <div class="table-responsive">
                    <table class="table records-table">
                        <thead>
                            <tr>
                                <th>卡密</th>
                                <th>分类</th>
                                <th>兑换时间</th>
                                <th>兑换IP</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            records.forEach(record => {
                const maskedCardCode = record.card_code.length > 8
                    ? record.card_code.substring(0, 4) + '****' + record.card_code.substring(record.card_code.length - 4)
                    : record.card_code;

                tableHtml += `
                    <tr>
                        <td><code>${maskedCardCode}</code></td>
                        <td>${record.category_name}</td>
                        <td>${record.used_at}</td>
                        <td>${record.used_ip}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            showRecordsResult(tableHtml);
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 自动聚焦到输入框
            cardCodeInput.focus();

            // 输入框回车提交
            cardCodeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    exchangeForm.dispatchEvent(new Event('submit'));
                }
            });

            // 记录查询输入框回车提交
            recordCardCodeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    recordsForm.dispatchEvent(new Event('submit'));
                }
            });
        });
    </script>
</body>
</html>
