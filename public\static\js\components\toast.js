/**
 * 提示组件
 * 提供各种提示消息的显示功能
 */

const Toast = {
    /**
     * 显示成功消息
     * @param {string} message 消息内容
     */
    success(message) {
        this.show(message, 'success');
    },
    
    /**
     * 显示错误消息
     * @param {string} message 消息内容
     */
    error(message) {
        this.show(message, 'error');
    },
    
    /**
     * 显示警告消息
     * @param {string} message 消息内容
     */
    warning(message) {
        this.show(message, 'warning');
    },
    
    /**
     * 显示信息消息
     * @param {string} message 消息内容
     */
    info(message) {
        this.show(message, 'info');
    },
    
    /**
     * 显示提示消息
     * @param {string} message 消息内容
     * @param {string} type 消息类型
     */
    show(message, type = 'info') {
        // 目前使用简单的alert实现
        // 后续可以替换为更美观的toast组件
        alert(message);
    }
};

// 导出到全局
window.Toast = Toast;
